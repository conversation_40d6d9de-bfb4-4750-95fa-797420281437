#!/usr/bin/env python3
"""
Simple test script for utility tools - geocode, weather, currency, etc.

GEOCODE API Reference:
- Uses Google Maps Geocoding API
- Requires GOOGLE_MAPS_API_KEY environment variable
- Input: address (string) - The address or location to geocode
- Output: Formatted address, coordinates (lat/lng), place ID, location types, address components

GEOCODE API PARAMETER REFERENCE:
===============================

REQUIRED PARAMETERS:
- address: The address or location to geocode (string)

ENVIRONMENT VARIABLES:
- GOOGLE_MAPS_API_KEY: Your Google Maps API key (required)

RESPONSE STRUCTURE:
- formatted_address: Clean, formatted version of the address
- latitude: GPS latitude coordinate (float)
- longitude: GPS longitude coordinate (float)
- place_id: Unique Google place identifier (string)
- location_types: Array of location type classifications
- address_components: Detailed breakdown of address parts
- geometry: Full geometry information including location bounds

NOTES:
- API returns the most relevant result first
- Supports partial addresses and landmark names
- Includes comprehensive location metadata
- Handles various address formats and international locations

WEATHER API PARAMETER REFERENCE:
===============================

WEATHER FORECAST TOOL:
REQUIRED PARAMETERS:
- latitude: GPS latitude coordinate (float, -90 to 90)
- longitude: GPS longitude coordinate (float, -180 to 180)
- hours: Number of forecast hours (int, 1 to 168, default: 24)

WEATHER BY ADDRESS TOOL:
REQUIRED PARAMETERS:
- address: The address or location to get weather for (string)
- hours: Number of forecast hours (int, 1 to 168, default: 24)

ENVIRONMENT VARIABLES:
- GOOGLE_API_KEY: Your Google API key (required)
- GOOGLE_MAPS_API_KEY: Required for weather_by_address_tool (geocoding)

RESPONSE STRUCTURE:
- weather_condition: Description and type (Clear, Cloudy, Rainy, etc.)
- temperature: Current temperature and feels-like temperature
- humidity: Relative humidity percentage
- wind: Speed, direction, and gust information
- precipitation: Rain/snow probability and quantity
- visibility: Visibility distance in kilometers
- uv_index: UV radiation index
- cloud_cover: Cloud coverage percentage
- air_pressure: Atmospheric pressure
- timezone: Local timezone information

NOTES:
- API provides detailed hourly forecasts up to 7 days (168 hours)
- Includes comprehensive weather metrics and conditions
- weather_by_address_tool combines geocoding + weather lookup
- Supports international locations with timezone awareness

TIMEZONE API PARAMETER REFERENCE:
================================

TIMEZONE INFO TOOL:
REQUIRED PARAMETERS:
- latitude: GPS latitude coordinate (float, -90 to 90)
- longitude: GPS longitude coordinate (float, -180 to 180)
- timestamp: Unix timestamp for time check (int, optional, default: current time)

TIME CONVERT TOOL:
REQUIRED PARAMETERS:
- from_timezone: Source timezone ID (string, e.g., 'America/New_York')
- to_timezone: Target timezone ID (string, e.g., 'Asia/Tokyo')
- time_str: Time string in 'YYYY-MM-DD HH:MM:SS' or 'HH:MM:SS' format
- date_str: Date string in 'YYYY-MM-DD' format (optional, default: today)

TIMEZONE BY ADDRESS TOOL:
REQUIRED PARAMETERS:
- address: The address or location to get timezone info for (string)
- timestamp: Unix timestamp for time check (int, optional, default: current time)

ENVIRONMENT VARIABLES:
- GOOGLE_API_KEY: Your Google API key (required)

RESPONSE STRUCTURE:
- timezone_id: Standard timezone identifier (e.g., 'America/Los_Angeles')
- timezone_name: Human-readable timezone name
- raw_offset: Standard time offset from UTC in seconds
- dst_offset: Daylight saving time offset in seconds
- total_offset: Combined offset from UTC in seconds/hours
- local_time: Current local time in the timezone
- utc_time: Corresponding UTC time

NOTES:
- Uses Google Timezone API for accurate timezone information
- Supports daylight saving time calculations
- time_convert_tool requires pytz library for timezone conversions
- timezone_by_address_tool combines geocoding + timezone lookup
- Handles historical and future timestamps for DST calculations

CURRENCY API PARAMETER REFERENCE:
================================

CURRENCY RATES TOOL:
REQUIRED PARAMETERS:
- base_currency: Base currency code (string, e.g., 'USD', 'EUR', 'GBP', default: 'USD')

CURRENCY CONVERT TOOL:
REQUIRED PARAMETERS:
- amount: Amount to convert (float, must be > 0)
- from_currency: Source currency code (string, e.g., 'USD', 'EUR')
- to_currency: Target currency code (string, e.g., 'JPY', 'GBP')

SUPPORTED CURRENCIES TOOL:
PARAMETERS: None (returns list of all supported currencies)

ENVIRONMENT VARIABLES:
- EXCHANGE_RATE_API_KEY: Your ExchangeRate API key (optional, has hardcoded fallback)

RESPONSE STRUCTURE:
- exchange_rates: Current rates for base currency against all others
- conversion_result: Converted amount with exchange rate details
- last_update: When exchange rates were last updated
- next_update: When rates will next be updated
- supported_codes: List of all available currency codes with names

NOTES:
- Uses ExchangeRate-API.com for real-time currency data
- Supports 170+ currencies including cryptocurrencies
- Includes major currencies, regional currencies, and crypto
- Rates updated daily (some plans more frequently)
- Provides reverse conversion calculations automatically
- Includes currency strength indicators and helpful context
"""

from src.tools.geocode import geocode_tool
from src.tools.weather import weather_forecast_tool, weather_by_address_tool
from src.tools.timeconvert import timezone_info_tool, time_convert_tool, timezone_by_address_tool
from src.tools.currency import currency_rates_tool, currency_convert_tool, supported_currencies_tool
import os
import sys
import json
import time
from typing import Dict, Any

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def test_geocode_basic():
    """Basic geocoding test with a well-known address."""
    print("\n🌍 Testing Basic Geocoding...")
    result = geocode_tool.invoke({
        "address": "1600 Amphitheatre Parkway, Mountain View, CA"
    })

    # Handle potential error responses
    if isinstance(result, str) and result.startswith("Error"):
        print(f"API Error: {result}")
        return

    if "Google Maps API key not found" in result:
        print(f"Configuration Error: {result}")
        return

    try:
        print("✅ Geocoding successful!")
        print(f"Result: {result}")

        # Extract key information from the result string
        if "Coordinates:" in result:
            coords_line = [line for line in result.split(
                '\n') if 'Coordinates:' in line][0]
            print(f"📍 {coords_line}")

        if "Formatted Address:" in result:
            address_line = [line for line in result.split(
                '\n') if 'Formatted Address:' in line][0]
            print(f"🏠 {address_line}")

    except Exception as e:
        print(f"Unexpected error processing result: {e}")


def test_geocode_landmarks():
    """Test geocoding with famous landmarks."""
    print("\n🗽 Testing Landmark Geocoding...")
    landmarks = [
        "Statue of Liberty, New York",
        "Eiffel Tower, Paris",
        "Big Ben, London"
    ]

    for landmark in landmarks:
        print(f"\nGeocoding: {landmark}")
        result = geocode_tool.invoke({"address": landmark})

        if isinstance(result, str) and result.startswith("Error"):
            print(f"  ❌ Error: {result}")
            continue

        if "Google Maps API key not found" in result:
            print(f"  ⚠️  Configuration Error: API key missing")
            continue

        try:
            # Extract coordinates from result
            if "Coordinates:" in result:
                coords_line = [line for line in result.split(
                    '\n') if 'Coordinates:' in line][0]
                coordinates = coords_line.split('Coordinates: ')[1]
                print(f"  ✅ Found at: {coordinates}")
            else:
                print(f"  ⚠️  No coordinates found")

        except Exception as e:
            print(f"  ❌ Error processing: {e}")


def test_geocode_international():
    """Test geocoding with international addresses."""
    print("\n🌐 Testing International Geocoding...")
    international_addresses = [
        "Tokyo Station, Tokyo, Japan",
        "Sydney Opera House, Sydney, Australia",
        "Machu Picchu, Peru",
        "Red Square, Moscow, Russia"
    ]

    successful_geocodes = 0

    for address in international_addresses:
        print(f"\nGeocoding: {address}")
        result = geocode_tool.invoke({"address": address})

        if isinstance(result, str) and result.startswith("Error"):
            print(f"  ❌ Network/API Error")
            continue

        if "Google Maps API key not found" in result:
            print(f"  ⚠️  API key required")
            continue

        if "No geocoding results found" in result:
            print(f"  ❌ No results found")
            continue

        try:
            if "Formatted Address:" in result and "Coordinates:" in result:
                successful_geocodes += 1
                print(f"  ✅ Successfully geocoded")

                # Show location types if available
                if "Location Types:" in result:
                    types_line = [line for line in result.split(
                        '\n') if 'Location Types:' in line][0]
                    location_types = types_line.split('Location Types: ')[1]
                    print(f"  🏷️  Types: {location_types}")
            else:
                print(f"  ⚠️  Incomplete result")

        except Exception as e:
            print(f"  ❌ Error processing: {e}")

    print(
        f"\n📊 Successfully geocoded {successful_geocodes}/{len(international_addresses)} international locations")


def test_geocode_partial_addresses():
    """Test geocoding with partial or ambiguous addresses."""
    print("\n🔍 Testing Partial Address Geocoding...")
    partial_addresses = [
        "Central Park",
        "Times Square",
        "Golden Gate Bridge",
        "Hollywood Sign"
    ]

    for address in partial_addresses:
        print(f"\nGeocoding partial address: {address}")
        result = geocode_tool.invoke({"address": address})

        if isinstance(result, str) and result.startswith("Error"):
            print(f"  ❌ Error: {result}")
            continue

        if "Google Maps API key not found" in result:
            print(f"  ⚠️  API key missing")
            continue

        try:
            if "Formatted Address:" in result:
                # Extract the formatted address to see how Google interpreted it
                address_lines = [line for line in result.split(
                    '\n') if 'Formatted Address:' in line]
                if address_lines:
                    formatted = address_lines[0].split(
                        'Formatted Address: ')[1]
                    print(f"  ✅ Interpreted as: {formatted}")

                    # Show coordinates if available
                    if "Coordinates:" in result:
                        coords_lines = [line for line in result.split(
                            '\n') if 'Coordinates:' in line]
                        if coords_lines:
                            coords = coords_lines[0].split('Coordinates: ')[1]
                            print(f"  📍 Location: {coords}")
            else:
                print(f"  ❌ No valid address found")

        except Exception as e:
            print(f"  ❌ Error processing: {e}")


def test_geocode_error_handling():
    """Test geocoding error handling with invalid inputs."""
    print("\n⚠️  Testing Error Handling...")
    invalid_inputs = [
        "",  # Empty string
        "xyz123invalidaddress456",  # Nonsense address
        "🌟🌟🌟",  # Special characters only
        "a" * 200  # Very long string
    ]

    for i, invalid_address in enumerate(invalid_inputs, 1):
        display_address = invalid_address if len(
            invalid_address) < 50 else invalid_address[:47] + "..."
        print(f"\nTest {i}: Invalid input '{display_address}'")

        result = geocode_tool.invoke({"address": invalid_address})

        if "Google Maps API key not found" in result:
            print(f"  ⚠️  API key missing - skipping error tests")
            continue

        if isinstance(result, str) and ("Error" in result or "No geocoding results found" in result):
            print(f"  ✅ Properly handled invalid input")
        else:
            print(f"  ⚠️  Unexpected result: {result[:100]}...")


def test_geocode_detailed_output():
    """Test geocoding with detailed output analysis."""
    print("\n📋 Testing Detailed Output Analysis...")
    test_address = "1 Apple Park Way, Cupertino, CA"

    print(f"Analyzing geocoding result for: {test_address}")
    result = geocode_tool.invoke({"address": test_address})

    if "Google Maps API key not found" in result:
        print("  ⚠️  API key required for detailed testing")
        return

    if isinstance(result, str) and result.startswith("Error"):
        print(f"  ❌ API Error: {result}")
        return

    try:
        print("\n📊 Result Analysis:")

        # Check for all expected components
        expected_components = [
            "Formatted Address:",
            "Coordinates:",
            "Place ID:",
            "Location Types:",
            "Full result:"
        ]

        for component in expected_components:
            if component in result:
                print(f"  ✅ {component} present")
            else:
                print(f"  ❌ {component} missing")

        # Extract and display key information
        result_lines = result.split('\n')

        for line in result_lines:
            if line.strip().startswith("Formatted Address:"):
                print(f"\n🏠 Address: {line.split('Formatted Address: ')[1]}")
            elif line.strip().startswith("Coordinates:"):
                coords = line.split('Coordinates: ')[1]
                lat, lng = coords.split(', ')
                print(f"📍 Latitude: {lat}")
                print(f"📍 Longitude: {lng}")
            elif line.strip().startswith("Place ID:"):
                place_id = line.split('Place ID: ')[1]
                print(f"🆔 Place ID: {place_id}")
            elif line.strip().startswith("Location Types:"):
                types = line.split('Location Types: ')[1]
                print(f"🏷️  Types: {types}")

    except Exception as e:
        print(f"❌ Error analyzing result: {e}")


def test_weather_basic():
    """Basic weather forecast test with known coordinates."""
    print("\n🌤️  Testing Basic Weather Forecast...")
    # Mountain View, CA (Google headquarters)
    result = weather_forecast_tool.invoke({
        "latitude": 37.4220,
        "longitude": -122.0841,
        "hours": 3
    })

    # Handle potential error responses
    if isinstance(result, str) and result.startswith("Error"):
        print(f"API Error: {result}")
        return

    if "Google Weather API key not found" in result:
        print(f"Configuration Error: {result}")
        return

    try:
        print("✅ Weather forecast successful!")
        print(f"Result preview: {result[:300]}...")

        # Extract key information from the result string
        if "CURRENT CONDITIONS SUMMARY:" in result:
            summary_start = result.find("CURRENT CONDITIONS SUMMARY:")
            summary_section = result[summary_start:summary_start+200]
            print(f"📊 {summary_section}")

        if "Timezone:" in result:
            timezone_line = [line for line in result.split(
                '\n') if 'Timezone:' in line][0]
            print(f"🕐 {timezone_line}")

    except Exception as e:
        print(f"Unexpected error processing result: {e}")


def test_weather_by_address():
    """Test weather forecast by address (combines geocoding + weather)."""
    print("\n🏠 Testing Weather by Address...")
    addresses = [
        "Times Square, New York",
        "Golden Gate Bridge, San Francisco",
        "1600 Amphitheatre Parkway, Mountain View, CA"
    ]

    for address in addresses:
        print(f"\nGetting weather for: {address}")
        result = weather_by_address_tool.invoke({
            "address": address,
            "hours": 2
        })

        if isinstance(result, str) and result.startswith("Error"):
            print(f"  ❌ Error: {result}")
            continue

        if "API key not found" in result:
            print(f"  ⚠️  Configuration Error: API key missing")
            continue

        try:
            # Extract key weather information
            if "CURRENT CONDITIONS SUMMARY:" in result:
                lines = result.split('\n')
                for line in lines:
                    if "Temperature:" in line:
                        print(f"  🌡️  {line.strip()}")
                    elif "Condition:" in line and "CURRENT CONDITIONS" not in line:
                        print(f"  ☁️  {line.strip()}")
                        break
            else:
                print(f"  ⚠️  Weather data format unexpected")

        except Exception as e:
            print(f"  ❌ Error processing: {e}")


def test_weather_international():
    """Test weather forecast for international locations."""
    print("\n🌍 Testing International Weather Forecasts...")
    international_locations = [
        {"name": "Tokyo, Japan", "lat": 35.6762, "lng": 139.6503},
        {"name": "London, UK", "lat": 51.5074, "lng": -0.1278},
        {"name": "Sydney, Australia", "lat": -33.8688, "lng": 151.2093},
        {"name": "Moscow, Russia", "lat": 55.7558, "lng": 37.6176}
    ]

    successful_forecasts = 0

    for location in international_locations:
        print(f"\nGetting weather for: {location['name']}")
        result = weather_forecast_tool.invoke({
            "latitude": location['lat'],
            "longitude": location['lng'],
            "hours": 20
        })

        if isinstance(result, str) and result.startswith("Error"):
            print(f"  ❌ Network/API Error")
            continue

        if "Google Weather API key not found" in result:
            print(f"  ⚠️  API key required")
            continue

        if "No weather forecast data available" in result:
            print(f"  ❌ No weather data found")
            continue

        try:
            if "Weather Forecast for coordinates" in result and "CURRENT CONDITIONS SUMMARY:" in result:
                successful_forecasts += 1
                print(f"  ✅ Successfully retrieved weather forecast")

                # Show timezone if available
                if "Timezone:" in result:
                    timezone_line = [line for line in result.split(
                        '\n') if 'Timezone:' in line][0]
                    timezone = timezone_line.split('Timezone: ')[1]
                    print(f"  🕐 Timezone: {timezone}")

                # Show temperature
                if "Temperature:" in result:
                    temp_lines = [line for line in result.split(
                        '\n') if 'Temperature:' in line and 'CURRENT' not in line]
                    if temp_lines:
                        temp_line = temp_lines[0]
                        temp = temp_line.split('Temperature: ')[
                            1] if 'Temperature: ' in temp_line else temp_line
                        print(f"  🌡️  Current: {temp}")
            else:
                print(f"  ⚠️  Incomplete weather result")

        except Exception as e:
            print(f"  ❌ Error processing: {e}")

    print(
        f"\n📊 Successfully retrieved weather for {successful_forecasts}/{len(international_locations)} international locations")


def test_weather_error_handling():
    """Test weather forecast error handling with invalid inputs."""
    print("\n⚠️  Testing Weather Error Handling...")

    # Test invalid coordinates
    invalid_coords = [
        {"lat": 91.0, "lng": 0.0, "desc": "Invalid latitude (>90)"},
        {"lat": 0.0, "lng": 181.0, "desc": "Invalid longitude (>180)"},
        {"lat": -91.0, "lng": 0.0, "desc": "Invalid latitude (<-90)"},
        {"lat": 0.0, "lng": -181.0, "desc": "Invalid longitude (<-180)"},
    ]

    for coord in invalid_coords:
        print(f"\nTest: {coord['desc']}")
        result = weather_forecast_tool.invoke({
            "latitude": coord['lat'],
            "longitude": coord['lng'],
            "hours": 1
        })

        if "Google Weather API key not found" in result:
            print(f"  ⚠️  API key missing - skipping coordinate tests")
            continue

        if isinstance(result, str) and ("Invalid" in result or "Error" in result):
            print(f"  ✅ Properly handled invalid coordinates")
        else:
            print(f"  ⚠️  Unexpected result: {result[:100]}...")

    # Test invalid hours
    print(f"\nTest: Invalid hours parameter")
    result = weather_forecast_tool.invoke({
        "latitude": 37.4220,
        "longitude": -122.0841,
        "hours": 200  # Max is 168
    })

    if "Google Weather API key not found" in result:
        print(f"  ⚠️  API key missing - skipping hours test")
    elif isinstance(result, str) and ("Invalid hours" in result or "Error" in result):
        print(f"  ✅ Properly handled invalid hours parameter")
    else:
        print(f"  ⚠️  Unexpected result for invalid hours")


def test_weather_detailed_output():
    """Test weather forecast with detailed output analysis."""
    print("\n📋 Testing Detailed Weather Output Analysis...")
    test_coords = {"lat": 37.7749, "lng": -122.4194}  # San Francisco

    print(
        f"Analyzing weather forecast for San Francisco: {test_coords['lat']}, {test_coords['lng']}")
    result = weather_forecast_tool.invoke({
        "latitude": test_coords['lat'],
        "longitude": test_coords['lng'],
        "hours": 5
    })

    if "Google Weather API key not found" in result:
        print("  ⚠️  API key required for detailed testing")
        return

    if isinstance(result, str) and result.startswith("Error"):
        print(f"  ❌ API Error: {result}")
        return

    try:
        print("\n📊 Weather Result Analysis:")

        # Check for all expected components
        expected_components = [
            "Weather Forecast for coordinates",
            "Timezone:",
            "Forecast Hours:",
            "CURRENT CONDITIONS SUMMARY:",
            "Temperature:",
            "Condition:",
            "Humidity:",
            "Wind Speed:"
        ]

        for component in expected_components:
            if component in result:
                print(f"  ✅ {component} present")
            else:
                print(f"  ❌ {component} missing")

        # Extract and display key weather information
        result_lines = result.split('\n')

        for line in result_lines:
            if line.strip().startswith("Timezone:"):
                timezone = line.split('Timezone: ')[
                    1] if 'Timezone: ' in line else line
                print(f"\n🕐 Timezone: {timezone}")
            elif line.strip().startswith("Forecast Hours:"):
                hours = line.split('Forecast Hours: ')[
                    1] if 'Forecast Hours: ' in line else line
                print(f"📅 Forecast Hours: {hours}")
            elif "Temperature:" in line and "CURRENT CONDITIONS" not in line:
                # Get the first temperature line from current conditions
                if "CURRENT CONDITIONS" in result:
                    current_section = result.split(
                        "CURRENT CONDITIONS SUMMARY:")[1]
                    current_lines = current_section.split('\n')
                    for current_line in current_lines:
                        if "Temperature:" in current_line:
                            temp = current_line.split('Temperature: ')[
                                1] if 'Temperature: ' in current_line else current_line
                            print(f"🌡️  Current Temperature: {temp}")
                            break
                break

    except Exception as e:
        print(f"❌ Error analyzing weather result: {e}")


def test_timezone_basic():
    """Basic timezone lookup test with known coordinates."""
    print("\n🕐 Testing Basic Timezone Lookup...")
    # Mountain View, CA (Google headquarters)
    result = timezone_info_tool.invoke({
        "latitude": 37.4220,
        "longitude": -122.0841
    })

    # Handle potential error responses
    if isinstance(result, str) and result.startswith("Error"):
        print(f"API Error: {result}")
        return

    if "Google API key not found" in result:
        print(f"Configuration Error: {result}")
        return

    try:
        print("✅ Timezone lookup successful!")
        print(f"Result preview: {result[:300]}...")

        # Extract key information from the result string
        if "Timezone ID:" in result:
            timezone_lines = [line for line in result.split(
                '\n') if 'Timezone ID:' in line]
            if timezone_lines:
                print(f"🌍 {timezone_lines[0]}")

        if "UTC Offset:" in result:
            offset_lines = [line for line in result.split(
                '\n') if 'UTC Offset:' in line]
            if offset_lines:
                print(f"⏰ {offset_lines[0]}")

    except Exception as e:
        print(f"Unexpected error processing result: {e}")


def test_timezone_by_address():
    """Test timezone lookup by address (combines geocoding + timezone)."""
    print("\n🏠 Testing Timezone by Address...")
    addresses = [
        "Times Square, New York",
        "Big Ben, London",
        "Tokyo Station, Japan"
    ]

    for address in addresses:
        print(f"\nGetting timezone for: {address}")
        result = timezone_by_address_tool.invoke({
            "address": address
        })

        if isinstance(result, str) and result.startswith("Error"):
            print(f"  ❌ Error: {result}")
            continue

        if "API key not found" in result:
            print(f"  ⚠️  Configuration Error: API key missing")
            continue

        try:
            # Extract key timezone information
            if "Timezone ID:" in result:
                timezone_lines = [line for line in result.split(
                    '\n') if 'Timezone ID:' in line]
                if timezone_lines:
                    timezone_id = timezone_lines[0].split('Timezone ID: ')[1]
                    print(f"  🌍 Timezone: {timezone_id}")

            if "Local Time:" in result:
                time_lines = [line for line in result.split(
                    '\n') if 'Local Time:' in line]
                if time_lines:
                    local_time = time_lines[0].split('Local Time: ')[1]
                    print(f"  🕐 Local Time: {local_time}")

        except Exception as e:
            print(f"  ❌ Error processing: {e}")


def test_time_conversion():
    """Test time conversion between different timezones."""
    print("\n🔄 Testing Time Conversion...")

    # Test cases for time conversion
    conversion_tests = [
        {
            "from": "America/New_York",
            "to": "Europe/London",
            "time": "14:30:00",
            "desc": "NYC to London"
        },
        {
            "from": "America/Los_Angeles",
            "to": "Asia/Tokyo",
            "time": "09:00:00",
            "desc": "LA to Tokyo"
        },
        {
            "from": "Europe/Paris",
            "to": "Australia/Sydney",
            "time": "20:15:00",
            "desc": "Paris to Sydney"
        }
    ]

    for test in conversion_tests:
        print(f"\nTesting {test['desc']}: {test['time']}")
        result = time_convert_tool.invoke({
            "from_timezone": test['from'],
            "to_timezone": test['to'],
            "time_str": test['time']
        })

        if isinstance(result, str) and result.startswith("Error"):
            print(f"  ❌ Error: {result}")
            continue

        if "pytz library is not installed" in result:
            print(f"  ⚠️  Missing dependency: {result}")
            continue

        try:
            # Extract conversion information
            if "Time Conversion:" in result:
                print(f"  ✅ Conversion successful")

                # Show from and to times
                lines = result.split('\n')
                for line in lines:
                    if line.startswith("From:"):
                        print(f"  📤 {line}")
                    elif line.startswith("To:"):
                        print(f"  📥 {line}")
                        break

        except Exception as e:
            print(f"  ❌ Error processing: {e}")


def test_timezone_international():
    """Test timezone lookup for international locations."""
    print("\n🌍 Testing International Timezone Lookups...")
    international_locations = [
        {"name": "Tokyo, Japan", "lat": 35.6762, "lng": 139.6503},
        {"name": "London, UK", "lat": 51.5074, "lng": -0.1278},
        {"name": "Sydney, Australia", "lat": -33.8688, "lng": 151.2093},
        {"name": "Dubai, UAE", "lat": 25.2048, "lng": 55.2708}
    ]

    successful_lookups = 0

    for location in international_locations:
        print(f"\nGetting timezone for: {location['name']}")
        result = timezone_info_tool.invoke({
            "latitude": location['lat'],
            "longitude": location['lng']
        })

        if isinstance(result, str) and result.startswith("Error"):
            print(f"  ❌ Network/API Error")
            continue

        if "Google API key not found" in result:
            print(f"  ⚠️  API key required")
            continue

        try:
            if "Timezone Information for coordinates" in result and "Timezone ID:" in result:
                successful_lookups += 1
                print(f"  ✅ Successfully retrieved timezone info")

                # Show timezone ID and offset
                if "Timezone ID:" in result:
                    timezone_lines = [line for line in result.split(
                        '\n') if 'Timezone ID:' in line]
                    if timezone_lines:
                        timezone_id = timezone_lines[0].split(
                            'Timezone ID: ')[1]
                        print(f"  🌍 ID: {timezone_id}")

                if "UTC Offset:" in result:
                    offset_lines = [line for line in result.split(
                        '\n') if 'UTC Offset:' in line]
                    if offset_lines:
                        offset = offset_lines[0].split('UTC Offset: ')[1]
                        print(f"  ⏰ Offset: {offset}")
            else:
                print(f"  ⚠️  Incomplete timezone result")

        except Exception as e:
            print(f"  ❌ Error processing: {e}")

    print(
        f"\n📊 Successfully retrieved timezone info for {successful_lookups}/{len(international_locations)} international locations")


def test_timezone_error_handling():
    """Test timezone tools error handling with invalid inputs."""
    print("\n⚠️  Testing Timezone Error Handling...")

    # Test invalid coordinates for timezone_info_tool
    invalid_coords = [
        {"lat": 91.0, "lng": 0.0, "desc": "Invalid latitude (>90)"},
        {"lat": 0.0, "lng": 181.0, "desc": "Invalid longitude (>180)"}
    ]

    for coord in invalid_coords:
        print(f"\nTest timezone lookup: {coord['desc']}")
        result = timezone_info_tool.invoke({
            "latitude": coord['lat'],
            "longitude": coord['lng']
        })

        if "Google API key not found" in result:
            print(f"  ⚠️  API key missing - skipping coordinate tests")
            continue

        if isinstance(result, str) and ("Invalid" in result or "Error" in result):
            print(f"  ✅ Properly handled invalid coordinates")
        else:
            print(f"  ⚠️  Unexpected result: {result[:100]}...")

    # Test invalid timezone IDs for time_convert_tool
    print(f"\nTest time conversion: Invalid timezone IDs")
    result = time_convert_tool.invoke({
        "from_timezone": "Invalid/Timezone",
        "to_timezone": "America/New_York",
        "time_str": "12:00:00"
    })

    if "pytz library is not installed" in result:
        print(f"  ⚠️  pytz library missing - skipping timezone conversion tests")
    elif isinstance(result, str) and ("Unknown timezone" in result or "Error" in result):
        print(f"  ✅ Properly handled invalid timezone ID")
    else:
        print(f"  ⚠️  Unexpected result for invalid timezone")

    # Test invalid time format
    print(f"\nTest time conversion: Invalid time format")
    result = time_convert_tool.invoke({
        "from_timezone": "America/New_York",
        "to_timezone": "Europe/London",
        "time_str": "25:99:99"  # Invalid time
    })

    if "pytz library is not installed" in result:
        print(f"  ⚠️  pytz library missing")
    elif isinstance(result, str) and ("Invalid time format" in result or "Error" in result):
        print(f"  ✅ Properly handled invalid time format")
    else:
        print(f"  ⚠️  Unexpected result for invalid time format")


def test_timezone_with_timestamp():
    """Test timezone lookup with specific timestamps (for DST testing)."""
    print("\n📅 Testing Timezone with Specific Timestamps...")

    # Test coordinates for New York (has DST)
    ny_coords = {"lat": 40.7128, "lng": -74.0060}

    # Test summer time (DST active) vs winter time
    test_timestamps = [
        {"ts": int(time.mktime((2024, 7, 15, 12, 0, 0, 0, 0, 0))),
         "desc": "Summer (DST)"},
        {"ts": int(time.mktime((2024, 1, 15, 12, 0, 0, 0, 0, 0))),
         "desc": "Winter (Standard)"}
    ]

    for test in test_timestamps:
        print(f"\nTesting NYC timezone: {test['desc']}")
        result = timezone_info_tool.invoke({
            "latitude": ny_coords['lat'],
            "longitude": ny_coords['lng'],
            "timestamp": test['ts']
        })

        if isinstance(result, str) and result.startswith("Error"):
            print(f"  ❌ Error: {result}")
            continue

        if "Google API key not found" in result:
            print(f"  ⚠️  API key required")
            continue

        try:
            if "Timezone Information" in result:
                print(f"  ✅ Timestamp-specific lookup successful")

                # Show DST information
                if "DST Offset:" in result:
                    dst_lines = [line for line in result.split(
                        '\n') if 'DST Offset:' in line]
                    if dst_lines:
                        dst_offset = dst_lines[0].split('DST Offset: ')[1]
                        print(f"  🌞 {dst_lines[0]}")

                if "UTC Offset:" in result:
                    offset_lines = [line for line in result.split(
                        '\n') if 'UTC Offset:' in line]
                    if offset_lines:
                        print(f"  ⏰ {offset_lines[0]}")

        except Exception as e:
            print(f"  ❌ Error processing: {e}")


def test_currency_rates():
    """Test getting exchange rates for different base currencies."""
    print("\n💱 Testing Currency Exchange Rates...")

    # Test different base currencies
    base_currencies = ["USD", "EUR", "GBP", "JPY"]

    for base_currency in base_currencies:
        print(f"\nGetting rates for: {base_currency}")
        result = currency_rates_tool.invoke({
            "base_currency": base_currency
        })

        if isinstance(result, str) and result.startswith("Error"):
            print(f"  ❌ Error: {result}")
            continue

        try:
            # Extract key rate information
            if f"Exchange Rates for {base_currency}" in result:
                print(f"  ✅ Successfully retrieved rates for {base_currency}")

                # Show last update time
                if "Last Updated:" in result:
                    update_lines = [line for line in result.split(
                        '\n') if 'Last Updated:' in line]
                    if update_lines:
                        print(f"  🕐 {update_lines[0]}")

                # Show total currencies count
                if "Total Currencies Available:" in result:
                    total_lines = [line for line in result.split(
                        '\n') if 'Total Currencies Available:' in line]
                    if total_lines:
                        print(f"  📊 {total_lines[0]}")

                # Show a sample rate
                if "MAJOR CURRENCY RATES:" in result:
                    lines = result.split('\n')
                    major_section = False
                    for line in lines:
                        if "MAJOR CURRENCY RATES:" in line:
                            major_section = True
                            continue
                        if major_section and line.strip().startswith("1 " + base_currency):
                            print(f"  💰 Sample rate: {line.strip()}")
                            break
            else:
                print(f"  ⚠️  Unexpected rate data format")

        except Exception as e:
            print(f"  ❌ Error processing: {e}")


def test_currency_conversion():
    """Test currency conversion between different currencies."""
    print("\n🔄 Testing Currency Conversion...")

    # Test cases for currency conversion
    conversion_tests = [
        {
            "amount": 100,
            "from": "USD",
            "to": "EUR",
            "desc": "USD to EUR"
        },
        {
            "amount": 1000,
            "from": "JPY",
            "to": "USD",
            "desc": "JPY to USD"
        },
        {
            "amount": 50.75,
            "from": "GBP",
            "to": "CAD",
            "desc": "GBP to CAD"
        },
        {
            "amount": 250,
            "from": "EUR",
            "to": "CNY",
            "desc": "EUR to CNY"
        }
    ]

    successful_conversions = 0

    for test in conversion_tests:
        print(
            f"\nTesting {test['desc']}: {test['amount']} {test['from']} → {test['to']}")
        result = currency_convert_tool.invoke({
            "amount": test['amount'],
            "from_currency": test['from'],
            "to_currency": test['to']
        })

        if isinstance(result, str) and result.startswith("Error"):
            print(f"  ❌ Error: {result}")
            continue

        try:
            # Extract conversion information
            if "Currency Conversion:" in result:
                successful_conversions += 1
                print(f"  ✅ Conversion successful")

                # Show exchange rate
                if "Exchange Rate:" in result:
                    rate_lines = [line for line in result.split(
                        '\n') if 'Exchange Rate:' in line]
                    if rate_lines:
                        print(f"  📈 {rate_lines[0]}")

                # Show converted amount
                if "Converted Amount:" in result:
                    amount_lines = [line for line in result.split(
                        '\n') if 'Converted Amount:' in line]
                    if amount_lines:
                        print(f"  💰 {amount_lines[0]}")

                # Show currency strength indicator
                strength_lines = [
                    line for line in result.split('\n') if '💡' in line]
                if strength_lines:
                    print(f"  {strength_lines[0]}")

        except Exception as e:
            print(f"  ❌ Error processing: {e}")

    print(
        f"\n📊 Successfully converted {successful_conversions}/{len(conversion_tests)} currency pairs")


def test_supported_currencies():
    """Test getting list of supported currencies."""
    print("\n📋 Testing Supported Currencies List...")

    result = supported_currencies_tool.invoke({})

    if isinstance(result, str) and result.startswith("Error"):
        print(f"❌ Error: {result}")
        return

    try:
        print("✅ Successfully retrieved supported currencies")

        # Extract key information
        if "Total Currencies:" in result:
            total_lines = [line for line in result.split(
                '\n') if 'Total Currencies:' in line]
            if total_lines:
                total_count = total_lines[0].split('Total Currencies: ')[1]
                print(f"📊 Total currencies supported: {total_count}")

        # Show major currencies section
        if "MAJOR CURRENCIES:" in result:
            print("✅ Major currencies section found")
            lines = result.split('\n')
            major_section = False
            major_count = 0
            for line in lines:
                if "MAJOR CURRENCIES:" in line:
                    major_section = True
                    continue
                elif major_section and line.strip().startswith('  ') and ' - ' in line:
                    major_count += 1
                    if major_count <= 3:  # Show first 3 major currencies
                        print(f"  💰 {line.strip()}")
                elif major_section and not line.strip().startswith('  '):
                    break
            print(
                f"  ... and {major_count - 3} more major currencies" if major_count > 3 else "")

        # Show crypto currencies if available
        if "CRYPTOCURRENCIES:" in result:
            print("✅ Cryptocurrency support available")
            lines = result.split('\n')
            crypto_section = False
            crypto_count = 0
            for line in lines:
                if "CRYPTOCURRENCIES:" in line:
                    crypto_section = True
                    continue
                elif crypto_section and line.strip().startswith('  ') and ' - ' in line:
                    crypto_count += 1
                    if crypto_count <= 2:  # Show first 2 cryptos
                        print(f"  ₿ {line.strip()}")
                elif crypto_section and not line.strip().startswith('  '):
                    break
            if crypto_count > 2:
                print(f"  ... and {crypto_count - 2} more cryptocurrencies")

        # Show usage examples section
        if "USAGE EXAMPLES:" in result:
            print("✅ Usage examples included")

    except Exception as e:
        print(f"❌ Error processing supported currencies: {e}")


def test_currency_error_handling():
    """Test currency tools error handling with invalid inputs."""
    print("\n⚠️  Testing Currency Error Handling...")

    # Test invalid currency codes
    print(f"\nTest: Invalid currency codes")
    result = currency_convert_tool.invoke({
        "amount": 100,
        "from_currency": "INVALID",
        "to_currency": "USD"
    })

    if isinstance(result, str) and ("Invalid currency codes" in result or "Error" in result or "unsupported-code" in result):
        print(f"  ✅ Properly handled invalid currency code")
    else:
        print(f"  ⚠️  Unexpected result: {result[:100]}...")

    # Test invalid amount
    print(f"\nTest: Invalid amount (negative)")
    result = currency_convert_tool.invoke({
        "amount": -50,
        "from_currency": "USD",
        "to_currency": "EUR"
    })

    if isinstance(result, str) and ("Invalid amount" in result or "Error" in result):
        print(f"  ✅ Properly handled invalid amount")
    else:
        print(f"  ⚠️  Unexpected result for invalid amount")

    # Test invalid base currency for rates
    print(f"\nTest: Invalid base currency for rates")
    result = currency_rates_tool.invoke({
        "base_currency": "XYZ"
    })

    if isinstance(result, str) and ("Invalid currency code" in result or "Error" in result or "unsupported-code" in result):
        print(f"  ✅ Properly handled invalid base currency")
    else:
        print(f"  ⚠️  Unexpected result for invalid base currency")


def test_currency_real_world():
    """Test currency tools with real-world conversion scenarios."""
    print("\n🌍 Testing Real-World Currency Scenarios...")

    # Real-world conversion scenarios
    scenarios = [
        {
            "desc": "Travel: USD to EUR (Europe trip)",
            "amount": 1500,
            "from": "USD",
            "to": "EUR"
        },
        {
            "desc": "Business: GBP to JPY (UK-Japan trade)",
            "amount": 10000,
            "from": "GBP",
            "to": "JPY"
        },
        {
            "desc": "Remittance: USD to INR (sending money to India)",
            "amount": 500,
            "from": "USD",
            "to": "INR"
        },
        {
            "desc": "Investment: EUR to CNY (European-Chinese investment)",
            "amount": 25000,
            "from": "EUR",
            "to": "CNY"
        }
    ]

    successful_scenarios = 0

    for scenario in scenarios:
        print(f"\n{scenario['desc']}")
        print(
            f"  Converting: {scenario['amount']:,} {scenario['from']} → {scenario['to']}")

        result = currency_convert_tool.invoke({
            "amount": scenario['amount'],
            "from_currency": scenario['from'],
            "to_currency": scenario['to']
        })

        if isinstance(result, str) and result.startswith("Error"):
            print(f"  ❌ Error: {result}")
            continue

        try:
            if "Currency Conversion:" in result and "Converted Amount:" in result:
                successful_scenarios += 1
                print(f"  ✅ Conversion successful")

                # Extract and show the converted amount
                amount_lines = [line for line in result.split(
                    '\n') if 'Converted Amount:' in line]
                if amount_lines:
                    converted = amount_lines[0].split('Converted Amount: ')[1]
                    print(f"  💰 Result: {converted}")

                # Show the exchange rate for context
                rate_lines = [line for line in result.split(
                    '\n') if 'Exchange Rate:' in line]
                if rate_lines:
                    rate = rate_lines[0].split('Exchange Rate: ')[1]
                    print(f"  📈 Rate: {rate}")
            else:
                print(f"  ⚠️  Incomplete conversion result")

        except Exception as e:
            print(f"  ❌ Error processing: {e}")

    print(
        f"\n📊 Successfully processed {successful_scenarios}/{len(scenarios)} real-world scenarios")


def test_currency_rate_comparison():
    """Test comparing exchange rates across different base currencies."""
    print("\n📊 Testing Currency Rate Comparisons...")

    # Test getting rates for major currencies to compare
    major_bases = ["USD", "EUR", "GBP"]
    target_currency = "JPY"  # Compare how much JPY each base currency gets

    print(
        f"Comparing how much {target_currency} you get for 1 unit of each major currency:")

    rates_data = {}

    for base in major_bases:
        print(f"\nGetting {base} to {target_currency} rate...")
        result = currency_convert_tool.invoke({
            "amount": 1,
            "from_currency": base,
            "to_currency": target_currency
        })

        if isinstance(result, str) and result.startswith("Error"):
            print(f"  ❌ Error: {result}")
            continue

        try:
            if "Converted Amount:" in result:
                amount_lines = [line for line in result.split(
                    '\n') if 'Converted Amount:' in line]
                if amount_lines:
                    # Extract just the number from "Converted Amount: X.XX JPY"
                    amount_text = amount_lines[0].split(
                        'Converted Amount: ')[1]
                    amount_value = amount_text.split()[0].replace(',', '')
                    rates_data[base] = float(amount_value)
                    print(f"  ✅ 1 {base} = {amount_value} {target_currency}")

        except Exception as e:
            print(f"  ❌ Error processing: {e}")

    # Show comparison if we got multiple rates
    if len(rates_data) >= 2:
        print(f"\n📈 Currency Strength Comparison (vs {target_currency}):")
        sorted_rates = sorted(rates_data.items(),
                              key=lambda x: x[1], reverse=True)
        for i, (currency, rate) in enumerate(sorted_rates, 1):
            strength = "💪 Strongest" if i == 1 else "💰 Strong" if i == 2 else "📉 Weaker"
            print(f"  {i}. {currency}: {rate:,.2f} {target_currency} {strength}")


def check_api_configuration():
    """Check if the required API keys are configured."""
    print("🔧 Checking API Configuration...")

    google_maps_key = os.getenv("GOOGLE_API_KEY")
    currency_api_key = os.getenv("EXCHANGE_RATE_API_KEY")

    google_configured = False
    currency_configured = False

    if google_maps_key:
        print("✅ GOOGLE_API_KEY is configured")
        print(f"   Key length: {len(google_maps_key)} characters")
        print("   Used for: Geocoding, Weather, and Timezone APIs")
        google_configured = True
    else:
        print("❌ GOOGLE_API_KEY is not set")
        print("   Set it with: export GOOGLE_API_KEY='your_api_key_here'")
        print("   Affects: Geocoding, Weather, and Timezone tests")

    if currency_api_key:
        print("✅ EXCHANGE_RATE_API_KEY is configured")
        print(f"   Key length: {len(currency_api_key)} characters")
        currency_configured = True
    else:
        print("✅ EXCHANGE_RATE_API_KEY not set (using hardcoded fallback)")
        print("   Currency tests will use built-in API key")
        currency_configured = True  # Always works due to fallback

    return google_configured, currency_configured


if __name__ == "__main__":
    print("Utility Tools Test Suite")
    print("=" * 30)

    # Check configuration first
    google_configured, currency_configured = check_api_configuration()

    if not google_configured:
        print("\n⚠️  WARNING: Geocoding, Weather, and Timezone tests will fail without GOOGLE_API_KEY")

    if not currency_configured:
        print("\n⚠️  WARNING: Currency tests will fail (but this should not happen due to fallback)")

    if google_configured and currency_configured:
        print("\n✅ All API keys configured - full test suite will run")

    try:
        # Geocoding Tests
        print("\n🧪 Running Geocoding Tests...")
        test_geocode_basic()
        test_geocode_landmarks()
        test_geocode_international()
        test_geocode_partial_addresses()
        test_geocode_error_handling()
        test_geocode_detailed_output()
        print("✅ All geocoding tests completed!")

        # Weather Tests
        print("\n🌤️  Running Weather Tests...")
        test_weather_basic()
        test_weather_by_address()
        test_weather_international()
        test_weather_error_handling()
        test_weather_detailed_output()
        print("✅ All weather tests completed!")

        # Timezone Tests
        print("\n🕐 Running Timezone Tests...")
        test_timezone_basic()
        test_timezone_by_address()
        test_time_conversion()
        test_timezone_international()
        test_timezone_error_handling()
        test_timezone_with_timestamp()
        print("✅ All timezone tests completed!")

        # Currency Tests
        print("\n💱 Running Currency Tests...")
        test_currency_rates()
        test_currency_conversion()
        test_supported_currencies()
        test_currency_error_handling()
        test_currency_real_world()
        test_currency_rate_comparison()
        print("✅ All currency tests completed!")

        # Final Summary
        print("\n" + "=" * 50)
        print("📋 UTILITY TOOLS TEST SUITE SUMMARY:")

        if google_configured and currency_configured:
            print("🎉 Complete utility tools test suite executed successfully")
            print("✅ Geocoding: GOOGLE_API_KEY configured")
            print("✅ Weather: GOOGLE_API_KEY configured")
            print("✅ Timezone: GOOGLE_API_KEY configured")
            print("✅ Currency: EXCHANGE_RATE_API_KEY configured (or using fallback)")
        elif google_configured:
            print("⚠️  Partial test suite executed (Google APIs only)")
            print("✅ Geocoding: GOOGLE_API_KEY configured")
            print("✅ Weather: GOOGLE_API_KEY configured")
            print("✅ Timezone: GOOGLE_API_KEY configured")
            print("✅ Currency: Using fallback API key")
        else:
            print("⚠️  Limited test suite executed")
            print("❌ Geocoding: GOOGLE_API_KEY missing")
            print("❌ Weather: GOOGLE_API_KEY missing")
            print("❌ Timezone: GOOGLE_API_KEY missing")
            print("✅ Currency: Using fallback API key")

        print("\n🔧 TOOL CATEGORIES TESTED:")
        print("  📍 Geocoding: Address → Coordinates conversion")
        print("  🌤️  Weather: Location-based weather forecasting")
        print("  🕐 Timezone: Timezone info and time conversion")
        print("  💱 Currency: Exchange rates and currency conversion")

        print("\n📊 TEST COVERAGE:")
        print("  • Basic functionality tests")
        print("  • International location/currency tests")
        print("  • Error handling and validation")
        print("  • Real-world usage scenarios")
        print("  • API integration and data parsing")

    except Exception as e:
        print(f"\n❌ Test suite error: {e}")
