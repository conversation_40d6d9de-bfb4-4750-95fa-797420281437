FROM ghcr.io/astral-sh/uv:python3.12-bookworm-slim

# Install uv.
COPY --from=ghcr.io/astral-sh/uv:latest /uv /bin/uv

WORKDIR /app


# Copy the application into the container.
COPY . /app

# Copy config example as default config if conf.yaml doesn't exist
RUN if [ ! -f conf.yaml ]; then cp conf.yaml.example conf.yaml; fi

# Allow runtime configuration via environment variables
ENV BASIC_MODEL_API_KEY=""
ENV BASIC_MODEL_BASE_URL="https://ark.cn-beijing.volces.com/api/v3"
ENV BASIC_MODEL_MODEL="doubao-1-5-pro-32k-250115"

# Search API configuration
ENV SEARCH_API="serper"
ENV SERPER_API_KEY=""
ENV TAVILY_API_KEY=""
ENV BRAVE_SEARCH_API_KEY=""

EXPOSE 8000

# Run the application.
CMD ["uv", "run", "python", "server.py", "--host", "0.0.0.0", "--port", "8000"]
