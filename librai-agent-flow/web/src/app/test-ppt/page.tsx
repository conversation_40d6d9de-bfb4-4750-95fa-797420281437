"use client";

import React, { useState } from "react";
import PPTCard from "~/components/ppt-card";
import { Button } from "~/components/ui/button";

const testReportContent = `# 人工智能在医疗领域的应用研究报告

## 概述

人工智能（AI）技术正在医疗健康领域掀起一场革命。从诊断辅助到药物发现，AI正在改变传统医疗实践的方方面面。

## 主要应用领域

### 医学影像诊断
- **放射科AI**：能够快速准确地识别X光、CT、MRI中的异常
- **病理诊断**：协助病理医生分析组织切片
- **眼科筛查**：糖尿病视网膜病变的早期检测

### 药物研发
- **分子设计**：利用AI预测和设计新药分子结构
- **临床试验优化**：提高试验效率，降低研发成本
- **个性化用药**：基于患者基因型推荐最适合的治疗方案

### 智能诊疗
- **症状分析**：通过自然语言处理分析患者描述
- **辅助诊断**：为医生提供诊断建议和治疗方案
- **健康监测**：可穿戴设备实时监控生命体征

## 技术优势

| 技术特点 | 传统方法 | AI方法 | 改进程度 |
|---------|---------|--------|----------|
| 诊断速度 | 30-60分钟 | 3-5分钟 | 提升10-20倍 |
| 准确率 | 85-90% | 95-98% | 提升5-10% |
| 成本效益 | 高 | 中 | 降低30-50% |

## 挑战与限制

### 技术挑战
- **数据质量**：需要大量高质量的标注数据
- **算法透明性**：医生需要理解AI的决策过程
- **泛化能力**：模型在不同医院、设备间的适用性

### 伦理与监管
- **隐私保护**：患者数据的安全与隐私
- **责任归属**：AI诊断错误的法律责任
- **公平性**：避免算法偏见影响医疗公平

## 未来展望

AI在医疗领域的应用前景广阔：

1. **精准医疗**：基于个人基因组、生活方式的个性化治疗
2. **预防医学**：通过AI预测疾病风险，实现早期干预
3. **远程医疗**：AI助力偏远地区获得高质量医疗服务
4. **医疗资源优化**：智能调度，提高医疗资源利用效率

## 结论

人工智能正在重塑医疗行业的未来。虽然仍面临技术和伦理挑战，但其潜力巨大。通过持续的技术创新和监管完善，AI将成为提升医疗质量、降低成本、促进健康公平的重要工具。

---

*本报告基于最新研究数据和行业实践编写，为AI在医疗领域的应用提供全面分析。*`;

export default function TestPPTPage() {
  const [showPPT, setShowPPT] = useState(false);
  const [selectedContent, setSelectedContent] = useState(testReportContent);

  const contentOptions = [
    {
      name: "AI医疗应用报告",
      content: testReportContent
    },
    {
      name: "简短演示内容",
      content: `# 演示文稿测试

## 欢迎
- 这是一个测试演示文稿
- 用于验证PPT功能

## 主要功能
- 动态内容生成
- 实时预览
- 下载支持

## 谢谢观看
- 测试完成
- 效果良好`
    },
    {
      name: "产品介绍",
      content: `# 智能研究助手

## 产品概述
全新的AI驱动研究工具，帮助您快速获取和分析信息。

## 核心功能

### 智能搜索
- 多源数据整合
- 实时信息抓取
- 智能过滤排序

### 报告生成
- 自动内容组织
- 专业格式输出
- 多种导出选项

### PPT制作
- 一键生成演示文稿
- 可视化数据展示
- 专业模板应用

## 使用优势

| 特性 | 传统方式 | 智能助手 |
|------|----------|----------|
| 研究时间 | 2-3小时 | 10-15分钟 |
| 信息质量 | 参差不齐 | 高质量筛选 |
| 格式规范 | 需手动调整 | 自动标准化 |

## 开始使用
立即体验智能研究的便利与高效！`
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">PPT功能测试页面</h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">测试控制面板</h2>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              选择测试内容：
            </label>
            <select 
              className="w-full p-2 border border-gray-300 rounded-md"
              value={selectedContent}
              onChange={(e) => setSelectedContent(e.target.value)}
            >
              {contentOptions.map((option, index) => (
                <option key={index} value={option.content}>
                  {option.name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex gap-4">
            <Button 
              onClick={() => setShowPPT(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              生成PPT预览
            </Button>
            <Button 
              onClick={() => setShowPPT(false)}
              variant="outline"
            >
              隐藏PPT
            </Button>
          </div>
        </div>

        {showPPT && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">PPT预览效果</h2>
            <div className="flex justify-center">
              <PPTCard
                title="基于研究报告生成的演示文稿"
                content={selectedContent}
                className=""
              />
            </div>
          </div>
        )}

        {!showPPT && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">使用说明</h2>
            <div className="prose max-w-none">
              <p>这是PPT功能的测试页面。您可以：</p>
              <ol>
                <li>选择不同的测试内容</li>
                <li>点击"生成PPT预览"按钮查看效果</li>
                <li>测试PPT预览、编辑和下载功能</li>
              </ol>
              <p>PPT组件会根据选择的内容动态生成演示文稿预览。</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 