// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import "~/styles/globals.css";

import { type Metadata } from "next";
import { <PERSON>ei<PERSON> } from "next/font/google";
import Script from "next/script";

import { ThemeProviderWrapper } from "~/components/deer-flow/theme-provider-wrapper";
import { type DeerFlowConfig } from "~/core/config/types";
import { env } from "~/env";

import { Toaster } from "../components/deer-flow/toaster";

export const metadata: Metadata = {
  title: "LibrAI",
  description:
    "Deep Exploration and Efficient Research, an AI tool that combines language models with specialized tools for research tasks.",
  icons: [
    { rel: "icon", url: "/favicon.svg?v=1.5", type: "image/svg+xml" },
    { rel: "icon", url: "/favicon.ico" }
  ],
};

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

const defaultConfig: DeerFlowConfig = {
  rag: {
    provider: "default"
  },
  models: {
    basic: ["gpt-3.5-turbo"],
    reasoning: ["gpt-4"]
  }
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  // Get API URL from runtime environment
  const runtimeApiUrl = process.env.NEXT_PUBLIC_API_URL;
  
  return (
    <html lang="en" className={`${geist.variable}`} suppressHydrationWarning>
      <head>
        <script>{`window.__deerflowConfig = ${JSON.stringify(defaultConfig)}`}</script>
        <script>{`window.__runtimeConfig = ${JSON.stringify({ apiUrl: runtimeApiUrl })}`}</script>
        {/* Define isSpace function globally to fix markdown-it issues with Next.js + Turbopack
          https://github.com/markdown-it/markdown-it/issues/1082#issuecomment-********** */}
        <Script id="markdown-it-fix" strategy="beforeInteractive">
          {`
            if (typeof window !== 'undefined' && typeof window.isSpace === 'undefined') {
              window.isSpace = function(code) {
                return code === 0x20 || code === 0x09 || code === 0x0A || code === 0x0B || code === 0x0C || code === 0x0D;
              };
            }
          `}
        </Script>
        {/* Performance and error handling optimization */}
        <Script id="performance-optimization" strategy="beforeInteractive">
          {`
            if (typeof window !== 'undefined') {
              // 1. Force all wheel events to be passive
              const originalAddEventListener = EventTarget.prototype.addEventListener;
              EventTarget.prototype.addEventListener = function(type, listener, options) {
                const performanceCriticalEvents = ['wheel', 'scroll', 'touchstart', 'touchmove', 'touchend', 'mousewheel', 'DOMMouseScroll'];
                
                if (performanceCriticalEvents.includes(type)) {
                  if (typeof options === 'boolean') {
                    options = { capture: options, passive: true };
                  } else if (typeof options === 'object' && options !== null) {
                    options = { ...options, passive: true };
                  } else {
                    options = { passive: true };
                  }
                }
                
                return originalAddEventListener.call(this, type, listener, options);
              };
              
              // 2. Block invalid network requests
              const originalFetch = window.fetch;
              window.fetch = function(...args) {
                const url = args[0];
                
                if (typeof url === 'string') {
                  // Block incomplete URLs that cause 404s
                  if (url === 'https' || url === 'http' || url.match(/^https?:\\/\\/[^\\/]*\\/?$/)) {
                    return Promise.reject(new Error('Invalid URL blocked'));
                  }
                  
                  // Block problematic domains
                  try {
                    const hostname = new URL(url, window.location.origin).hostname;
                    const blockedDomains = ['blogger.google', 'blogger.googleusercontent', 'blogger', 'blog', 'fin', 'finances', 'financesforecast'];
                    if (blockedDomains.some(domain => hostname.includes(domain))) {
                      return Promise.reject(new Error('Domain blocked'));
                    }
                  } catch (e) {
                    return Promise.reject(new Error('Invalid URL'));
                  }
                }
                
                return originalFetch.apply(this, args).catch(error => {
                  console.warn('Network request failed silently:', error.message);
                  throw error;
                });
              };
              
              // 3. Global error handling
              window.addEventListener('error', (e) => {
                if (e.message && (e.message.includes('net::ERR') || e.message.includes('Failed to load resource'))) {
                  e.preventDefault();
                  return false;
                }
              });
              
              window.addEventListener('unhandledrejection', (e) => {
                if (e.reason && e.reason.message && (
                  e.reason.message.includes('net::ERR') || 
                  e.reason.message.includes('Failed to load') ||
                  e.reason.message.includes('blocked')
                )) {
                  e.preventDefault();
                }
              });
              
              // 4. Performance CSS
              const style = document.createElement('style');
              style.textContent = \`
                [data-radix-scroll-area-viewport] {
                  will-change: scroll-position !important;
                  transform: translateZ(0) !important;
                  -webkit-overflow-scrolling: touch !important;
                }
                
                img {
                  content-visibility: auto;
                  contain-intrinsic-size: 200px 200px;
                }
              \`;
              document.head.appendChild(style);
            }
          `}
        </Script>
      </head>
      <body className="bg-app">
        <ThemeProviderWrapper>{children}</ThemeProviderWrapper>
        <Toaster />
        {
          // NO USER BEHAVIOR TRACKING OR PRIVATE DATA COLLECTION BY DEFAULT
          //
          // When `NEXT_PUBLIC_STATIC_WEBSITE_ONLY` is `true`, the script will be injected
          // into the page only when `AMPLITUDE_API_KEY` is provided in `.env`
        }
        {env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY && env.AMPLITUDE_API_KEY && (
          <>
            <Script src="https://cdn.amplitude.com/script/d2197dd1df3f2959f26295bb0e7e849f.js"></Script>
            <Script id="amplitude-init" strategy="lazyOnload">
              {`window.amplitude.init('${env.AMPLITUDE_API_KEY}', {"fetchRemoteConfig":true,"autocapture":true});`}
            </Script>
          </>
        )}
      </body>
    </html>
  );
}
