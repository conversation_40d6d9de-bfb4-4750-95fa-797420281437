'use client';

import React, { useState } from 'react';
import { Input, Button } from 'antd';
import PPTCard from '../../components/ppt-card';

const { TextArea } = Input;

export default function PPTPage() {
  const [markdownContent, setMarkdownContent] = useState('');
  const [showPPT, setShowPPT] = useState(false);

  const handleGenerate = () => {
    if (markdownContent.trim()) {
      setShowPPT(true);
    }
  };

  return (
    <div className="flex min-h-screen flex-col items-center p-12">
      <div className="z-10 max-w-5xl w-full items-center justify-between">
        <h1 className="text-3xl font-bold mb-12 text-center">Presentation Generator</h1>
        
        {!showPPT ? (
          <div className="space-y-8 max-w-3xl mx-auto">
            <TextArea
              placeholder="Please input your markdown content here..."
              value={markdownContent}
              onChange={(e) => setMarkdownContent(e.target.value)}
              rows={15}
              className="w-full text-base p-6"
              style={{ fontSize: '16px', lineHeight: '1.6' }}
            />
            <div className="mt-3">
              <Button 
                type="primary"
                onClick={handleGenerate}
                disabled={!markdownContent.trim()}
                className="w-full h-12 text-lg"
                size="large"
              >
                Generate PPT
              </Button>
            </div>
          </div>
        ) : (
          <div className="mt-8">
            <PPTCard 
              title=""
              content={markdownContent}
              className="mx-auto"
            />
          </div>
        )}
      </div>
    </div>
  );
} 