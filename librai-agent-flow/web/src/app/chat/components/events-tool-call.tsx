import React, { useMemo } from "react";

import Image from "~/components/deer-flow/image";
import type { ToolCallRuntime } from "~/core/messages";
import { parseJSON } from "~/core/utils";

interface EventItem {
    thumbnail: string;
    link: string;
    title: string;
    description: string;
    date: {
        when: string;
    };
}

interface EventsData {
    events?: EventItem[];
}

export default function EventToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {
  // 解析事件数据
  const eventsData: EventsData = useMemo(() => {
    try {
      return parseJSON(toolCall.result, {});
    } catch {
      return {};
    }
  }, [toolCall.result]);
  let events: EventItem[] = [];
  
  if (eventsData?.events) {
    events = eventsData.events;
  } else {
    events = [];
  }

  // 日期格式化
  const formatDate = (dateStr: string) => {
    // 处理 "Sat, Aug 9, 1 – 5 PM GMT+8" 格式
    const match = /(\w+),\s+(\w+)\s+(\d+)/.exec(dateStr);
    if (match) {
      const [, , month, day] = match;
      return { day, month: month?.toUpperCase() ?? "???" };
    }
    
    // 如果格式不匹配，尝试解析为Date对象
    try {
      const date = new Date(dateStr);
      if (!isNaN(date.getTime())) {
        const day = date.getDate();
        const month = date.toLocaleString("en-US", { month: "short" }).toUpperCase();
        return { day, month };
      }
    } catch {
      // 如果解析失败，返回默认值
    }
    
    return { day: "?", month: "???" };
  };

  // console.log(eventsData, "eventsData");
  
  return (
    <section className="w-full flex justify-center">
      <div className="w-full max-w-md p-[16px] flex flex-col gap-[16px] rounded-xl border border-[#E8E9FF] bg-[#fff]">
        {/* 顶部标签 */}
        <div className="flex items-center gap-[2px] p-[4px] rounded-sm border border-[#EFEFF3]" style={{ width: "fit-content" }}>
          <img src="/images/event-icon.svg" alt="event" className="w-[16px] h-[16px]"  />
          <span className="text-[12px] text-[#101828]">Event</span>
        </div>
        {/* <div className="text-[14px] text-[#101828]">Event card</div> */}
        <div className="flex flex-col gap-[8px]">
          {events.map((event, idx) => {
            const { day, month } = formatDate(event?.date?.when || "");
            return (
                <a
                    key={idx}
                    href={event.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block group"
                    style={{ textDecoration: "none" }}
                    >
                    <div
                        className="flex justify-between gap-[12px] rounded-lg border border-[#EFEFF3] shadow-md hover:shadow-lg transition-shadow duration-200 p-[12px] cursor-pointer"
                        >
                        <div className="flex flex-col items-center text-[#5C62FF] ml-[4px]">
                            <div className="text-2xl font-bold leading-[24px]">{day}</div>
                            <div className="text-xs uppercase">{month}</div>
                        </div>
                        <div className="flex-1 max-w-[219px]">
                            <h2
                                className="text-sm text-[#101828] leading-[18px] break-words line-clamp-2"
                                style={{
                                display: "-webkit-box",
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: "vertical",
                                overflow: "hidden",
                                }}
                            >
                                {event?.title}
                            </h2>
                            <p
                                className="text-xs text-[#676F83] leading-[15px] line-clamp-2 mt-[4px]"
                                style={{
                                    display: "-webkit-box",
                                    WebkitLineClamp: 2,
                                    WebkitBoxOrient: "vertical",
                                    overflow: "hidden",
                                }}
                                >
                                {event?.description}
                            </p>
                        </div>
                        {event?.thumbnail && (
                        <Image
                            src={event.thumbnail}
                            alt={event.title}
                            className="w-[70px] h-[70px] object-cover rounded-sm flex-shrink-0"
                            fallback={
                              <div className="w-[70px] h-[70px] bg-gray-200 rounded-sm flex items-center justify-center flex-shrink-0">
                                <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9a2 2 0 012-2h3z"></path>
                                </svg>
                              </div>
                            }
                        />
                        )}
                    </div>
                </a>
            );
          })}
        </div>
      </div>
    </section>
  );
}
