// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { MagicWandIcon } from "@radix-ui/react-icons";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowUp, Lightbulb, Plus, X, Paperclip, BookOpen, ChevronRight } from "lucide-react";
import { useCallback, useMemo, useRef, useState, useEffect, forwardRef, useImperativeHandle } from "react";

import { Detective } from "~/components/deer-flow/icons/detective";
import { KnowledgeIcon } from "~/components/deer-flow/icons/knowledge";
import MessageInput, {
  type MessageInputRef,
} from "~/components/deer-flow/message-input";
import { ReportStyleDialog } from "~/components/deer-flow/report-style-dialog";
import { ResourceMentions } from "~/components/deer-flow/resource-mentions";
import { Tooltip } from "~/components/deer-flow/tooltip";
import { BorderBeam } from "~/components/magicui/border-beam";
import { Button } from "~/components/ui/button";
import { Checkbox } from "~/components/ui/checkbox";
import { enhancePrompt } from "~/core/api";
import { getConfig } from "~/core/api/config";
import { queryRAGResources } from "~/core/api/rag";
import type { Option, Resource, Message } from "~/core/messages";
import {
  setEnableDeepThinking,
  setEnableBackgroundInvestigation,
  useSettingsStore,
  useStore,
} from "~/core/store";
import { cn } from "~/lib/utils";

// Add tools 面板组件，直接定义在本文件
const tools = [
  // {
  //   key: "google",
  //   label: "Google",
  //   icon: <img src="/images/google-icon.svg" className="w-[16px] h-[16px]" alt="Google" />,
  // },
  {
    key: "hotel",
    label: "Hotel",
    icon: <img src="/images/hotel-icon.svg" className="w-[16px] h-[16px]" alt="Hotel" />,
    tool_name: "google_hotels_search"
  },
  {
    key: "event",
    label: "Event",
    icon: <img src="/images/event-icon.svg" className="w-[16px] h-[16px]" alt="Event" />,
    tool_name: "google_events_search"
  },
  // {
  //   key: "flight",
  //   label: "Flight",
  //   icon: <img src="/images/flight-icon.svg" className="w-5 h-5" alt="Flight" />,
  // },
  // {
  //   key: "local",
  //   label: "Local",
  //   icon: <img src="/images/location-icon.svg" className="w-5 h-5" alt="Local" />,
  // },
  {
    key: "weather",
    label: "Weather",
    icon: <img src="/images/weather-icon.svg" className="w-[16px] h-[16px]" alt="Weather" />,
    tool_name: "weather_tool"
  },
  {
    key: "timezone",
    label: "Timezone",
    icon: <img src="/images/timezone-icon.svg" className="w-[16px] h-[16px]" alt="Timezone" />,
    tool_name: "time_convert_tool"
  },
  // {
  //   key: "currency",
  //   label: "Currency converter",
  //   icon: <img src="/images/currency-icon.svg" className="w-5 h-5" alt="Currency" />,
  // },
];

export interface InputBoxRef {
  fillContent: (content: string) => void;
}

export const InputBox = forwardRef<InputBoxRef, {
  className?: string;
  size?: "large" | "normal";
  responding?: boolean;
  feedback?: { option: Option } | null;
  onSend?: (
    message: string,
    options?: {
      interruptFeedback?: string;
      resources?: Array<Resource>;
      tools?: Array<string>;
    },
  ) => void;
  onCancel?: () => void;
  onRemoveFeedback?: () => void;
  onFileUpload?: (files: FileList) => void;
  onKnowledgeFileSelect?: (knowledgeFile: { id: string; title: string; size: string }) => void;
  hasFilesBelow?: boolean;
}>(({
  className,
  responding,
  feedback,
  onSend,
  onCancel,
  onRemoveFeedback,
  onFileUpload,
  onKnowledgeFileSelect,
  hasFilesBelow,
}, ref) => {
  const enableDeepThinking = useSettingsStore(
    (state) => state.general.enableDeepThinking,
  );
  const backgroundInvestigation = useSettingsStore(
    (state) => state.general.enableBackgroundInvestigation,
  );
  const reasoningModel = useMemo(() => getConfig().models.reasoning?.[0], []);
  const reportStyle = useSettingsStore((state) => state.general.reportStyle);
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<MessageInputRef>(null);
  const feedbackRef = useRef<HTMLDivElement>(null);
  const addKnowledgeButtonRef = useRef<HTMLButtonElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const addToolsButtonRef = useRef<HTMLButtonElement>(null);
  const [selectedTools, setSelectedTools] = useState<Array<string>>([]);
  // Enhancement state
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [isEnhanceAnimating, setIsEnhanceAnimating] = useState(false);
  const [currentPrompt, setCurrentPrompt] = useState("");

  // Knowledge panel state
  const [showKnowledgePanel, setShowKnowledgePanel] = useState(false);
  const [showToolsPanel, setShowToolsPanel] = useState(false);
  const [knowledgeResources, setKnowledgeResources] = useState<Array<Resource>>([]);
  const [loadingResources, setLoadingResources] = useState(false);
  const [selectedKnowledgeIndex, setSelectedKnowledgeIndex] = useState(0);
  const [showResourcesList, setShowResourcesList] = useState(false);

  // Model Selector State
  const [selectedModel, setSelectedModel] = useState<"4o" | "nano">("4o");
  const [modelMenuOpen, setModelMenuOpen] = useState(false);
  const [hoveredModel, setHoveredModel] = useState<"4o" | "nano" | null>(null);


  // Expose fillContent method through ref
  useImperativeHandle(ref, () => ({
    fillContent: (content: string) => {
      if (inputRef.current) {
        inputRef.current.setContent(content);
        setCurrentPrompt(content);
        // Focus the input after filling content
        inputRef.current.focus();
      }
    }
  }), []);

  const handleSendMessage = useCallback(
    (message: string, resources: Array<Resource>) => {
      if (responding) {
        onCancel?.();
      } else {
        if (message.trim() === "") {
          return;
        }
        if (onSend) {
          // 处理消息内容
          // const processedMessage = processMessage(message);
          console.log("selectedTools", selectedTools);
          onSend(message, {
            interruptFeedback: feedback?.option.value,
            resources: resources,
            tools: selectedTools.map(tool => tools.find(t => t.key === tool)?.tool_name ?? ""),
          });
          onRemoveFeedback?.();
          // Clear enhancement animation after sending
          setIsEnhanceAnimating(false);

        }
      }
    },
    [responding, onCancel, onSend, feedback, onRemoveFeedback, selectedTools],
  );

  const handleEnhancePrompt = useCallback(async () => {
    if (currentPrompt.trim() === "" || isEnhancing) {
      return;
    }

    setIsEnhancing(true);
    setIsEnhanceAnimating(true);

    try {
      const enhancedPrompt = await enhancePrompt({
        prompt: currentPrompt,
        report_style: reportStyle.toUpperCase(),
      });

      // Add a small delay for better UX
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Update the input with the enhanced prompt with animation
      if (inputRef.current) {
        inputRef.current.setContent(enhancedPrompt);
        setCurrentPrompt(enhancedPrompt);
      }

      // Keep animation for a bit longer to show the effect
      setTimeout(() => {
        setIsEnhanceAnimating(false);
      }, 1000);
    } catch (error) {
      console.error("Failed to enhance prompt:", error);
      setIsEnhanceAnimating(false);
      // Could add toast notification here
    } finally {
      setIsEnhancing(false);
    }
  }, [currentPrompt, isEnhancing, reportStyle]);

  // Handle Add Knowledge button click
  const handleAddKnowledge = useCallback(() => {
    if (showKnowledgePanel) {
      setShowKnowledgePanel(false);
      setShowResourcesList(false);
    } else {
      setShowKnowledgePanel(true);
      setShowResourcesList(false);
    }
    setSelectedKnowledgeIndex(0);
  }, [showKnowledgePanel]);

  // Handle upload files
  const handleUploadFiles = useCallback(() => {
    setShowKnowledgePanel(false);
    fileInputRef.current?.click();
  }, []);

  // Handle file selection
  const handleFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && onFileUpload) {
      onFileUpload(files);
    }
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [onFileUpload]);

  // Handle choose knowledge
  const handleChooseKnowledge = useCallback(async () => {
    setLoadingResources(true);
    setShowResourcesList(true);
    
    try {
      // Load all available resources (empty query to get all)
      const resources = await queryRAGResources("");
      setKnowledgeResources(resources);
    } catch (error) {
      console.error("Failed to load knowledge resources:", error);
      setKnowledgeResources([]);
    } finally {
      setLoadingResources(false);
    }
  }, []);

  const handleAddTools = useCallback(() => {
    if (showToolsPanel) {
      setShowToolsPanel(false);
      return;
    }
    setShowToolsPanel(true);
  }, [showToolsPanel]);

  // Handle resource selection
  const handleSelectKnowledge = useCallback((item: { id: string; label: string }) => {
    // 通知父组件添加知识文件到展示区域
    if (onKnowledgeFileSelect) {
      onKnowledgeFileSelect({
        id: item.id,
        title: item.label,
        size: "32k" // 默认大小，可以根据实际情况调整
      });
    }
    
    setShowKnowledgePanel(false);
    setShowResourcesList(false);
  }, [onKnowledgeFileSelect]);

  // Handle keyboard navigation for knowledge panel
  const handleKnowledgePanelKeyDown = useCallback((event: KeyboardEvent) => {
    if (!showKnowledgePanel) return;

    if (event.key === "Escape") {
      if (showResourcesList) {
        setShowResourcesList(false);
        setSelectedKnowledgeIndex(1); // Return to "Choose knowledge" option
      } else {
        setShowKnowledgePanel(false);
      }
      event.preventDefault();
      return;
    }

    const maxIndex = showResourcesList ? knowledgeResources.length - 1 : 1; // Main menu has 2 options (0,1)

    if (event.key === "ArrowUp") {
      setSelectedKnowledgeIndex((prev) => 
        prev > 0 ? prev - 1 : maxIndex
      );
      event.preventDefault();
      return;
    }

    if (event.key === "ArrowDown") {
      setSelectedKnowledgeIndex((prev) => 
        prev < maxIndex ? prev + 1 : 0
      );
      event.preventDefault();
      return;
    }

    if (event.key === "Enter") {
      if (!showResourcesList) {
        // Main menu
        if (selectedKnowledgeIndex === 0) {
          handleUploadFiles();
        } else if (selectedKnowledgeIndex === 1) {
          handleChooseKnowledge();
        }
      } else {
        // Knowledge resources list
        const selectedResource = knowledgeResources[selectedKnowledgeIndex];
        if (selectedResource) {
          handleSelectKnowledge({
            id: selectedResource.uri,
            label: selectedResource.title
          });
        }
      }
      event.preventDefault();
      return;
    }
  }, [showKnowledgePanel, showResourcesList, knowledgeResources, selectedKnowledgeIndex, handleSelectKnowledge, handleUploadFiles, handleChooseKnowledge]);

  // Add keyboard event listener
  useEffect(() => {
    if (showKnowledgePanel) {
      document.addEventListener("keydown", handleKnowledgePanelKeyDown);
      return () => {
        document.removeEventListener("keydown", handleKnowledgePanelKeyDown);
      };
    }
  }, [showKnowledgePanel, handleKnowledgePanelKeyDown]);

  // Close panel when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        showKnowledgePanel &&
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) ||
        (event.target as HTMLElement).closest('.ProseMirror') // 检查点击是否在编辑器区域内
      ) {
        if (showResourcesList) {
          setShowResourcesList(false);
          setSelectedKnowledgeIndex(1); // Return to "Choose knowledge" option
        } else {
          setShowKnowledgePanel(false);
        }
      }
      
      if (
        showToolsPanel &&
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) ||
        (event.target as HTMLElement).closest('.ProseMirror') // 检查点击是否在编辑器区域内
      ) {
        setShowToolsPanel(false);
      }
    }

    if (showKnowledgePanel || showToolsPanel) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }
  }, [showKnowledgePanel, showToolsPanel, showResourcesList]);

  // 添加输入区域的点击处理
  const handleInputClick = useCallback(() => {
    if (showKnowledgePanel) {
      setShowKnowledgePanel(false);
      setShowResourcesList(false);
    }
    if (showToolsPanel) {
      setShowToolsPanel(false);
    }
  }, [showKnowledgePanel, showToolsPanel]);

  // 在组件内部添加测试PPT功能
//   const testPPTGeneration = () => {
//     const markdownReportContent = `# 人工智能技术发展趋势研究报告

// ## 摘要

// 本报告深入分析了人工智能技术的最新发展趋势，包括机器学习、深度学习、自然语言处理等核心技术的突破和应用前景。

// ## 主要发现

// ### 1. 技术突破

// - **大型语言模型**：GPT系列、Claude等模型在理解和生成能力上取得显著进展
// - **多模态AI**：图像、文本、音频的融合处理能力不断提升  
// - **AI芯片**：专用AI处理器性能大幅提升，能耗比显著改善

// ### 2. 应用领域扩展

// - **医疗健康**：AI辅助诊断准确率超过95%
// - **自动驾驶**：Level 4自动驾驶技术逐步成熟
// - **金融科技**：智能风控、量化交易广泛应用
// - **教育科技**：个性化学习方案、智能评估系统

// ### 3. 市场规模预测

// | 年份 | 市场规模(亿美元) | 增长率 |
// |------|------------------|--------|
// | 2023 | 1,350 | 35% |
// | 2024 | 1,847 | 37% |
// | 2025 | 2,575 | 39% |
// | 2026 | 3,654 | 42% |

// ## 技术挑战

// ### 计算资源需求
// - 训练大型模型需要大量GPU资源
// - 推理成本持续优化中
// - 边缘计算部署面临挑战

// ### 数据与隐私
// - 高质量训练数据稀缺
// - 数据隐私保护要求提高
// - 跨域数据共享困难

// ### 伦理与安全
// - AI偏见问题需要持续关注
// - 深度伪造技术带来安全隐患
// - AI治理框架亟待完善

// ## 发展趋势预测

// ### 短期(1-2年)

// 1. **模型效率提升**：更小、更快、更准确的模型
// 2. **工具集成**：AI能力深度集成到各类软件工具
// 3. **成本下降**：AI服务价格持续降低

// ### 中期(3-5年)

// 1. **通用人工智能初现**：向AGI迈出实质性步伐
// 2. **行业标准化**：AI应用标准和规范逐步建立
// 3. **生态完善**：完整的AI产业生态链形成

// ### 长期(5-10年)

// 1. **人机协作新模式**：AI成为人类工作的重要伙伴
// 2. **社会结构变革**：就业模式、教育体系深度调整
// 3. **全球治理体系**：国际AI治理机制日趋成熟

// ## 投资建议

// ### 技术投资方向

// - **基础模型研发**：持续投入核心算法研究
// - **垂直应用**：专注特定领域的AI解决方案
// - **基础设施**：AI训练和推理基础设施建设

// ### 风险管控

// - **技术风险**：关注技术路线选择和迭代速度
// - **监管风险**：密切跟踪政策法规变化
// - **竞争风险**：保持技术领先和差异化优势

// ## 结论

// 人工智能技术正处于快速发展期，未来3-5年将是关键窗口期。企业和投资者应：

// 1. **把握机遇**：积极布局AI技术和应用
// 2. **防范风险**：建立完善的风险管控机制
// 3. **持续创新**：保持技术研发和产品创新
// 4. **合规发展**：确保AI应用符合伦理和法规要求

// 通过科学规划和战略布局，可以在AI时代获得持续竞争优势。

// ---

// *本报告基于公开资料和行业调研编制，数据截至2024年12月。*`;

//     const pptMarkdownContent = `## 📊 PPT生成完成

// 基于您的研究报告，我已成功生成了一份专业的演示文稿。PPT包含了报告中的关键信息和数据可视化内容。

// ### 🎯 演示文稿特色

// - **结构清晰**：按照研究报告的逻辑结构组织内容
// - **数据可视化**：将表格和统计数据转换为图表形式
// - **专业设计**：采用现代化的模板和配色方案
// - **重点突出**：突出关键发现和结论

// ### 💡 您可以进行的操作

// - 📖 **预览PPT内容**：查看生成的演示文稿效果
// - ✏️ **编辑演示文稿**：根据需要调整内容和样式
// - 💾 **下载PPT文件**：获取可编辑的PowerPoint文件

// 演示文稿已准备就绪，请点击下方的PPT卡片查看效果！`;

//     const testReportMessage: Message = {
//       id: `test-report-${Date.now()}`,
//       threadId: useStore.getState().threadId || "test-thread",
//       agent: "reporter",
//       role: "assistant",
//       content: markdownReportContent,
//       contentChunks: [markdownReportContent],
//       isStreaming: false,
//     };
    
//     const testMessage: Message = {
//       id: `test-ppt-${Date.now()}`,
//       threadId: useStore.getState().threadId || "test-thread",
//       agent: "ppt_generator",
//       role: "assistant",
//       content: pptMarkdownContent,
//       contentChunks: [pptMarkdownContent],
//       isStreaming: false,
//     };
    
//     // 添加到store
//     useStore.getState().appendMessage(testReportMessage);
//     useStore.getState().appendMessage(testMessage);
//   };

  return (
    <div
      className={cn(
        "bg-card relative flex h-full w-full flex-col",
        hasFilesBelow
          ? "rounded-t-[24px] border-x border-t border-b"
          : "rounded-[24px] border",
        className,
      )}
      style={{
        borderColor: hasFilesBelow ? '#DCDFE6' : '#E5E5EF'
      }}
      ref={containerRef}
    >
      <div className="w-full">
        <AnimatePresence>
          {feedback && (
            <motion.div
              ref={feedbackRef}
              className="bg-background border-brand absolute top-0 left-0 mt-2 ml-4 flex items-center justify-center gap-1 rounded-2xl border px-2 py-0.5"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
              transition={{ duration: 0.2, ease: "easeInOut" }}
            >
              <div className="text-brand flex h-full w-full items-center justify-center text-sm opacity-90">
                {feedback.option.text}
              </div>
              <X
                className="cursor-pointer opacity-60"
                size={16}
                onClick={onRemoveFeedback}
              />
            </motion.div>
          )}
          {isEnhanceAnimating && (
            <motion.div
              className="pointer-events-none absolute inset-0 z-20"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="relative h-full w-full">
                {/* Sparkle effect overlay */}
                <motion.div
                  className="absolute inset-0 rounded-[24px] bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-blue-500/10"
                  animate={{
                    background: [
                      "linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1))",
                      "linear-gradient(225deg, rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1))",
                      "linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1))",
                    ],
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                />
                {/* Floating sparkles */}
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute h-2 w-2 rounded-full bg-blue-400"
                    style={{
                      left: `${20 + i * 12}%`,
                      top: `${30 + (i % 2) * 40}%`,
                    }}
                    animate={{
                      y: [-10, -20, -10],
                      opacity: [0, 1, 0],
                      scale: [0.5, 1, 0.5],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      delay: i * 0.2,
                    }}
                  />
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        <MessageInput
          className={cn(
            "h-24 p-[16px] pb-0",
            // feedback && "pt-9",
            isEnhanceAnimating && "transition-all duration-500",
          )}
          ref={inputRef}
          onEnter={handleSendMessage}
          onChange={setCurrentPrompt}
          onClick={handleInputClick} // 添加点击处理
        />
      </div>
      <div className="flex items-center p-[16px] pt-[8px]">
        <div className="flex grow gap-2 items-center">
          {/* {reasoningModel && (
            <Tooltip
              className="max-w-60"
              title={
                <div>
                  <h3 className="mb-2 font-bold">
                    Deep Thinking Mode: {enableDeepThinking ? "On" : "Off"}
                  </h3>
                  <p>
                    When enabled, LibrAI will use reasoning model (
                    {reasoningModel}) to generate more thoughtful plans.
                  </p>
                </div>
              }
            >
              <Button
                className={cn(
                  "rounded-2xl",
                  enableDeepThinking && "!border-brand !text-brand",
                )}
                variant="outline"
                onClick={() => {
                  setEnableDeepThinking(!enableDeepThinking);
                }}
              >
                <Lightbulb /> Deep Thinking
              </Button>
            </Tooltip>
          )} */}

          {/* <Tooltip
            className="max-w-60"
            title={
              <div>
                <h3 className="mb-2 font-bold">
                  Investigation Mode: {backgroundInvestigation ? "On" : "Off"}
                </h3>
                <p>
                  When enabled, LibrAI will perform a quick search before
                  planning. This is useful for researches related to ongoing
                  events and news.
                </p>
              </div>
            }
          >
            <Button
              className={cn(
                "rounded-2xl",
                backgroundInvestigation && "!border-brand !text-brand",
              )}
              variant="outline"
              onClick={() =>
                setEnableBackgroundInvestigation(!backgroundInvestigation)
              }
            >
              <Detective /> Investigation
            </Button>
          </Tooltip> */}
          {/* <ReportStyleDialog /> */}

          {/* <Tooltip title="测试PPT生成功能">
            <Button
              variant="outline"
              className="rounded-2xl bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 text-blue-700 hover:from-blue-100 hover:to-purple-100"
              onClick={testPPTGeneration}
            >
              📊 测试PPT
            </Button>
          </Tooltip> */}

          <div className="relative">
            <Tooltip title="Add Knowledge">
              <Button
                ref={addKnowledgeButtonRef}
                variant="ghost"
                size="icon"
                                  className={cn(
                  "w-[32px] h-[32px] rounded-lg bg-[#F7F7FC] hover:bg-[#EAEAF2]",
                  showKnowledgePanel && "!border-brand !text-brand"
                )}
                onClick={handleAddKnowledge}
              >
                <Plus className="h-4 w-4 text-[#343A3E]" />
              </Button>
            </Tooltip>
            {/* Knowledge Panel */}
            <AnimatePresence>
              {showKnowledgePanel && (
                <motion.div
                  className="absolute bottom-full mb-2 left-0 z-50"
                  initial={{ opacity: 0, scale: 0.95, y: 10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: 10 }}
                  transition={{ duration: 0.15, ease: "easeOut" }}
                >
                  <div 
                    className="relative flex flex-col p-1 overflow-auto rounded-xl border bg-white shadow-[0px_10px_10px_-5px_rgba(0,0,0,0.04),0px_20px_25px_-5px_rgba(0,0,0,0.04)] min-w-[200px] max-w-[300px] max-h-[300px]"
                    style={{ borderColor: '#E5E5EF' }}
                    onMouseLeave={() => setSelectedKnowledgeIndex(-1)}
                  >
                    {!showResourcesList ? (
                      // Main menu options (matching Figma design)
                      <div className="flex flex-col gap-1">
                        <button
                          className={cn(
                            "flex items-center gap-2 px-2 py-2 rounded-lg text-sm text-left w-full transition-colors",
                            "hover:bg-[#F7F7FC] focus:bg-[#F7F7FC] focus:outline-none",
                            selectedKnowledgeIndex === 0 && "bg-[#F7F7FC]"
                          )}
                          onClick={handleUploadFiles}
                          onMouseEnter={() => setSelectedKnowledgeIndex(0)}
                          style={{ color: '#343A3E' ,display:'none'}}
                        >
                          <Paperclip className="h-4 w-4 shrink-0" />
                          <span className="font-normal">Upload files</span>
                        </button>
                        <button
                          className={cn(
                            "flex items-center gap-2 px-2 py-2 rounded-lg text-sm text-left w-full transition-colors",
                            "hover:bg-[#F7F7FC] focus:bg-[#F7F7FC] focus:outline-none",
                            selectedKnowledgeIndex === 1 && "bg-[#F7F7FC]"
                          )}
                          onClick={handleChooseKnowledge}
                          onMouseEnter={() => setSelectedKnowledgeIndex(1)}
                          style={{ color: '#343A3E' }}
                        >
                          <KnowledgeIcon className="h-4 w-4 shrink-0" />
                          <span className="font-normal flex-1">Choose knowledge</span>
                          <ChevronRight className="h-4 w-4 shrink-0" />
                        </button>
                      </div>
                    ) : (
                      // Knowledge resources list
                      <div className="flex flex-col gap-1">
                        {loadingResources ? (
                          <div className="flex items-center justify-center p-4 text-gray-500">
                            Loading...
                          </div>
                        ) : knowledgeResources.length > 0 ? (
                          knowledgeResources.map((item, index) => (
                            <button
                              key={index}
                              className={cn(
                                "flex items-center gap-2 px-2 py-2 rounded-lg text-sm text-left w-full transition-colors",
                                "hover:bg-[#F7F7FC] focus:bg-[#F7F7FC] focus:outline-none",
                                selectedKnowledgeIndex === index && "bg-[#F7F7FC]"
                              )}
                              onClick={() => handleSelectKnowledge({
                                id: item.uri,
                                label: item.title
                              })}
                              onMouseEnter={() => setSelectedKnowledgeIndex(index)}
                              style={{ color: '#343A3E' }}
                            >
                              {/* <BookOpen className="h-4 w-4 shrink-0" /> */}
                              <span className="font-normal truncate">{item.title}</span>
                            </button>
                          ))
                        ) : (
                          <div className="flex items-center justify-center p-4 text-gray-500">
                            No knowledge resources available
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          <div className="relative h-[32px]">
            <Tooltip title="Add Tools">
              {selectedTools.length > 0 ? <div className="h-[32px] flex items-center gap-[6px] bg-[#EAEAF2] rounded-lg px-[6px] py-[8px]" onClick={handleAddTools}>
                {selectedTools.slice(0, 3).map((tool) => (
                  <img key={tool} src={`/images/${tool}-icon.svg`} alt="" className="w-[16px] h-[16px]"/>
                ))}
                {selectedTools.length > 3 && <div className="text-xs text-[#9099B1]">+{selectedTools.length - 3}</div>}
              </div> : <Button
                ref={addToolsButtonRef}
                variant="ghost"
                size="icon"
                onClick={handleAddTools}
                className="w-[32px] h-[32px]"
              >
                <img src="/images/tool-icon.svg" alt="" className="w-[32px] h-[32px]"/>
              </Button>}
            </Tooltip>
            <AnimatePresence>
              {showToolsPanel && (
                <motion.div
                  className="absolute bottom-full mb-2 left-0 z-50"
                  initial={{ opacity: 0, scale: 0.95, y: 10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: 10 }}
                  transition={{ duration: 0.15, ease: "easeOut" }}
                >
                  <div className="bg-white border-[#EAEAF2] relative flex flex-col overflow-auto rounded-xl border py-[8px] px-[4px] shadow-[0px_20px_25px_-5px_rgba(0,0,0,0.04),_0px_10px_10px_-5px_rgba(0,0,0,0.04)] w-[260px]">
                    <div className="font-semibold text-sm text-[#343A3E] p-[8px]">Add tools</div>
                    <ul className="flex flex-col">
                                              {tools.map((tool) => (
                          <li 
                            key={tool.key} 
                            className="flex items-center gap-[8px] p-[8px] hover:bg-[#F7F7FC] rounded-lg cursor-pointer"
                            onClick={() => {
                              const isSelected = selectedTools.includes(tool.key);
                              if (isSelected) {
                                setSelectedTools(selectedTools.filter((t) => t !== tool.key));
                              } else {
                                setSelectedTools([...selectedTools, tool.key]);
                              }
                            }}
                          >
                            <span className="flex items-center justify-center">{tool.icon}</span>
                            <span className="flex-1 text-sm text-[#343A3E]">{tool.label}</span>
                            <Checkbox
                              checked={selectedTools.includes(tool.key)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedTools([...selectedTools, tool.key]);
                                } else {
                                  setSelectedTools(selectedTools.filter((t) => t !== tool.key));
                                }
                              }}
                              className="w-[16px] h-[16px] data-[state=checked]:bg-[#5C62FF] data-[state=checked]:border-[#5C62FF]"
                            />
                          </li>
                        ))}
                    </ul>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
        <div className="flex shrink-0 items-center gap-2">
          <Tooltip title="Enhance prompt with AI">
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-[32px]",
                "hover:bg-accent",
                isEnhancing && "animate-pulse",
              )}
              onClick={handleEnhancePrompt}
              disabled={isEnhancing || currentPrompt.trim() === ""}
            >
              {isEnhancing ? (
                <div className="flex h-10 w-10 items-center justify-center">
                  <div className="bg-foreground h-3 w-3 animate-bounce rounded-full opacity-70" />
                </div>
              ) : (
                <MagicWandIcon className="text-brand" />
              )}
            </Button>
          </Tooltip>
          {/* Model Selector Dropdown */}
          <div className="relative">
            <Tooltip title="Select Model">
              <Button
                variant="ghost"
                className="h-[32px] border-none flex items-center justify-between p-[6px] text-[#343A3E] text-sm"
                onClick={() => setModelMenuOpen((v) => !v)}
              >
                {selectedModel === "4o" ? "ChatGPT 4o" : "ChatGPT nano"}
                <svg className="w-4 h-4" viewBox="0 0 20 20" fill="none"><path d="M6 8l4 4 4-4" stroke="#333" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
              </Button>
            </Tooltip>
            <AnimatePresence>
              {modelMenuOpen && (
                <motion.div
                  className="absolute left-0 bottom-full mb-2 w-[180px] rounded-xl border bg-white border-[#EAEAF2] shadow-[0px_20px_25px_-5px_rgba(0,0,0,0.04),_0px_10px_10px_-5px_rgba(0,0,0,0.04)] z-50 p-[4px] text-sm text-[#343A3E]"
                  initial={{ opacity: 0, scale: 0.95, y: 10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: 10 }}
                  transition={{ duration: 0.15, ease: "easeOut" }}
                >
                  {[
                    { key: "4o", label: "ChatGPT 4o" },
                    { key: "nano", label: "ChatGPT nano" }
                  ].map((item) => (
                    <div
                      key={item.key}
                      className={cn(
                        "flex items-center p-[8px] cursor-pointer rounded-lg",
                        hoveredModel === item.key && "bg-[#F7F7FC]"
                      )}
                      onMouseEnter={() => setHoveredModel(item.key as "4o" | "nano")}
                      onMouseLeave={() => setHoveredModel(null)}
                      onClick={() => {
                        setSelectedModel(item.key as "4o" | "nano");
                        setModelMenuOpen(false);
                      }}
                    >
                      <span className="flex-1">{item.label}</span>
                      {selectedModel === item.key && (
                        <svg className="w-5 h-5 text-[#5C62FF]" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" strokeWidth="2" d="M5 13l4 4L19 7"/></svg>
                      )}
                    </div>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          <Tooltip title={responding ? "Stop" : "Send"}>
            <Button
              variant="ghost"
              size="icon"
              // className={cn("h-10 w-10")}
              className="w-[32px] h-[32px] border-none"
              onClick={() => inputRef.current?.submit()}
            >
              {responding || !currentPrompt.trim() ? (
                <img src="/images/send-icon.svg" alt="" className="w-[32px] h-[32px]"/>
              ) : (
                <img src="/images/send-icon-active.svg" alt="" className="w-[32px] h-[32px]"/>
              )}
            </Button>
          </Tooltip>
        </div>
      </div>
      {isEnhancing && (
        <>
          <BorderBeam
            duration={5}
            size={250}
            className="from-transparent via-red-500 to-transparent"
          />
          <BorderBeam
            duration={5}
            delay={3}
            size={250}
            className="from-transparent via-blue-500 to-transparent"
          />
        </>
      )}
      
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="*/*"
        className="hidden"
        onChange={handleFileChange}
      />
    </div>
  );
});

InputBox.displayName = "InputBox";
