import type { ToolCallRuntime } from "~/core/messages";
import { parseJSON } from "~/core/utils";

// 解析结果类型
interface TimezoneResult {
    from_timezone: string;
    to_timezone: string;
    source_formatted: string;
    target_formatted: string;
}

function formatOffset(offset: number) {
  if (offset === 0) return "+0HRS";
  return (offset > 0 ? "+" : "") + offset + "HRS";
}

// 格式化时区时间字符串 "2023-07-08 04:11:56 PDT -0700" -> "04:11 AM PDT"
function formatTimezoneTime(timezoneString: string): string {
  try {
    // 解析格式: "2023-07-08 04:11:56 PDT -0700"
    const match = /(\d{2}):(\d{2}):(\d{2})\s+(\w+)/.exec(timezoneString);
    if (match?.[1] && match[2] && match[4]) {
      const hour = match[1];
      const minute = match[2];
      const timezone = match[4];
      const hourNum = parseInt(hour, 10);
      const ampm = hourNum >= 12 ? 'PM' : 'AM';
      const displayHour = hourNum === 0 ? 12 : hourNum > 12 ? hourNum - 12 : hourNum;
      return `${displayHour.toString().padStart(2, '0')}:${minute} ${ampm} ${timezone}`;
    }
    return timezoneString;
  } catch {
    return timezoneString;
  }
}

// 提取时区偏移量 "2023-07-08 04:11:56 PDT -0700" -> -7
function extractOffset(timezoneString: string): number {
  try {
    const match = /([+-]\d{4})$/.exec(timezoneString);
    if (match?.[1]) {
      const offsetStr = match[1];
      const hours = parseInt(offsetStr.substring(1, 3), 10);
      return offsetStr.startsWith('-') ? -hours : hours;
    }
    return 0;
  } catch {
    return 0;
  }
}

// 格式化日期 "2023-07-08 04:11:56 PDT -0700" -> "Jul 8, 2023"
function formatDate(timezoneString: string): string {
  try {
    // 解析格式: "2023-07-08 04:11:56 PDT -0700"
    const match = /(\d{4})-(\d{2})-(\d{2})/.exec(timezoneString);
    if (match?.[1] && match[2] && match[3]) {
      const year = match[1];
      const month = parseInt(match[2], 10);
      const day = parseInt(match[3], 10);
      
      const date = new Date(parseInt(year), month - 1, day);
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric', 
        year: 'numeric' 
      });
    }
    return "Today";
  } catch {
    return "Today";
  }
}

const TimezoneToolCall = ({ toolCall }: { toolCall: ToolCallRuntime }) => {
//   const results = useMemo(() => parseResult(toolCall.result), [toolCall.result]);
    let timezoneData: TimezoneResult | null = null;
    let timezoneList = [];
    try {
        timezoneData = parseJSON(toolCall.result, {
            from_timezone: "",
            to_timezone: "",
            source_formatted: "",
            target_formatted: "",
        });
    } catch {
        timezoneData = null;
    }

  // console.log(timezoneData, "timezoneData");
  timezoneList=[
    {
        location: timezoneData?.from_timezone,
        time: formatTimezoneTime(timezoneData?.source_formatted ?? ""),
        offset: formatOffset(extractOffset(timezoneData?.source_formatted ?? "")),
        date: formatDate(timezoneData?.source_formatted ?? "")
    },
    {
        location: timezoneData?.to_timezone,
        time: formatTimezoneTime(timezoneData?.target_formatted ?? ""),
        offset: formatOffset(extractOffset(timezoneData?.target_formatted ?? "")),
        date: formatDate(timezoneData?.target_formatted ?? "")
    }
  ];
  // 支持多城市，若无结果则显示空
  return (
    <section className="w-full flex justify-center">
      <div className="w-full max-w-md p-[16px] flex flex-col gap-[16px] rounded-xl border border-[#E8E9FF] bg-[#fff]">
        {/* 顶部标签区 */}
        <div className="flex items-center gap-[4px] p-[4px] rounded-sm border border-[#EFEFF3] w-fit">
          {/* <span className="flex items-center gap-1 h-[26px] px-[8px] rounded-[4px] border border-[#EFEFF3]"> */}
            <img src="/images/timezone-icon.svg" className="w-[16px] h-[16px]" />
            <span className="text-[12px] text-[#101828]">Timezone</span>
          {/* </span> */}
        </div>
        {/* 标题 */}
        <div className="text-sm text-[#101828]">Timezone card</div>
        {/* 卡片区 */}
        <div className="flex flex-col gap-[8px]">
          {timezoneList.length === 0 && (
            <div className="text-gray-400 text-center">No timezone data</div>
          )}
          {timezoneList.map((item, i) => (
            <div
              key={i}
              className="flex items-center justify-between gap-[12px] p-[12px] rounded-lg border border-[#EFEFF3] text-[#101828] text-sm"
            >
              <div className="flex flex-col justify-between">
                <span className="text-[#676F83] text-xs">{item?.date}, {item?.offset}</span>
                <span className="font-semibold text-base">{item?.location}</span>
              </div>
              <span className="font-bold text-xl">{item?.time}</span>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TimezoneToolCall;
