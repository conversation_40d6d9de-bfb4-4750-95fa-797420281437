import React, { useMemo } from "react";

import Image from "~/components/deer-flow/image";
import type { ToolCallRuntime } from "~/core/messages";
import { parseJSON } from "~/core/utils";

// 酒店数据类型
interface Hotel {
  name: string;
  overall_rating: number;
  link: string;
  thumbnail: string;
  price: string;
  reviews: number;
}

interface HotelResults {
  ads?: Hotel[];
}


const HotelsToolCall = ({ toolCall }: { toolCall: ToolCallRuntime }) => {

  const results: HotelResults = useMemo(() => {
    try {
      return parseJSON(toolCall.result, {});
    } catch {
      return {};
    }
  }, [toolCall.result]);

//   console.log(results, "results");

  return (
    <section className="w-full flex justify-center">
      <div className="w-full p-[16px] flex flex-col gap-[16px] rounded-xl border border-[#E8E9FF] bg-[#fff]">
        {/* 顶部徽章 */}
        <div className="flex items-center gap-[2px] p-[4px] rounded-sm border border-[#EFEFF3]"  style={{ width: "fit-content" }}>
          {/* <span className="flex items-center gap-1 h-[32px] px-3 rounded-md border border-[#EFEFF3] bg-white text-base font-medium"> */}
            <img src="/images/hotel-icon.svg" alt="hotel" className="w-[16px] h-[16px]" />
            <span className="text-[12px] text-[#101828]">Hotel</span>
          {/* </span> */}
        </div>
        {/* <div className="text-[14px] text-[#101828]">Hotel card</div> */}
        {/* 酒店卡片列表 */}
        <div className="flex flex-col gap-[8px]">
          {results?.ads?.map((hotel: Hotel, idx: number) => {
            const fullStars = Math.floor(hotel?.overall_rating || 0);
            const halfStar = (hotel?.overall_rating || 0) - fullStars >= 0.5;
            const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);
            
            return (
              <a
              key={idx}
              href={hotel?.link}
              target="_blank"
              rel="noopener noreferrer"
              className="block group"
              style={{ textDecoration: "none" }}
            >
              <div
                className="flex justify-between gap-[8px] rounded-lg border border-[#EFEFF3] shadow-md hover:shadow-lg transition-shadow duration-200 p-[12px] cursor-pointer"
              >
                <div className="flex-1 min-w-0 flex flex-col justify-between">
                  <div className="w-full min-w-0">
                      <div className="text-sm text-[#101828] leading-[18px] break-words"
                           style={{
                             wordBreak: "break-word",
                             overflowWrap: "break-word"
                           }}>
                          {hotel?.name}
                          </div>
                          {/* star rating */}
                          <div className="flex items-center gap-[2px] mt-[4px] h-[15px] leading-[15px]">
                          <span className="text-xs text-[#676F83]">{hotel?.overall_rating}/5</span>
                          <span className="flex items-center ml-[4px]">
                              {Array.from({ length: fullStars }).map((_, i) => (
                              <img src="/images/star-active-icon.svg" key={i} className="w-[12px] h-[12px]" />
                              ))}
                              {halfStar && <img src="/images/star-active-icon.svg" className="w-[12px] h-[12px]" />}
                              {Array.from({ length: emptyStars }).map((_, i) => (
                              <img src="/images/star-inactive-icon.svg" key={i} className="w-[12px] h-[12px]" />
                              ))}
                          </span>
                          <span className="text-xs text-[#676F83] ml-[4px]">
                              ({(hotel?.reviews / 1000).toFixed(1)}k)
                          </span>
                          </div>
                      </div>
                      <div className="text-sm text-[#101828] leading-[18px]">${hotel?.price}</div>
                  </div>
                  {hotel?.thumbnail && <Image
                    src={hotel?.thumbnail}
                    alt={hotel?.name}
                    className="w-[70px] h-[70px] object-cover rounded-sm flex-shrink-0"
                    fallback={
                      <div className="w-[70px] h-[70px] bg-gray-200 rounded-sm flex items-center justify-center flex-shrink-0">
                        <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                      </div>
                    }
                  />}
              </div>
            </a>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default HotelsToolCall;
