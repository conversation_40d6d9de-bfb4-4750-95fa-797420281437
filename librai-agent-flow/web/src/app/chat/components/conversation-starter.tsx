// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { motion } from "framer-motion";

import { cn } from "~/lib/utils";

import { Welcome } from "./welcome";

const questions = [
  "Review the resumes and rank the candidates by expertise.",
  "List properties for purchase in a safe neighborhood in New York.",
  "Among NVDA, MRVL, and TSM, which trades at the steepest valuation premium and what drives it?",
];
export function ConversationStarter({
  className,
  onSend,
  onFillInput,
}: {
  className?: string;
  onSend?: (message: string) => void;
  onFillInput?: (message: string) => void;
}) {
  return (
    <div className={cn("w-full", className)}>
      <div className="mb-6 flex justify-center absolute left-0 right-0 top-[calc(50%-200px)]">
        {/* <Welcome className="w-[75%]" /> */}
      </div>
      <ul className="flex flex-col">
        {questions.map((question, index) => (
          <motion.li
            key={question}
            className="w-full"
            style={{ transition: "all 0.2s ease-out" }}
            initial={{ opacity: 0, y: 24 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{
              duration: 0.2,
              delay: index * 0.1 + 0.5,
              ease: "easeOut",
            }}
          >
            <div
              className="cursor-pointer p-[12px] text-sm opacity-75 transition-all duration-300 hover:opacity-100 h-full flex items-center text-[#343A3E]"
              onClick={() => {
                onFillInput?.(question);
              }}
            >
              {question}
            </div>
          </motion.li>
        ))}
      </ul>
    </div>
  );
}
