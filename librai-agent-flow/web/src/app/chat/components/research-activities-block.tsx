// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { PythonOutlined } from "@ant-design/icons";
import { RiNewspaperLine } from "@remixicon/react";
import { motion } from "framer-motion";
import { LRUCache } from "lru-cache";
import { BookOpenText, FileText, PencilRuler, Search } from "lucide-react";
import { useTheme } from "next-themes";
import { useMemo } from "react";
import SyntaxHighlighter from "react-syntax-highlighter";
import { docco } from "react-syntax-highlighter/dist/esm/styles/hljs";
import { dark } from "react-syntax-highlighter/dist/esm/styles/prism";

import { FavIcon } from "~/components/deer-flow/fav-icon";
import Image from "~/components/deer-flow/image";
import { LoadingAnimation } from "~/components/deer-flow/loading-animation";
import { Markdown } from "~/components/deer-flow/markdown";
import { RainbowText } from "~/components/deer-flow/rainbow-text";
import { Tooltip } from "~/components/deer-flow/tooltip";
import PPTCard from "~/components/ppt-card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "~/components/ui/accordion";
import { Skeleton } from "~/components/ui/skeleton";
// import WorkflowHotelsCard from "~/components/workflow-hotels-card";
import { findMCPTool } from "~/core/mcp";
import type { ToolCallRuntime } from "~/core/messages";
import { useMessage, useStore } from "~/core/store";
import { parseJSON } from "~/core/utils";
import { cn } from "~/lib/utils";

import EventToolCall from "./events-tool-call";
import HotelsToolCall from "./hotels-tool-call";
import TimezoneToolCall from "./timezone-tool-call";
import WeatherToolCall from "./weather-tool-call";

export function ResearchActivitiesBlock({
  className,
  researchId,
}: {
  className?: string;
  researchId: string;
}) {
  const activityIds = useStore((state) =>
    state.researchActivityIds.get(researchId),
  )!;
  const ongoing = useStore((state) => state.ongoingResearchId === researchId);
  return (
    <div className="w-full min-w-0 research-activities-block" 
         style={{
           wordBreak: "break-word",
           overflowWrap: "break-word"
         }}>
      <ul className={cn("flex flex-col py-4 gap-[16px] min-w-0", className)}>
        {activityIds.map(
          (activityId, i) =>
            i !== 0 && (
              <motion.li
                key={activityId}
                style={{ 
                  transition: "all 0.4s ease-out",
                  minWidth: 0,
                  wordBreak: "break-word",
                  overflowWrap: "break-word"
                }}
                initial={{ opacity: 0, y: 24 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.4,
                  ease: "easeOut",
                }}
                className="min-w-0 w-full"
              >
                <ActivityMessage messageId={activityId} />
                <ActivityListItem messageId={activityId} />
                {/* {i !== activityIds.length - 1 && <hr className="my-8" />} */}
              </motion.li>
            ),
        )}
      </ul>
      {ongoing && <LoadingAnimation className="mx-4 my-12" />}
    </div>
  );
}

function ActivityMessage({ messageId }: { messageId: string }) {
  const message = useMessage(messageId);
  // Move all hooks to the top level to avoid conditional hook calls
  const researchIds = useStore((state) => state.researchIds);
  const messages = useStore((state) => state.messages);
  const researchReportIds = useStore((state) => state.researchReportIds);
  
  if (message?.agent && message.content) {
    if (message.agent === "ppt_generator") {
      // 获取当前研究相关的报告内容
      
      // 查找最近的研究报告内容
      let reportContent = "";
      
      // 方法1：查找最新的研究报告
      const latestResearchId = researchIds[researchIds.length - 1];
      if (latestResearchId) {
        const reportId = researchReportIds.get(latestResearchId);
        if (reportId) {
          const reportMessage = messages.get(reportId);
          if (reportMessage?.content) {
            reportContent = reportMessage.content;
          }
        }
      }
      
      // 方法2：如果没有找到，尝试在最近的消息中查找reporter消息
      // if (!reportContent) {
      //   const messageIds = useStore.getState().messageIds;
      //   const recentMessages = messageIds.slice(-10); // 查看最近10条消息
      //   for (let i = recentMessages.length - 1; i >= 0; i--) {
      //     const msgId = recentMessages[i];
      //     const msg = messages.get(msgId);
      //     if (msg?.agent === "reporter" && msg.content && msg.content.length > 500) {
      //       reportContent = msg.content;
      //       break;
      //     }
      //   }
      // }
        //  return (
        //   <div className="w-full flex justify-center">
        //     <PPTCard
        //       content={reportContent as string}
        //       className="max-w-lg"
        //     />
        //   </div>
        // );
    }
    if (message.agent !== "reporter" && message.agent !== "planner") {
      return (
        <div className="w-full flex justify-center overflow-x-auto">
           <div className="w-full rounded-xl border border-[#E8E9FF] bg-white p-[16px] flex flex-col gap-[16px] font-sans">
            <Markdown animated checkLinkCredibility>
              {message.content}
            </Markdown>
          </div>
        </div>
       
      );
    }
  }
  return null;
}

function ActivityListItem({ messageId }: { messageId: string }) {
  const message = useMessage(messageId);
  if (message) {
    if (!message.isStreaming && message.toolCalls?.length) {
      for (const toolCall of message.toolCalls) {
        if (toolCall.name === "web_search") {
          return <WebSearchToolCall key={toolCall.id} toolCall={toolCall} />;
        } else if (toolCall.name.includes("weather")) {
          return <WeatherToolCall key={toolCall.id} toolCall={toolCall} />;
        } else if (toolCall.name.includes("time")) {
          return <TimezoneToolCall key={toolCall.id} toolCall={toolCall} />;
        } else if (toolCall.name.includes("events")) {
          return <EventToolCall key={toolCall.id} toolCall={toolCall}/>;
        } else if (toolCall.name.includes("hotels")) {
          return <HotelsToolCall key={toolCall.id} toolCall={toolCall}/>;
        } else if (toolCall.name.includes("geocode")) {
          return null
        }  else if (toolCall.name === "crawl_tool") {
          return <CrawlToolCall key={toolCall.id} toolCall={toolCall} />;
          // return null;
        } else if (toolCall.name === "python_repl_tool") {
          return <PythonToolCall key={toolCall.id} toolCall={toolCall} />;
          // return null;
        } else if (toolCall.name === "local_search_tool") {
          return <RetrieverToolCall key={toolCall.id} toolCall={toolCall} />;
          // return null;
        } else {
          return <MCPToolCall key={toolCall.id} toolCall={toolCall} />;
          // return null;
        }
      }
    }
  }
  return null;
}

const __pageCache = new LRUCache<string, string>({ max: 100 });
type SearchResult =
  | {
      type: "page";
      title: string;
      url: string;
      content: string;
    }
  | {
      type: "image";
      image_url: string;
      image_description: string;
    };

function WebSearchToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {
  const searching = useMemo(() => toolCall.result === undefined, [toolCall.result]);
  const searchResults = useMemo<SearchResult[]>(() => {
    let results: SearchResult[] | undefined = undefined;
    try {
      results = toolCall.result ? parseJSON(toolCall.result, []) : undefined;
    } catch {
      results = undefined;
    }
    if (Array.isArray(results)) {
      results.forEach((result) => {
        if (result.type === "page") {
          __pageCache.set(result.url, result.title);
        }
      });
    } else {
      results = [];
    }
    return results;
  }, [toolCall.result]);
  const pageResults = useMemo(
    () => searchResults?.filter((result) => result.type === "page"),
    [searchResults],
  );
  const imageResults = useMemo(
    () => searchResults?.filter((result) => result.type === "image"),
    [searchResults],
  );
  const query = (toolCall.args as { query: string }).query;

  // 错误检测
  const isError = typeof toolCall.result === 'string' && (
    toolCall.result.startsWith('Exception(') ||
    toolCall.result.toLowerCase().startsWith('error')
  );

  // 详细内容智能提取
  let markdownContent: string | null = null;
  if (!isError && toolCall.result) {
    try {
      const parsed = JSON.parse(toolCall.result);
      if (Array.isArray(parsed)) {
        markdownContent = parsed
          .filter((item) => item.type === 'page' && item.content)
          .map((item) => item.content)
          .join('\n\n');
      }
    } catch {
      markdownContent = toolCall.result;
    }
  }

  // 顶部标签
  const toolName = toolCall.name;
  const toolLabelMap: Record<string, { icon: React.ReactNode; label: string }> = {
    web_search: { icon: <img src="/images/google-icon.svg" className="w-[12px] h-[12px] m-[3px]" />, label: "Google" },
    // local_search_tool: { icon: <span className="w-4 h-4 mr-1 bg-blue-400 rounded-full inline-block" />, label: "Knowledge 1" },
    // 可扩展更多
  };
  const toolBadge = toolName && toolLabelMap[toolName]
    ? (
      <span className="flex items-center gap-[2px] h-[26px] px-[4px] rounded-[4px] border border-[#EFEFF3]">
        {toolLabelMap[toolName].icon}
        {toolLabelMap[toolName].label}
      </span>
    )
    : null;

  return (
    <section className="w-full flex justify-center">
      <div className="w-full rounded-xl border border-[#E8E9FF] bg-white p-[16px] flex flex-col gap-[16px] font-sans">
        {/* 顶部标签区 */}
        <div className="flex items-center gap-[8px] text-xs text-[#101828]">{toolBadge}</div>
        {/* 搜索框 */}
        <div className="flex items-center bg-[#EFEFF3] rounded-lg px-3 py-2 text-[#676F83] min-h-[40px] h-auto">
          <Search size={16} className="mr-2 text-gray-400 flex-shrink-0" />
          <span className="break-words leading-tight"
                style={{
                  wordBreak: "break-word",
                  overflowWrap: "break-word"
                }}>{query}</span>
        </div>
        {/* 网页卡片区 */}
        {pageResults && pageResults.length > 0 && (
          <div className="relative">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-[8px] p-[8px] rounded-lg border border-[#EFEFF3] bg-[#F7F7FC]">
              {pageResults.map((result, i) => (
                <a
                  key={i}
                  href={result.url}
                  target="_blank"
                  className="min-h-[67px] bg-white rounded-lg p-[12px] flex flex-col justify-between cursor-pointer hover:shadow-md transition-shadow"
                >
                  <div className="text-sm font-bold text-[#101828] leading-tight line-clamp-2 mb-2" 
                       style={{
                         display: "-webkit-box",
                         WebkitLineClamp: 2,
                         WebkitBoxOrient: "vertical",
                         overflow: "hidden",
                         wordBreak: "break-word",
                         overflowWrap: "break-word"
                       }}>
                    {result.title}
                  </div>
                  <div className="flex items-center gap-[4px] text-xs text-[#676F83]">
                    <RiNewspaperLine className="w-[16px] h-[16px] text-gray-400 flex-shrink-0" />
                    <span className="break-all text-xs leading-tight truncate"
                          style={{
                            wordBreak: "break-all",
                            overflowWrap: "break-word"
                          }}>
                      {result.url.replace(/^https?:\/\//, '').split('/')[0]}
                    </span>
                  </div>
                </a>
              ))}
            </div>
            <img src="/images/open-icon.svg" className="absolute w-[24px] h-[24px] right-[13px] bottom-[13px] cursor-pointer" />
          </div>
          
        )}
        {/* 图片卡片区 */}
        {imageResults && imageResults.length > 0 && (
          <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-2">
            {imageResults.map((result, i) => (
              <a
                key={i}
                href={result.image_url}
                target="_blank"
                className="aspect-square flex flex-col items-center bg-gray-50 border rounded-lg hover:shadow transition"
              >
                <img
                  src={result.image_url}
                  alt={result.image_description}
                  className="w-full h-full object-cover rounded"
                  loading="lazy"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const parent = target.parentElement;
                    if (parent) {
                      parent.innerHTML = `
                        <div class="w-full h-full flex items-center justify-center bg-gray-200 rounded text-gray-500 text-xs">
                          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                          </svg>
                        </div>
                      `;
                    }
                  }}
                  onLoad={(e) => {
                    // 图片加载成功，确保显示
                    (e.target as HTMLImageElement).style.display = 'block';
                  }}
                />
                {/* <span className="text-xs text-gray-500 mt-1 truncate">{result.image_description}</span> */}
              </a>
            ))}
          </div>
        )}
        {/* 详细内容区 */}
        {toolCall.result && (
          isError ? (
            <div className="text-red-500 font-mono mt-2 break-all">
              {toolCall.result}
            </div>
          ) : (
            markdownContent ? (
              <div className="prose prose-sm max-w-none">
                <Markdown>{markdownContent}</Markdown>
              </div>
            ) : null
          )
        )}
      </div>
    </section>
  );
}

function CrawlToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {
  const url = useMemo(
    () => (toolCall.args as { url: string }).url,
    [toolCall.args],
  );
  const title = useMemo(() => __pageCache.get(url), [url]);
  return (
    <section className="w-full flex justify-center">
      <div className="w-full rounded-xl border border-[#E8E9FF] bg-white p-[16px] flex flex-col gap-[16px] font-sans">
      <div>
        <RainbowText
          className="flex items-center text-base font-medium italic"
          animated={toolCall.result === undefined}
        >
          <BookOpenText size={16} className={"mr-2"} />
          <span>Reading</span>
        </RainbowText>
      </div>
      <ul className="mt-2 flex flex-wrap gap-4">
        <motion.li
          className="text-muted-foreground bg-accent flex h-40 w-40 gap-2 rounded-md px-2 py-1 text-sm"
          initial={{ opacity: 0, y: 10, scale: 0.66 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{
            duration: 0.2,
            ease: "easeOut",
          }}
        >
          <FavIcon className="mt-1" url={url} title={title} />
          <a
            className="h-full flex-grow overflow-hidden text-ellipsis whitespace-nowrap"
            href={url}
            target="_blank"
          >
            {title ?? url}
          </a>
        </motion.li>
      </ul>
      </div>
     
    </section>
  );
}

function RetrieverToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {
  const searching = useMemo(() => {
    return toolCall.result === undefined;
  }, [toolCall.result]);
  const documents = useMemo<
    Array<{ id: string; title: string; content: string }>
  >(() => {
    return toolCall.result ? parseJSON(toolCall.result, []) : [];
  }, [toolCall.result]);
  return (
    <section className="mt-4 pl-4">
      <div className="font-medium italic">
        <RainbowText className="flex items-center" animated={searching}>
          <Search size={16} className={"mr-2"} />
          <span>Retrieving documents from RAG&nbsp;</span>
          <span className="max-w-[500px] overflow-hidden text-ellipsis whitespace-nowrap">
            {(toolCall.args as { keywords: string }).keywords}
          </span>
        </RainbowText>
      </div>
      <div className="pr-4">
        {documents && (
          <ul className="mt-2 flex flex-wrap gap-4">
            {searching &&
              [...Array(2)].map((_, i) => (
                <li
                  key={`search-result-${i}`}
                  className="flex h-40 w-40 gap-2 rounded-md text-sm"
                >
                  <Skeleton
                    className="to-accent h-full w-full rounded-md bg-gradient-to-tl from-slate-400"
                    style={{ animationDelay: `${i * 0.2}s` }}
                  />
                </li>
              ))}
            {documents.map((doc, i) => (
              <motion.li
                key={`search-result-${i}`}
                className="text-muted-foreground bg-accent flex max-w-40 gap-2 rounded-md px-2 py-1 text-sm"
                initial={{ opacity: 0, y: 10, scale: 0.66 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                  duration: 0.2,
                  delay: i * 0.1,
                  ease: "easeOut",
                }}
              >
                <FileText size={32} />
                {doc.title}
              </motion.li>
            ))}
          </ul>
        )}
      </div>
    </section>
  );
}

function PythonToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {
  const code = useMemo<string | undefined>(() => {
    return (toolCall.args as { code?: string }).code;
  }, [toolCall.args]);
  const { resolvedTheme } = useTheme();
  return (
    <section className="w-full flex justify-center">
      <div className="w-full rounded-xl border border-[#E8E9FF] bg-white p-[16px] flex flex-col gap-[16px] font-sans">
      <div className="flex items-center">
        <PythonOutlined className={"mr-2"} />
        <RainbowText
          className="text-base font-medium italic"
          animated={toolCall.result === undefined}
        >
          Running Python code
        </RainbowText>
      </div>
      <div>
        <div className="bg-accent mt-2 max-h-[400px] overflow-y-auto rounded-md p-2 text-sm">
          <SyntaxHighlighter
            language="python"
            style={resolvedTheme === "dark" ? dark : docco}
            customStyle={{
              background: "transparent",
              border: "none",
              boxShadow: "none",
            }}
          >
            {code?.trim() ?? ""}
          </SyntaxHighlighter>
        </div>
      </div>
      {toolCall.result && <PythonToolCallResult result={toolCall.result} />}
      </div>
      
    </section>
  );
}

function PythonToolCallResult({ result }: { result: string }) {
  const { resolvedTheme } = useTheme();
  const hasError = useMemo(
    () => result.includes("Error executing code:\n"),
    [result],
  );
  const error = useMemo(() => {
    if (hasError) {
      const parts = result.split("```\nError: ");
      if (parts.length > 1) {
        return parts[1]!.trim();
      }
    }
    return null;
  }, [result, hasError]);
  const stdout = useMemo(() => {
    if (!hasError) {
      const parts = result.split("```\nStdout: ");
      if (parts.length > 1) {
        return parts[1]!.trim();
      }
    }
    return null;
  }, [result, hasError]);
  return (
    <>
      <div className="mt-4 font-medium italic">
        {hasError ? "Error when executing the above code" : "Execution output"}
      </div>
      <div className="bg-accent mt-2 max-h-[400px] overflow-y-auto rounded-md p-2 text-sm">
        <SyntaxHighlighter
          language="plaintext"
          style={resolvedTheme === "dark" ? dark : docco}
          customStyle={{
            color: hasError ? "red" : "inherit",
            background: "transparent",
            border: "none",
            boxShadow: "none",
          }}
        >
          {error ?? stdout ?? "(empty)"}
        </SyntaxHighlighter>
      </div>
    </>
  );
}

function MCPToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {
  const tool = useMemo(() => findMCPTool(toolCall.name), [toolCall.name]);
  const { resolvedTheme } = useTheme();
  return (
    <section className="w-full flex justify-center">
      <div className="overflow-y-auto w-full rounded-xl border border-[#E8E9FF] bg-white p-[16px] flex flex-col gap-[16px] font-sans">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="item-1">
            <AccordionTrigger>
              <Tooltip title={tool?.description}>
                <div className="flex items-center font-medium italic">
                  <PencilRuler size={16} className={"mr-2"} />
                  <RainbowText
                    className="pr-0.5 text-base font-medium italic"
                    animated={toolCall.result === undefined}
                  >
                    Running {toolCall.name ? toolCall.name + "()" : "MCP tool"}
                  </RainbowText>
                </div>
              </Tooltip>
            </AccordionTrigger>
            <AccordionContent>
              {toolCall.result && (
                <div className="bg-accent max-h-[400px] max-w-[560px] overflow-y-auto rounded-md text-sm">
                  <SyntaxHighlighter
                    language="json"
                    style={resolvedTheme === "dark" ? dark : docco}
                    customStyle={{
                      background: "transparent",
                      border: "none",
                      boxShadow: "none",
                    }}
                  >
                    {toolCall.result.trim()}
                  </SyntaxHighlighter>
                </div>
              )}
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </section>
  );
}
