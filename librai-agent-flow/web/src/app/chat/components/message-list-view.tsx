// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { LoadingOutlined } from "@ant-design/icons";
import { motion } from "framer-motion";
import {
  Download,
  Headphones,
  ChevronDown,
  ChevronRight,
  Lightbulb,
} from "lucide-react";
import React, { useCallback, useMemo, useRef, useState } from "react";

import { LoadingAnimation } from "~/components/deer-flow/loading-animation";
import { Markdown } from "~/components/deer-flow/markdown";
import { RainbowText } from "~/components/deer-flow/rainbow-text";
import { RollingText } from "~/components/deer-flow/rolling-text";
import {
  ScrollContainer,
  type ScrollContainerRef,
} from "~/components/deer-flow/scroll-container";
import { Tooltip } from "~/components/deer-flow/tooltip";
import { Button } from "~/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "~/components/ui/collapsible";
import type { Message, Option } from "~/core/messages";
import {
  closeResearch,
  openResearch,
  useLastFeedbackMessageId,
  useLastInterruptMessage,
  useMessage,
  useMessageIds,
  useResearchMessage,
  useStore,
} from "~/core/store";
import { parseJSON } from "~/core/utils";
import { cn } from "~/lib/utils";

import { StepFeedbackCard } from "./step-feedback-card";

export function MessageListView({
  className,
  onFeedback,
  onSendMessage,
}: {
  className?: string;
  onFeedback?: (feedback: { option: Option }) => void;
  onSendMessage?: (
    message: string,
    options?: { interruptFeedback?: string },
  ) => void;
}) {
  const scrollContainerRef = useRef<ScrollContainerRef>(null);
  const messageIds = useMessageIds();
  const interruptMessage = useLastInterruptMessage();
  const waitingForFeedbackMessageId = useLastFeedbackMessageId();
  const responding = useStore((state) => state.responding);
  const noOngoingResearch = useStore(
    (state) => state.ongoingResearchId === null,
  );
  const ongoingResearchIsOpen = useStore(
    (state) => state.ongoingResearchId === state.openResearchId,
  );

  // 检查是否有正在流式渲染的追问表单 - 优化检测逻辑避免频繁开关
  const hasStreamingClarificationForm = useMemo(() => {
    // 优化：只检查最后几条消息，避免遍历所有消息
    const recentMessageIds = messageIds.slice(-5); // 只检查最近5条消息
    const messages = useStore.getState().messages;

    // 先检查是否有正在流式生成的planner消息
    const hasStreamingPlanner = recentMessageIds.some(messageId => {
      const message = messages.get(messageId);
      return message?.agent === "planner" && message.isStreaming;
    });

    if (!hasStreamingPlanner) {
      return false;
    }

    // 只在有流式planner消息时才检查内容
    return recentMessageIds.some(messageId => {
      const message = messages.get(messageId);
      if (message?.agent === "planner" && message.isStreaming) {
        // 使用更简单的检测逻辑：只要内容中包含追问相关的关键词
        const content = message.content ?? "";
        return content.includes("clarification_questions") ||
               content.includes("additional information") ||
               content.includes("provide some additional information");
      }
      return false;
    });
  }, [messageIds]);

  const handleToggleResearch = useCallback(() => {
    // Fix the issue where auto-scrolling to the bottom
    // occasionally fails when toggling research.
    const timer = setTimeout(() => {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.scrollToBottom();
      }
    }, 500);
    return () => {
      clearTimeout(timer);
    };
  }, []);

  // 监听新问题添加事件，触发滚动 - 使用 useCallback 优化性能
  const handleQuestionAdded = React.useCallback(() => {
    // 使用 requestAnimationFrame 替代 setTimeout，性能更好
    requestAnimationFrame(() => {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.scrollToBottom();
      }
    });
  }, []);

  React.useEffect(() => {
    window.addEventListener('question-added', handleQuestionAdded);
    return () => {
      window.removeEventListener('question-added', handleQuestionAdded);
    };
  }, [handleQuestionAdded]);

  return (
    <ScrollContainer
      className={cn("flex h-full w-full flex-col overflow-hidden", className)}
      scrollShadowColor="var(--app-background)"
      autoScrollToBottom={!hasStreamingClarificationForm}
      ref={scrollContainerRef}
    >
      <ul className="flex flex-col">
        {messageIds.map((messageId) => (
          <MessageListItem
            key={messageId}
            messageId={messageId}
            waitForFeedback={waitingForFeedbackMessageId === messageId}
            interruptMessage={interruptMessage}
            onFeedback={onFeedback}
            onSendMessage={onSendMessage}
            onToggleResearch={handleToggleResearch}
          />
        ))}
        <div className="flex h-8 w-full shrink-0"></div>
      </ul>
      {responding && (noOngoingResearch || !ongoingResearchIsOpen) && (
        <LoadingAnimation className="ml-4" />
      )}
    </ScrollContainer>
  );
}

const MessageListItem = React.memo(function MessageListItem({
  className,
  messageId,
  waitForFeedback,
  interruptMessage,
  onFeedback,
  onSendMessage,
  onToggleResearch,
}: {
  className?: string;
  messageId: string;
  waitForFeedback?: boolean;
  onFeedback?: (feedback: { option: Option }) => void;
  interruptMessage?: Message | null;
  onSendMessage?: (
    message: string,
    options?: { interruptFeedback?: string },
  ) => void;
  onToggleResearch?: () => void;
}) {
  const message = useMessage(messageId);
  const researchIds = useStore((state) => state.researchIds);
  const startOfResearch = useMemo(() => {
    return researchIds.includes(messageId);
  }, [researchIds, messageId]);
  const stepFeedbackData = useStore((state) => state.stepFeedbackData);
  const stepFeedbackMessageId = useStore((state) => state.stepFeedbackMessageId);
  if (message) {
    if (messageId === stepFeedbackMessageId && stepFeedbackData) {
      return (
        <div className="w-full px-4">
          <StepFeedbackCard
            stepTitle={stepFeedbackData.step_title}
            stepDescription={stepFeedbackData.step_description}
            stepType={stepFeedbackData.step_type}
            executionResult={stepFeedbackData.execution_result}
            hasErrors={stepFeedbackData.has_errors}  // 新增
            errorReasons={stepFeedbackData.error_reasons}  // 新增
            errorSummary={stepFeedbackData.error_summary}  // 新增
            onFeedback={(feedback) => {
              onSendMessage?.(feedback, { interruptFeedback: feedback });
            }}
          />
        </div>
      );
    }
    if (
      message.role === "user" ||
      message.agent === "coordinator" ||
      message.agent === "planner" ||
      message.agent === "podcast" ||
      message.agent === "ppt_generator" ||
      startOfResearch
    ) {
      let content: React.ReactNode;
      if (message.agent === "planner") {
        content = (
          <div className="w-full px-4">
            <PlanCard
              message={message}
              waitForFeedback={waitForFeedback}
              interruptMessage={interruptMessage}
              onFeedback={onFeedback}
              onSendMessage={onSendMessage}
            />
          </div>
        );
      } else if (message.agent === "podcast") {
        content = (
          <div className="w-full px-4">
            <PodcastCard message={message} />
          </div>
        );
      } 
      // else if (message.agent === "ppt_generator") {
      //   content = (
      //     <div className="w-full px-4">
      //       <PPTCard 
      //         className=""
      //       />
      //     </div>
      //   );
      // } 
      else if (startOfResearch) {
        content = (
          <div className="w-full px-4">
            <ResearchCard
              researchId={message.id}
              onToggleResearch={onToggleResearch}
            />
          </div>
        );
      } else {
        content = message.content ? (
          <div
            className={cn(
              "flex w-full px-4",
              message.role === "user" && "justify-end",
              className,
            )}
          >
            <MessageBubble message={message}>
              <div className="flex w-full flex-col text-wrap break-words">
                {message.resources && message.resources.length > 0 && (
                  <div className="mb-3 flex flex-wrap gap-2">
                    {message.resources.map((resource, index) => (
                      <div
                        key={`${message.id}-resource-${index}`}
                        className="flex items-center gap-2 rounded-lg border bg-background px-2 py-1 text-xs"
                        style={{ borderColor: '#E5E5EF' }}
                      >
                        <span className="text-muted-foreground">📎</span>
                        <span className="truncate max-w-32" title={resource.title}>
                          {resource.title}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
                <Markdown
                  className={cn(
                    message.role === "user" &&
                      "prose-invert not-dark:text-secondary dark:text-inherit",
                  )}
                >
                  {message?.content}
                </Markdown>
              </div>
            </MessageBubble>
          </div>
        ) : null;
      }
      if (content) {
        return (
          <motion.li
            className="mt-10"
            key={messageId}
            initial={{ opacity: 0, y: 24 }}
            animate={{ opacity: 1, y: 0 }}
            style={{ transition: "all 0.2s ease-out" }}
            transition={{
              duration: 0.2,
              ease: "easeOut",
            }}
          >
            {content}
          </motion.li>
        );
      }
    }
    return null;
  }
});

const MessageBubble = React.memo(function MessageBubble({
  className,
  message,
  children,
}: {
  className?: string;
  message: Message;
  children: React.ReactNode;
}) {
  const openResearchId = useStore((state) => state.openResearchId);
  const isDoubleColumnMode = openResearchId !== null;
  
  return (
    <div
      className={cn(
        `group flex w-fit flex-col rounded-2xl px-4 py-3 text-nowrap shadow`,
        // Adjust max width based on layout mode to prevent text overflow
        isDoubleColumnMode ? "max-w-[75%]" : "max-w-[85%]",
        message.role === "user" && "bg-brand rounded-ee-none",
        message.role === "assistant" && "bg-card rounded-es-none",
        className,
      )}
    >
      {children}
    </div>
  );
});

const ResearchCard = React.memo(function ResearchCard({
  className,
  researchId,
  onToggleResearch,
}: {
  className?: string;
  researchId: string;
  onToggleResearch?: () => void;
}) {
  const reportId = useStore((state) => state.researchReportIds.get(researchId));
  const hasReport = reportId !== undefined;
  const reportGenerating = useStore(
    (state) => hasReport && state.messages.get(reportId)!.isStreaming,
  );
  const openResearchId = useStore((state) => state.openResearchId);
  const state = useMemo(() => {
    if (hasReport) {
      return reportGenerating ? "Generating report..." : "Report generated";
    }
    return "Researching...";
  }, [hasReport, reportGenerating]);
  const msg = useResearchMessage(researchId);
  const title = useMemo(() => {
    if (msg) {
      return parseJSON(msg.content ?? "", { title: "" }).title;
    }
    return undefined;
  }, [msg]);
  const handleOpen = useCallback(() => {
    if (openResearchId === researchId) {
      closeResearch();
    } else {
      openResearch(researchId);
    }
    onToggleResearch?.();
  }, [openResearchId, researchId, onToggleResearch]);
  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle>
          <RainbowText animated={state !== "Report generated"}>
            {title !== undefined && title !== "" ? title : "Deep Research"}
          </RainbowText>
        </CardTitle>
      </CardHeader>
      <CardFooter>
        <div className="flex w-full">
          <RollingText className="text-muted-foreground flex-grow text-sm">
            {state}
          </RollingText>
          <Button
            variant={!openResearchId ? "default" : "outline"}
            onClick={handleOpen}
          >
            {researchId !== openResearchId ? "Open" : "Close"}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
});

const ThoughtBlock = React.memo(function ThoughtBlock({
  className,
  content,
  isStreaming,
  hasMainContent,
}: {
  className?: string;
  content: string;
  isStreaming?: boolean;
  hasMainContent?: boolean;
}) {
  const [isOpen, setIsOpen] = useState(true);

  const [hasAutoCollapsed, setHasAutoCollapsed] = useState(false);

  React.useEffect(() => {
    if (hasMainContent && !hasAutoCollapsed) {
      setIsOpen(false);
      setHasAutoCollapsed(true);
    }
  }, [hasMainContent, hasAutoCollapsed]);

  if (!content || content.trim() === "") {
    return null;
  }

  return (
    <div className={cn("mb-6 w-full", className)}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className={cn(
              "h-auto w-full justify-start rounded-xl border px-6 py-4 text-left transition-all duration-200",
              "hover:bg-accent hover:text-accent-foreground",
              isStreaming
                ? "border-primary/20 bg-primary/5 shadow-sm"
                : "border-border bg-card",
            )}
          >
            <div className="flex w-full items-center gap-3">
              <Lightbulb
                size={18}
                className={cn(
                  "shrink-0 transition-colors duration-200",
                  isStreaming ? "text-primary" : "text-muted-foreground",
                )}
              />
              <span
                className={cn(
                  "leading-none font-semibold transition-colors duration-200",
                  isStreaming ? "text-primary" : "text-foreground",
                )}
              >
                Deep Thinking
              </span>
              {isStreaming && <LoadingAnimation className="ml-2 scale-75" />}
              <div className="flex-grow" />
              {isOpen ? (
                <ChevronDown
                  size={16}
                  className="text-muted-foreground transition-transform duration-200"
                />
              ) : (
                <ChevronRight
                  size={16}
                  className="text-muted-foreground transition-transform duration-200"
                />
              )}
            </div>
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:slide-up-2 data-[state=open]:slide-down-2 mt-3">
          <Card
            className={cn(
              "transition-all duration-200",
              isStreaming ? "border-primary/20 bg-primary/5" : "border-border",
            )}
          >
            <CardContent>
              <div className="flex h-40 w-full overflow-y-auto">
                <ScrollContainer
                  className={cn(
                    "flex h-full w-full flex-col overflow-hidden",
                    className,
                  )}
                  scrollShadow={false}
                  autoScrollToBottom
                >
                  <Markdown
                    className={cn(
                      "prose dark:prose-invert max-w-none transition-colors duration-200",
                      isStreaming ? "prose-primary" : "opacity-80",
                    )}
                    animated={isStreaming}
                  >
                    {content}
                  </Markdown>
                </ScrollContainer>
              </div>
            </CardContent>
          </Card>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
});

const GREETINGS = ["Cool", "Sounds great", "Looks good", "Great", "Awesome"];
function PlanCard({
  className,
  message,
  interruptMessage,
  onFeedback,
  waitForFeedback,
  onSendMessage,
}: {
  className?: string;
  message: Message;
  interruptMessage?: Message | null;
  onFeedback?: (feedback: { option: Option }) => void;
  onSendMessage?: (
    message: string,
    options?: { interruptFeedback?: string },
  ) => void;
  waitForFeedback?: boolean;
}) {
  // 稳定的问题列表 - 一旦解析出问题就不再变化，只增加新问题
  const [stableClarificationQuestions, setStableClarificationQuestions] = useState<Array<{
    question: string;
    options: Array<{ text: string; value: string; description?: string }>;
    allow_custom_input: boolean;
    required: boolean;
  }>>([]);

  // 当前解析的plan对象
  const currentPlan = useMemo(() => {
    try {
      return parseJSON(message.content ?? "", {}) as {
        title?: string;
        thought?: string;
        steps?: { title?: string; description?: string }[];
        clarification_questions?: string[];
        clarification_questions_with_options?: Array<{
          question: string;
          options: Array<{ text: string; value: string; description?: string }>;
          allow_custom_input: boolean;
          required: boolean;
        }>;
      };
    } catch {
      return {};
    }
  }, [message.content]);

  // 增量更新稳定的问题列表，并在新问题出现时平滑滚动
  React.useEffect(() => {
    if (currentPlan.clarification_questions_with_options) {
      const newQuestions = currentPlan.clarification_questions_with_options;

      // 使用 JSON.stringify 比较内容，避免引用比较导致的频繁更新
      const newQuestionsStr = JSON.stringify(newQuestions);
      const currentQuestionsStr = JSON.stringify(stableClarificationQuestions);

      // 只有当问题内容真正发生变化时才更新
      if (newQuestionsStr !== currentQuestionsStr) {
        const previousLength = stableClarificationQuestions.length;
        setStableClarificationQuestions(newQuestions);

        // 当出现新问题时，标记需要滚动（通过现有的滚动系统处理）
        if (newQuestions.length > previousLength && previousLength > 0) {
          // 使用 requestAnimationFrame 替代 setTimeout，性能更好
          requestAnimationFrame(() => {
            window.dispatchEvent(new CustomEvent('question-added'));
          });
        }
      }
    }
  }, [currentPlan.clarification_questions_with_options, stableClarificationQuestions]);

  // 当消息停止流式生成时，清理状态为下次使用做准备
  React.useEffect(() => {
    if (!message.isStreaming && stableClarificationQuestions.length > 0) {
      // 延迟清理，确保最终状态稳定
      const timer = setTimeout(() => {
        if (!message.isStreaming) {
          // 最终同步：使用当前解析的完整结果
          if (currentPlan.clarification_questions_with_options) {
            const finalQuestions = currentPlan.clarification_questions_with_options;
            // 只有当问题内容真正不同时才更新
            if (JSON.stringify(finalQuestions) !== JSON.stringify(stableClarificationQuestions)) {
              setStableClarificationQuestions(finalQuestions);
            }
          }
        }
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [message.isStreaming, stableClarificationQuestions, currentPlan.clarification_questions_with_options]);

  // 使用稳定的plan对象，避免频繁重新渲染
  const plan = useMemo(() => ({
    ...currentPlan,
    clarification_questions_with_options: stableClarificationQuestions
  }), [currentPlan, stableClarificationQuestions]);

  const reasoningContent = message.reasoningContent;
  const hasMainContent = Boolean(
    message.content && message.content.trim() !== "",
  );
  const isThinking = Boolean(reasoningContent && !hasMainContent);
  const shouldShowPlan = hasMainContent;

  // 持久化的追问选项本地状态 - 使用messageId作为key确保不会丢失
  const [selectedOptions, setSelectedOptions] = useState<Record<number, string[]>>({});
  const [customInputs, setCustomInputs] = useState<Record<number, string>>({});
  const [additionalRequirements, setAdditionalRequirements] = useState<string>("");

  // 选项点击切换高亮（单选，支持取消选中）
  function handleOptionClick(qIdx: number, oValue: string) {
    setSelectedOptions(prev => {
      const currentSelection = prev[qIdx] ?? [];
      // 如果当前选项已经被选中，则取消选中
      if (currentSelection.includes(oValue)) {
        return {
          ...prev,
          [qIdx]: []
        };
      } else {
        // 否则选中当前选项（单选）
        return {
          ...prev,
          [qIdx]: [oValue]
        };
      }
    });
  }
  // 自定义输入
  function handleCustomInput(qIdx: number, value: string) {
    setCustomInputs(prev => ({ ...prev, [qIdx]: value }));
  }
  // 判断是否有任意选项被选中或有自定义输入或额外需求
  const hasAnySelection = useMemo(() => {
    return (
      Object.values(selectedOptions).some(arr => arr.length > 0) ||
      Object.values(customInputs).some(v => v && v.trim() !== "") ||
      (additionalRequirements && additionalRequirements.trim() !== "")
    );
  }, [selectedOptions, customInputs, additionalRequirements]);

  // 组装补充信息
  function getSupplementInfo() {
    const arr: string[] = [];
    plan.clarification_questions_with_options?.forEach((q, qIdx) => {
      // 选项
      (selectedOptions[qIdx] ?? []).forEach(val => {
        const opt = q.options.find(o => o.value === val);
        if (opt) arr.push(`${q.question}: ${opt.text}`);
      });
      // 自定义
      if (customInputs[qIdx] && customInputs[qIdx].trim() !== "") {
        arr.push(`${q.question}: ${customInputs[qIdx]}`);
      }
    });
    // 添加额外需求
    if (additionalRequirements && additionalRequirements.trim() !== "") {
      arr.push(`Additional requirements: ${additionalRequirements}`);
    }
    return arr.join("\n");
  }

  // Edit按钮点击
  function handleEdit() {
    const supplement = getSupplementInfo();
    if (supplement && onSendMessage) {
      onSendMessage(`[CLARIFICATION_RESPONSE] ${supplement}`, { interruptFeedback: "clarification_response" });
    }
  }
  // Start Search按钮点击
  function handleStartSearch() {
    if (onSendMessage) {
      onSendMessage("[START_SEARCH]", { interruptFeedback: "accepted" });
    }
  }

  const handleAccept = useCallback(async () => {
    if (onSendMessage) {
      onSendMessage(
        `${GREETINGS[Math.floor(Math.random() * GREETINGS.length)]}! ${Math.random() > 0.5 ? "Let's get started." : "Let's start."}`,
        {
          interruptFeedback: "accepted",
        },
      );
    }
  }, [onSendMessage]);

  return (
    <div className={cn("w-full", className)}>
      {reasoningContent && (
        <ThoughtBlock
          content={reasoningContent}
          isStreaming={isThinking}
          hasMainContent={hasMainContent}
        />
      )}
      {shouldShowPlan && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        >
          <Card className="w-full">
            <CardHeader>
              <CardTitle>
                <Markdown animated={message.isStreaming}>
                  {`### ${plan.title !== undefined && plan.title !== "" ? plan.title : "Deep Research"}`}
                </Markdown>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Markdown className="opacity-80" animated={message.isStreaming}>
                {plan.thought}
              </Markdown>
              {plan.steps && plan.steps.length > 0 && (
                <ul className="my-2 flex list-decimal flex-col gap-4 border-l-[2px] pl-8">
                  {plan.steps.map((step, i) => (
                    <li key={`step-${i}`}>
                      <h3 className="mb text-lg font-medium">
                        <Markdown animated={message.isStreaming}>{step.title}</Markdown>
                      </h3>
                      <div className="text-muted-foreground text-sm">
                        <Markdown animated={message.isStreaming}>{step.description}</Markdown>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
              {/* 选项化追问多选/高亮/自定义输入 - 只有在消息不再流式传输时才显示 */}
              {!message.isStreaming && plan.clarification_questions_with_options && plan.clarification_questions_with_options.length > 0 && plan.clarification_questions_with_options.every(q => q.options) && (
                <div className="mt-4 p-4 rounded-md bg-blue-50 border border-blue-200 dark:bg-blue-900/20 dark:border-blue-800">
                  <h4 className="font-semibold mb-3 text-blue-900 dark:text-blue-100">
                    To get more accurate results, please provide additional information:
                  </h4>
                  <div className="space-y-4">
                    {plan.clarification_questions_with_options.map((question, qIdx) => (
                      <div 
                        key={`question-${qIdx}-${question.question.slice(0, 50)}`} 
                        className="space-y-2"
                      >
                        <h5 className="font-medium text-blue-900 dark:text-blue-100">
                          {question.question}
                          {question.required && <span className="text-red-500 ml-1">*</span>}
                        </h5>
                        <div className="flex flex-col gap-2">
                          {question.options?.map((option, oIdx) => {
                            const selected = selectedOptions[qIdx]?.includes(option.value);
                            return (
                              <Button
                                key={`option-${qIdx}-${oIdx}-${option.value}`}
                                variant={selected ? "default" : "outline"}
                                className={cn("justify-start text-left h-auto py-2 px-3", selected && "ring-2 ring-blue-400")}
                                onClick={() => handleOptionClick(qIdx, option.value)}
                              >
                                <div className="flex flex-col items-start">
                                  <span className="font-medium">{option.text}</span>
                                  {option.description && (
                                    <span className="text-xs text-muted-foreground mt-1">{option.description}</span>
                                  )}
                                </div>
                              </Button>
                            );
                          })}
                        </div>
                        {question.allow_custom_input && selectedOptions[qIdx]?.includes("other") && (
                          <div className="mt-2">
                            <input
                              type="text"
                              placeholder="Please specify your answer..."
                              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                              value={customInputs[qIdx] ?? ""}
                              onChange={e => handleCustomInput(qIdx, e.target.value)}
                            />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                  <div className="flex flex-col gap-2 mt-4">
                    {/* <div className="text-sm text-gray-600 dark:text-gray-400">
                      {!hasAnySelection 
                        ? "If you have additional requirements, you can also input them below:"
                        : "Based on your selections and additional requirements, we'll regenerate a more precise plan."
                      }
                    </div> */}
                    <div className="mt-2">
                      <input
                        type="text"
                        placeholder="Enter any additional requirements or preferences..."
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                        value={additionalRequirements}
                        onChange={e => setAdditionalRequirements(e.target.value)}
                      />
                    </div>
                    {/* <div className="flex justify-end gap-2">
                      {!hasAnySelection && (
                        <Button onClick={handleStartSearch} variant="default">Start Search</Button>
                      )}
                      {hasAnySelection && (
                        <Button onClick={handleEdit} variant="secondary">Edit</Button>
                      )}
                    </div> */}
                  </div>
                </div>
              )}
              {/* 如果没有选项化追问，显示传统的追问（向后兼容） */}
              {(!plan.clarification_questions_with_options || plan.clarification_questions_with_options.length === 0) && 
               plan.clarification_questions && plan.clarification_questions.length > 0 && (
                <div className="mt-4 p-3 rounded-md bg-yellow-50 border border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800">
                  <h4 className="font-semibold mb-2 text-yellow-900 dark:text-yellow-100">
                    LibrAI AI suggests that to get more accurate results, please provide some additional information:
                  </h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {plan.clarification_questions?.map((question, idx) => (
                      <li key={idx} className="text-sm text-yellow-900 dark:text-yellow-100">
                        {question}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-end">
              {!message.isStreaming && interruptMessage?.options?.length && (
                <motion.div
                  className="flex gap-2"
                  initial={{ opacity: 0, y: 12 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.3 }}
                >
                  {interruptMessage?.options?.map((option) => (
                    option.value!== "edit_plan" &&
                    <Button
                      key={option.value}
                      variant={
                        option.value === "accepted" ? "default" : "outline"
                      }
                      disabled={!waitForFeedback}
                      onClick={() => {
                        if (option.value === "accepted") {
                          void handleAccept();
                        } else {
                          onFeedback?.({
                            option,
                          });
                        }
                      }}
                    >
                      {option.text}
                    </Button>
                  ))}
                </motion.div>
              )}
            </CardFooter>
          </Card>
        </motion.div>
      )}
    </div>
  );
}

function PodcastCard({
  className,
  message,
}: {
  className?: string;
  message: Message;
}) {
  const data = useMemo(() => {
    return JSON.parse(message.content ?? "");
  }, [message.content]);
  const title = useMemo<string | undefined>(() => data?.title, [data]);
  const audioUrl = useMemo<string | undefined>(() => data?.audioUrl, [data]);
  const isGenerating = useMemo(() => {
    return message.isStreaming;
  }, [message.isStreaming]);
  const hasError = useMemo(() => {
    return data?.error !== undefined;
  }, [data]);
  const [isPlaying, setIsPlaying] = useState(false);
  return (
    <Card className={cn("w-[508px]", className)}>
      <CardHeader>
        <div className="text-muted-foreground flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            {isGenerating ? <LoadingOutlined /> : <Headphones size={16} />}
            {!hasError ? (
              <RainbowText animated={isGenerating}>
                {isGenerating
                  ? "Generating podcast..."
                  : isPlaying
                    ? "Now playing podcast..."
                    : "Podcast"}
              </RainbowText>
            ) : (
              <div className="text-red-500">
                Error when generating podcast. Please try again.
              </div>
            )}
          </div>
          {!hasError && !isGenerating && (
            <div className="flex">
              <Tooltip title="Download podcast">
                <Button variant="ghost" size="icon" asChild>
                  <a
                    href={audioUrl}
                    download={`${(title ?? "podcast").replaceAll(" ", "-")}.mp3`}
                  >
                    <Download size={16} />
                  </a>
                </Button>
              </Tooltip>
            </div>
          )}
        </div>
        <CardTitle>
          <div className="text-lg font-medium">
            <RainbowText animated={isGenerating}>{title}</RainbowText>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {audioUrl ? (
          <audio
            className="w-full"
            src={audioUrl}
            controls
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
          />
        ) : (
          <div className="w-full"></div>
        )}
      </CardContent>
    </Card>
  );
}
