// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { create } from "zustand";

// Workflow相关的状态类型定义
export interface WorkflowState {
  // 当前工作流状态
  isRunning: boolean;
  currentStep: string | null;
  progress: number; // 0-100
  
  // 任务相关
  currentTask: {
    id: string;
    title: string;
    description: string;
    type: 'research' | 'analysis' | 'report' | 'presentation';
  } | null;
  
  // PPT相关状态
  presentation: {
    id: string | null;
    title: string;
    slides: Array<{
      id: string;
      title: string;
      content: string;
      type: 'title' | 'content' | 'image';
    }>;
    designId: string | null;
    isGenerating: boolean;
  };
  
  // 历史记录
  history: Array<{
    id: string;
    title: string;
    timestamp: number;
    type: string;
  }>;
  
  // 节点和连接（用于工作流可视化）
  nodes: Array<{
    id: string;
    type: string;
    position: { x: number; y: number };
    data: Record<string, any>;
  }>;
  
  edges: Array<{
    id: string;
    source: string;
    target: string;
    type?: string;
  }>;
  
  // 表单数据
  formData: Record<string, any>;
  
  // 环境变量
  envVariables: Record<string, string>;
  
  // 聊天变量
  chatVariables: Record<string, any>;
  
  // 工具配置
  tools: Array<{
    id: string;
    name: string;
    enabled: boolean;
    config: Record<string, any>;
  }>;
  
  // 面板状态
  panels: {
    sidebar: boolean;
    properties: boolean;
    preview: boolean;
  };
  
  // 帮助线
  helpLines: {
    show: boolean;
    lines: Array<{ x?: number; y?: number; orientation: 'horizontal' | 'vertical' }>;
  };
  
  // 版本信息
  version: {
    current: string;
    history: Array<{
      version: string;
      timestamp: number;
      changes: string[];
    }>;
  };
  
  // 草稿状态
  draft: {
    hasChanges: boolean;
    lastSaved: number;
    autoSave: boolean;
  };
}

// Action接口定义
export interface WorkflowActions {
  // 工作流控制
  startWorkflow: (taskId: string) => void;
  stopWorkflow: () => void;
  pauseWorkflow: () => void;
  resumeWorkflow: () => void;
  setCurrentStep: (step: string) => void;
  setProgress: (progress: number) => void;
  
  // 任务管理
  setCurrentTask: (task: WorkflowState['currentTask']) => void;
  clearCurrentTask: () => void;
  
  // PPT管理
  createPresentation: (title: string) => void;
  updatePresentationTitle: (title: string) => void;
  addSlide: (slide: WorkflowState['presentation']['slides'][0]) => void;
  removeSlide: (slideId: string) => void;
  updateSlide: (slideId: string, updates: Partial<WorkflowState['presentation']['slides'][0]>) => void;
  setPresentationDesignId: (designId: string) => void;
  setGeneratingPresentation: (isGenerating: boolean) => void;
  clearPresentation: () => void;
  
  // 历史管理
  addToHistory: (item: Omit<WorkflowState['history'][0], 'id' | 'timestamp'>) => void;
  clearHistory: () => void;
  removeFromHistory: (id: string) => void;
  
  // 节点和边管理
  addNode: (node: WorkflowState['nodes'][0]) => void;
  removeNode: (nodeId: string) => void;
  updateNode: (nodeId: string, updates: Partial<WorkflowState['nodes'][0]>) => void;
  addEdge: (edge: WorkflowState['edges'][0]) => void;
  removeEdge: (edgeId: string) => void;
  
  // 表单数据
  setFormData: (data: Record<string, any>) => void;
  updateFormField: (field: string, value: any) => void;
  clearFormData: () => void;
  
  // 环境变量
  setEnvVariable: (key: string, value: string) => void;
  removeEnvVariable: (key: string) => void;
  clearEnvVariables: () => void;
  
  // 聊天变量
  setChatVariable: (key: string, value: any) => void;
  removeChatVariable: (key: string) => void;
  clearChatVariables: () => void;
  
  // 工具管理
  addTool: (tool: WorkflowState['tools'][0]) => void;
  removeTool: (toolId: string) => void;
  updateTool: (toolId: string, updates: Partial<WorkflowState['tools'][0]>) => void;
  toggleTool: (toolId: string) => void;
  
  // 面板控制
  togglePanel: (panel: keyof WorkflowState['panels']) => void;
  setPanelState: (panel: keyof WorkflowState['panels'], state: boolean) => void;
  
  // 帮助线
  showHelpLines: () => void;
  hideHelpLines: () => void;
  addHelpLine: (line: WorkflowState['helpLines']['lines'][0]) => void;
  clearHelpLines: () => void;
  
  // 版本管理
  setVersion: (version: string) => void;
  addVersionToHistory: (version: string, changes: string[]) => void;
  
  // 草稿管理
  markAsChanged: () => void;
  markAsSaved: () => void;
  toggleAutoSave: () => void;
  
  // 重置所有状态
  resetWorkflow: () => void;
}

// 默认状态
const defaultState: WorkflowState = {
  isRunning: false,
  currentStep: null,
  progress: 0,
  currentTask: null,
  presentation: {
    id: null,
    title: '',
    slides: [],
    designId: null,
    isGenerating: false,
  },
  history: [],
  nodes: [],
  edges: [],
  formData: {},
  envVariables: {},
  chatVariables: {},
  tools: [],
  panels: {
    sidebar: true,
    properties: false,
    preview: false,
  },
  helpLines: {
    show: false,
    lines: [],
  },
  version: {
    current: '1.0.0',
    history: [],
  },
  draft: {
    hasChanges: false,
    lastSaved: Date.now(),
    autoSave: true,
  },
};

// 创建Zustand store
export const useWorkflowStore = create<WorkflowState & WorkflowActions>((set, get) => ({
  ...defaultState,

  // 工作流控制
  startWorkflow: (taskId: string) => {
    set({ isRunning: true, currentStep: 'initializing', progress: 0 });
  },

  stopWorkflow: () => {
    set({ isRunning: false, currentStep: null, progress: 0 });
  },

  pauseWorkflow: () => {
    set({ isRunning: false });
  },

  resumeWorkflow: () => {
    set({ isRunning: true });
  },

  setCurrentStep: (step: string) => {
    set({ currentStep: step });
  },

  setProgress: (progress: number) => {
    set({ progress: Math.max(0, Math.min(100, progress)) });
  },

  // 任务管理
  setCurrentTask: (task) => {
    set({ currentTask: task });
  },

  clearCurrentTask: () => {
    set({ currentTask: null });
  },

  // PPT管理
  createPresentation: (title: string) => {
    const id = `ppt_${Date.now()}`;
    set((state) => ({
      presentation: {
        ...state.presentation,
        id,
        title,
        slides: [],
      },
    }));
  },

  updatePresentationTitle: (title: string) => {
    set((state) => ({
      presentation: { ...state.presentation, title },
    }));
  },

  addSlide: (slide) => {
    set((state) => ({
      presentation: {
        ...state.presentation,
        slides: [...state.presentation.slides, slide],
      },
    }));
  },

  removeSlide: (slideId: string) => {
    set((state) => ({
      presentation: {
        ...state.presentation,
        slides: state.presentation.slides.filter(s => s.id !== slideId),
      },
    }));
  },

  updateSlide: (slideId: string, updates) => {
    set((state) => ({
      presentation: {
        ...state.presentation,
        slides: state.presentation.slides.map(slide =>
          slide.id === slideId ? { ...slide, ...updates } : slide
        ),
      },
    }));
  },

  setPresentationDesignId: (designId: string) => {
    set((state) => ({
      presentation: { ...state.presentation, designId },
    }));
  },

  setGeneratingPresentation: (isGenerating: boolean) => {
    set((state) => ({
      presentation: { ...state.presentation, isGenerating },
    }));
  },

  clearPresentation: () => {
    set((state) => ({
      presentation: {
        id: null,
        title: '',
        slides: [],
        designId: null,
        isGenerating: false,
      },
    }));
  },

  // 历史管理
  addToHistory: (item) => {
    const historyItem = {
      ...item,
      id: `history_${Date.now()}`,
      timestamp: Date.now(),
    };
    set((state) => ({
      history: [historyItem, ...state.history].slice(0, 100), // 限制历史记录数量
    }));
  },

  clearHistory: () => {
    set({ history: [] });
  },

  removeFromHistory: (id: string) => {
    set((state) => ({
      history: state.history.filter(item => item.id !== id),
    }));
  },

  // 节点和边管理
  addNode: (node) => {
    set((state) => ({
      nodes: [...state.nodes, node],
    }));
  },

  removeNode: (nodeId: string) => {
    set((state) => ({
      nodes: state.nodes.filter(node => node.id !== nodeId),
      edges: state.edges.filter(edge => 
        edge.source !== nodeId && edge.target !== nodeId
      ),
    }));
  },

  updateNode: (nodeId: string, updates) => {
    set((state) => ({
      nodes: state.nodes.map(node =>
        node.id === nodeId ? { ...node, ...updates } : node
      ),
    }));
  },

  addEdge: (edge) => {
    set((state) => ({
      edges: [...state.edges, edge],
    }));
  },

  removeEdge: (edgeId: string) => {
    set((state) => ({
      edges: state.edges.filter(edge => edge.id !== edgeId),
    }));
  },

  // 表单数据
  setFormData: (data) => {
    set({ formData: data });
  },

  updateFormField: (field: string, value: any) => {
    set((state) => ({
      formData: { ...state.formData, [field]: value },
    }));
  },

  clearFormData: () => {
    set({ formData: {} });
  },

  // 环境变量
  setEnvVariable: (key: string, value: string) => {
    set((state) => ({
      envVariables: { ...state.envVariables, [key]: value },
    }));
  },

  removeEnvVariable: (key: string) => {
    set((state) => {
      const { [key]: removed, ...rest } = state.envVariables;
      return { envVariables: rest };
    });
  },

  clearEnvVariables: () => {
    set({ envVariables: {} });
  },

  // 聊天变量
  setChatVariable: (key: string, value: any) => {
    set((state) => ({
      chatVariables: { ...state.chatVariables, [key]: value },
    }));
  },

  removeChatVariable: (key: string) => {
    set((state) => {
      const { [key]: removed, ...rest } = state.chatVariables;
      return { chatVariables: rest };
    });
  },

  clearChatVariables: () => {
    set({ chatVariables: {} });
  },

  // 工具管理
  addTool: (tool) => {
    set((state) => ({
      tools: [...state.tools, tool],
    }));
  },

  removeTool: (toolId: string) => {
    set((state) => ({
      tools: state.tools.filter(tool => tool.id !== toolId),
    }));
  },

  updateTool: (toolId: string, updates) => {
    set((state) => ({
      tools: state.tools.map(tool =>
        tool.id === toolId ? { ...tool, ...updates } : tool
      ),
    }));
  },

  toggleTool: (toolId: string) => {
    set((state) => ({
      tools: state.tools.map(tool =>
        tool.id === toolId ? { ...tool, enabled: !tool.enabled } : tool
      ),
    }));
  },

  // 面板控制
  togglePanel: (panel) => {
    set((state) => ({
      panels: {
        ...state.panels,
        [panel]: !state.panels[panel],
      },
    }));
  },

  setPanelState: (panel, panelState) => {
    set((state) => ({
      panels: {
        ...state.panels,
        [panel]: panelState,
      },
    }));
  },

  // 帮助线
  showHelpLines: () => {
    set((state) => ({
      helpLines: { ...state.helpLines, show: true },
    }));
  },

  hideHelpLines: () => {
    set((state) => ({
      helpLines: { ...state.helpLines, show: false },
    }));
  },

  addHelpLine: (line) => {
    set((state) => ({
      helpLines: {
        ...state.helpLines,
        lines: [...state.helpLines.lines, line],
      },
    }));
  },

  clearHelpLines: () => {
    set((state) => ({
      helpLines: {
        ...state.helpLines,
        lines: [],
      },
    }));
  },

  // 版本管理
  setVersion: (version: string) => {
    set((state) => ({
      version: { ...state.version, current: version },
    }));
  },

  addVersionToHistory: (version: string, changes: string[]) => {
    const versionItem = {
      version,
      timestamp: Date.now(),
      changes,
    };
    set((state) => ({
      version: {
        ...state.version,
        history: [versionItem, ...state.version.history].slice(0, 50),
      },
    }));
  },

  // 草稿管理
  markAsChanged: () => {
    set((state) => ({
      draft: { ...state.draft, hasChanges: true },
    }));
  },

  markAsSaved: () => {
    set((state) => ({
      draft: {
        ...state.draft,
        hasChanges: false,
        lastSaved: Date.now(),
      },
    }));
  },

  toggleAutoSave: () => {
    set((state) => ({
      draft: { ...state.draft, autoSave: !state.draft.autoSave },
    }));
  },

  // 重置所有状态
  resetWorkflow: () => {
    set(defaultState);
  },
}));

// 导出一些便捷的选择器钩子
export const useWorkflowState = () => useWorkflowStore((state) => ({
  isRunning: state.isRunning,
  currentStep: state.currentStep,
  progress: state.progress,
}));

export const useCurrentTask = () => useWorkflowStore((state) => state.currentTask);

export const usePresentation = () => useWorkflowStore((state) => state.presentation);

export const useWorkflowHistory = () => useWorkflowStore((state) => state.history);

export const useWorkflowNodes = () => useWorkflowStore((state) => state.nodes);

export const useWorkflowEdges = () => useWorkflowStore((state) => state.edges);

export const useWorkflowTools = () => useWorkflowStore((state) => state.tools);

export const usePanelState = () => useWorkflowStore((state) => state.panels);

export const useFormData = () => useWorkflowStore((state) => state.formData);

export const useEnvVariables = () => useWorkflowStore((state) => state.envVariables);

export const useChatVariables = () => useWorkflowStore((state) => state.chatVariables);

export const useDraftState = () => useWorkflowStore((state) => state.draft);

export const useVersionInfo = () => useWorkflowStore((state) => state.version);

// 导出store实例用于外部访问
export const workflowStore = useWorkflowStore;
