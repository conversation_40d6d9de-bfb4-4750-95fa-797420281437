// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { env } from "~/env";

// Cache for runtime config
let runtimeConfigCache: { apiUrl: string } | null = null;

export function resolveServiceURL(path: string) {
  // Server-side: use env module which handles the process.env access
  if (typeof window === "undefined") {
    const BASE_URL = env.NEXT_PUBLIC_API_URL ?? "http://localhost:8000/api/";
    return buildURL(BASE_URL, path);
  }
  
  // Client-side: try multiple sources
  let BASE_URL: string;
  
  // 1. Try runtime config from window
  const windowConfig = (window as unknown as Record<string, unknown>).__runtimeConfig as { apiUrl?: string } | undefined;
  if (windowConfig?.apiUrl) {
    BASE_URL = windowConfig.apiUrl;
  }
  // 2. Try cached config from API
  else if (runtimeConfigCache?.apiUrl) {
    BASE_URL = runtimeConfigCache.apiUrl;
  }
  // 3. Fall back to build-time value
  else {
    BASE_URL = env.NEXT_PUBLIC_API_URL ?? "http://localhost:8000/api/";
    
    // Async fetch runtime config for future use
    void fetchRuntimeConfig();
  }
  
  // Debug log (remove this after testing)
  console.log("Window config apiUrl:", windowConfig?.apiUrl);
  console.log("Cached config apiUrl:", runtimeConfigCache?.apiUrl);
  console.log("Build-time NEXT_PUBLIC_API_URL:", env.NEXT_PUBLIC_API_URL);
  console.log("Final resolved BASE_URL:", BASE_URL);
  
  return buildURL(BASE_URL, path);
}

function buildURL(baseUrl: string, path: string): string {
  let BASE_URL = baseUrl;
  
  // Add protocol if missing
  if (!BASE_URL.startsWith("http://") && !BASE_URL.startsWith("https://")) {
    BASE_URL = `https://${BASE_URL}`;
  }
  
  // Add /api/ if missing (but only if it doesn't already contain /api/ or end with /api)
  if (!BASE_URL.includes("/api/") && !BASE_URL.endsWith("/api")) {
    if (!BASE_URL.endsWith("/")) {
      BASE_URL += "/";
    }
    BASE_URL += "api/";
  }
  
  // Fix: if BASE_URL ends with /api (without trailing slash), add trailing slash
  if (BASE_URL.endsWith("/api")) {
    BASE_URL += "/";
  }
  
  // Ensure BASE_URL ends with /
  if (!BASE_URL.endsWith("/")) {
    BASE_URL += "/";
  }
  
  // Ensure BASE_URL is valid
  try {
    new URL(BASE_URL);
  } catch {
    console.warn("Invalid base URL, falling back to localhost:", BASE_URL);
    BASE_URL = "http://localhost:8000/api/";
  }
  
  try {
    const finalUrl = new URL(path, BASE_URL).toString();
    console.log("URL building debug:", { baseUrl, path, BASE_URL, finalUrl });
    return finalUrl;
  } catch {
    console.warn("Failed to build URL, falling back to localhost");
    return `http://localhost:8000/api/${path}`;
  }
}

async function fetchRuntimeConfig() {
  try {
    const response = await fetch('/api/config');
    const config = await response.json();
    runtimeConfigCache = config;
    console.log("Fetched runtime config:", config);
  } catch (error) {
    console.error("Failed to fetch runtime config:", error);
  }
}
