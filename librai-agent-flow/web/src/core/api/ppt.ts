// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { resolveServiceURL } from "./resolve-service-url";

export interface CreatePPTTaskParams {
  type: 1|3|4|5|6|7|8|9|10|11|12|16|17|18;
  title?: string;
  content?: string;
}

export interface CreatePPTTaskResponse {
  code: number;
  data: {
    id: string;
  };
  msg?: string;
}

export interface GetPPTContentResponse {
  code: number; // 返回码, 0表示成功, 非0表示异常
  data: string; // ticket
  msg: string;
}

export interface CheckPPTContentResponse {
  code: number;
  data?: {
    status: 'pending' | 'processing' | 'completed' | 'failed' | number;
    progress?: number;
    result?: any;
    error?: string;
  };
  msg: string;
}

export interface GetPPTStructureResponse {
  code: number;
  data?: {
    outline: any; // PPT树形结构数据
  };
  msg: string;
}

export interface SavePPTWorkParams {
  task_id: number;
  template_id: number;
  name?: string;
  template_type?: 1 | 2; // 1: 系统模板, 2: 企业模板
}

export interface SavePPTWorkResponse {
  code: number;
  data?: {
    work_id: number; // 作品ID
    id: number; // 设计ID
    cover_url?: string; // 封面URL
  };
  msg: string;
}

export interface GetPPTWorkListParams {
  order?: number; // 排序 1最新创建 2最近修改，默认1
  page?: number; // 页码，默认1
  page_size?: number; // 每页显示数量，默认20
}

export interface PPTWorkItem {
  id: number;
  name: string;
  created_at: string;
  updated_at: string;
  // 可以根据实际返回数据补充更多字段
}

export interface GetPPTWorkListResponse {
  code: number;
  data?: {
    total: number;
    list: PPTWorkItem[];
  };
  msg: string;
}

export interface GetPPTWorkDetailResponse {
  code: number;
  data?: {
    id: number;
    name: string;
    created_at: string;
    updated_at: string;
    template_id: number;
    template_type: number;
    content: any; // PPT内容数据
    // 可以根据实际返回数据补充更多字段
  };
  msg: string;
}

export interface GetTemplateSuiteResponse {
  code: number;
  data?: {
    list: Array<{
      id: number;
      name: string;
      cover: string;  // 封面图片URL
      preview_images: string[];  // 预览图片URL列表
      description?: string;
      template_type: 1 | 2;  // 1: 系统模板, 2: 企业模板
    }>;
    total: number;
  };
  msg: string;
}

export interface GetPPTDesignDetailResponse {
  code: number;
  data?: {
    id: number;
    name: string;
    created_at: string;
    updated_at: string;
    template_id: number;
    template_type: number;
    content: any; // PPT内容数据
    preview_url?: string; // 预览URL
  };
  msg: string;
}

export interface ExportPPTParams {
  id: number;        // 作品ID
  format: 'png'|'jpeg'|'pdf'|'ppt';  // 导出格式
  edit?: string;     // 导出的作品是否可编辑，true=可编辑，false=不可编辑
  files_to_zip?: string;  // 导出的图片是否压缩为zip，true=不压缩，false=压缩
}

export interface ExportPPTResponse {
  code: number;
  data?: {
    task_key: string;    // 导出任务的标识
    url?: string;     // 导出文件的下载链接
  };
  msg: string;
}

export interface CheckExportResultParams {
  task_key: string;   // 作品导出的任务标识
}

export interface CheckExportResultResponse {
  code: number;
  data?: {
    status?: string;  // 导出状态
    url?: string;     // 导出文件下载地址
  };
  msg: string;
}

/**
 * 创建PPT生成任务
 * @param params 任务参数
 * @returns 返回任务ID
 */
export async function createPPTTask(params: CreatePPTTaskParams): Promise<CreatePPTTaskResponse> {
  const response = await fetch(resolveServiceURL('aippt/api/ai/chat/v2/task'), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    throw new Error(`创建PPT任务失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取PPT大纲 (仅支持智能生成模式 type=1)
 * @param taskId 任务ID
 * @returns 返回大纲内容流
 */
export async function getPPTOutline(taskId: number): Promise<Response> {
  const url = new URL(resolveServiceURL('aippt/api/ai/chat/outline'));
  url.searchParams.set('task_id', taskId.toString());

  const response = await fetch(url.toString(), {
    method: 'GET',
    headers: {
      'Accept': 'text/event-stream',
    },
  });

  if (!response.ok) {
    throw new Error(`获取PPT大纲失败: ${response.statusText}`);
  }

  return response;
}

/**
 * 获取PPT内容生成的ticket
 * @param taskId 任务ID
 * @returns 返回ticket
 */
export async function getPPTContent(taskId: number): Promise<GetPPTContentResponse> {
  const url = new URL(resolveServiceURL('aippt/api/ai/chat/v2/content'));
  url.searchParams.set('task_id', taskId.toString());

  const response = await fetch(url.toString(), {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`获取PPT内容失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 查询PPT内容生成结果
 * @param ticket 从getPPTContent获取的ticket
 * @returns 返回生成结果
 */
export async function checkPPTContent(ticket: string): Promise<CheckPPTContentResponse> {
  const url = new URL(resolveServiceURL('aippt/api/ai/chat/v2/content/check'));
  url.searchParams.set('ticket', ticket);

  const response = await fetch(url.toString(), {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`查询PPT内容生成结果失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取任务的PPT树形结构
 * @param taskId 任务ID
 * @returns 返回PPT树形结构
 */
export async function getPPTStructure(taskId: number): Promise<GetPPTStructureResponse> {
  const response = await fetch(resolveServiceURL('aippt/api/generate/data'), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      task_id: taskId,
    }),
  });

  if (!response.ok) {
    throw new Error(`获取PPT结构失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 保存PPT作品
 * @param params 保存参数
 * @returns 返回作品ID
 */
export async function savePPTWork(params: SavePPTWorkParams): Promise<SavePPTWorkResponse> {
  const response = await fetch(resolveServiceURL('aippt/api/design/v2/save'), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    throw new Error(`保存PPT作品失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取PPT作品列表
 * @param params 查询参数
 * @returns 返回作品列表
 */
export async function getPPTWorkList(params?: GetPPTWorkListParams): Promise<GetPPTWorkListResponse> {
  const url = new URL(resolveServiceURL('aippt/api/design/list'));
  
  // 设置查询参数
  url.searchParams.set('order', (params?.order || 1).toString());
  url.searchParams.set('page', (params?.page || 1).toString());
  url.searchParams.set('page_size', (params?.page_size || 20).toString());

  const response = await fetch(url.toString(), {
    method: 'GET',
  });

  if (!response.ok) {
    throw new Error(`获取PPT作品列表失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取PPT作品详情
 * @param userDesignId 作品ID
 * @returns 返回作品详情
 */
export async function getPPTWorkDetail(userDesignId: number): Promise<GetPPTWorkDetailResponse> {
  const url = new URL(resolveServiceURL('aippt/api/design/info'));
  url.searchParams.set('user_design_id', userDesignId.toString());

  const response = await fetch(url.toString(), {
    method: 'GET',
  });

  if (!response.ok) {
    throw new Error(`获取PPT作品详情失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取模板套装列表
 * @returns 返回模板套装列表
 */
export async function getTemplateSuiteList(): Promise<GetTemplateSuiteResponse> {
  const response = await fetch(resolveServiceURL('aippt/api/template_component/suit/search'), {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`获取模板套装列表失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取PPT设计详情
 * @param userDesignId 设计ID
 * @returns 返回设计详情
 */
export async function getPPTDesignDetail(userDesignId: number): Promise<GetPPTDesignDetailResponse> {
  const url = new URL(resolveServiceURL('aippt/api/design/detail'));
  url.searchParams.set('user_design_id', userDesignId.toString());

  const response = await fetch(url.toString(), {
    method: 'GET',
  });

  if (!response.ok) {
    throw new Error(`获取PPT设计详情失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 导出PPT作品
 * @param params 导出参数
 * @returns 返回导出文件的下载链接
 */
export async function exportPPT(params: ExportPPTParams): Promise<ExportPPTResponse> {
  const response = await fetch(resolveServiceURL('aippt/api/download/export/file'), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    throw new Error(`导出PPT失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 查询PPT导出结果
 * @param params 查询参数
 * @returns 返回导出结果状态和下载地址
 */
export async function checkExportResult(params: CheckExportResultParams): Promise<CheckExportResultResponse> {
  const response = await fetch(resolveServiceURL('aippt/api/download/export/file/result'), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    throw new Error(`查询PPT导出结果失败: ${response.statusText}`);
  }

  return response.json();
}


 