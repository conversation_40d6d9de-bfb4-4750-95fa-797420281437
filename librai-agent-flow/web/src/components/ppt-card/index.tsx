import React, { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import PresentationPreview from "./presentation-preview";
import { exportPPT, checkExportResult } from "../../core/api/ppt";
import { useWorkflowStore } from "../../core/store/workflow";
import { FullScreen, useFullScreenHandle } from "react-full-screen";

// 内联SVG图标组件
const OpenIcon = ({ className, onClick }: { className?: string; onClick?: () => void }) => (
  <svg 
    width="24" 
    height="24" 
    viewBox="0 0 24 24" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg" 
    className={className}
    onClick={onClick}
    style={{ cursor: 'pointer' }}
  >
    <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const DownloadIcon = ({ className, onClick }: { className?: string; onClick?: () => void }) => (
  <svg 
    width="24" 
    height="24" 
    viewBox="0 0 24 24" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg" 
    className={className}
    onClick={onClick}
    style={{ cursor: 'pointer' }}
  >
    <path d="M21 15V19A2 2 0 0 1 19 21H5A2 2 0 0 1 3 19V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <polyline points="7,10 12,15 17,10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

interface PPTCardProps {
  title?: string;
  pptUrl?: string;
  user_design_id?: number;
  pptStatus?: number;
  content?: string; // 添加content属性
  onDownload?: () => void;
  className?: string;
}

const PPTCard: React.FC<PPTCardProps> = ({
  title = "",
  pptUrl,
  user_design_id,
  pptStatus,
  content,
  onDownload,
  className = "",
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isFullscreenOpen, setIsFullscreenOpen] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const presentationId = useWorkflowStore((state) => state.presentation.id);

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const handleFullscreenOpen = () => {
    setIsFullscreenOpen(true);
  };

  const handleFullscreenClose = () => {
    setIsFullscreenOpen(false);
  };

  // console.log("content", content);
  const checkResult = async (task_key: string) => {
    try {
      const result = await checkExportResult({ task_key });
      if (result.code === 0 && result?.data?.url) {
        // 创建一个临时的a标签来下载文件
        const link = document.createElement("a");
        link.href = result.data.url;
        link.download = "presentation.ppt";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        return true;
      } else if (result.code === 0) {
        // 继续轮询
        return false;
      } else {
        // 出错了
        console.error("导出失败:", result);
        return true;
      }
    } catch (error) {
      console.error("检查导出结果失败:", error);
      return true;
    }
  };

  const handleDownload = async () => {
    if (presentationId) {
      try {
        setIsDownloading(true);
        const response = await exportPPT({
          id: parseInt(presentationId),
          format: "ppt",
          edit: "true",
          files_to_zip: "true",
        });

        if (response.code === 0 && response?.data?.task_key) {
          checkResult(response?.data?.task_key);
        }
      } catch (error) {
        console.error("下载PPT失败:", error);
        setIsDownloading(false);
      }
    } else if (onDownload) {
      onDownload();
    }
  };

  const handle = useFullScreenHandle();

  return (
    <div
      className={`bg-white rounded-xl shadow-md p-[16px] w-[650px] h-[460px] border border-[#E8E9FF] text-[#101828] flex flex-col ${className}`}
    >
      <div className="text-sm leading-[21px] mb-[16px]">{title}</div>
      <div className="rounded-lg overflow-hidden mb-[16px] border border-[#EFEFF3] bg-[#F7F7FC] p-[4px] flex-1">
        <div className="w-full h-full rounded-lg overflow-hidden">
          <FullScreen handle={handle} className="w-full h-full">
            <PresentationPreview 
              code={presentationId?.toString()} 
              content={content}
            />
          </FullScreen>
        </div>
      </div>
      <div className="flex justify-end gap-[12px]">
        <OpenIcon
          className="w-[24px] h-[24px] cursor-pointer"
          onClick={handle.enter}
        />
      </div>
    </div>
  );
};

export default PPTCard;
