"use client";

import React, { useEffect, useRef, useState } from "react";
// import { LoadingAnimation } from "../deer-flow/loading-animation";
import { useWorkflowStore } from "../../core/store/workflow";

// 声明全局AipptIframe类型
declare global {
  interface Window {
    AipptIframe: {
      show: (options: {
        appkey: string;
        channel: string;
        code: string;
        container: HTMLElement;
        editorModel?: boolean;
        onMessage?: (eventType: string, data: any) => void;
        options?: any;
      }) => Promise<void>;
      deleteIframe: () => void;
    };
  }
}

export type PresentationPreviewProps = {
  fileUrl?: string;
  appkey?: string;
  channel?: string;
  code?: string;
  editorModel?: boolean;
  content?: string;
  onMessage?: (eventType: string, data: any) => void;
};

const PresentationPreview: React.FC<PresentationPreviewProps> = ({
  fileUrl,
  appkey = "68676eab1d5e6",
  channel = "",
  code = "e9178e5de42cfcce8b1e3c4d5a5df0b7",
  editorModel = true,
  content: contentProp,
  onMessage,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const setPresentationDesignId = useWorkflowStore((state) => state.setPresentationDesignId);
  const createPresentation = useWorkflowStore((state) => state.createPresentation);

  // 动态加载AiPPT SDK脚本
  useEffect(() => {
    // 如果AipptIframe已经存在，直接标记为已加载
    if (window.AipptIframe) {
      setIsScriptLoaded(true);
      return;
    }

    const script = document.createElement("script");
    script.src =
      "https://aippt-international-api-static.aippt.cn/aippt-iframe-sdk.js";

    // script.defer = true;

    script.onload = () => {
      setIsScriptLoaded(true);
    };

    script.onerror = () => {
      setError("Failed to load AiPPT SDK");
    };

    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, []);

  const defaultContent = `# 日本七日游旅行计划

## 行程概览
- **目的地**: 日本（东京-京都-大阪）
- **出行时间**: 7天6晚
- **最佳季节**: 春季（3-5月）樱花季或秋季（9-11月）红叶季
- **预算范围**: 8000-12000人民币/人

## 第一天：抵达东京
### 上午
- 抵达成田/羽田机场
- 购买JR Pass及交通卡
- 前往酒店办理入住

### 下午
- 浅草寺参观
- 仲见世通商店街购物
- 品尝传统日式料理

### 晚上
- 东京塔夜景观赏
- 银座区域漫步

## 第二天：东京市区游览
### 上午
- 明治神宫参拜
- 原宿竹下通体验潮流文化
- 表参道购物

### 下午
- 上野公园及上野动物园
- 东京国立博物馆

### 晚上
- 新宿歌舞伎町
- 品尝居酒屋文化

## 第三天：东京周边
### 全天
- 镰仓一日游
- 镰仓大佛
- 江之岛海岸
- 小町通古街

## 第四天：前往京都
### 上午
- 搭乘新干线前往京都
- 酒店入住

### 下午
- 清水寺参观
- 三年坂二年坂漫步
- 祇园古街体验

### 晚上
- 传统怀石料理体验

## 第五天：京都文化之旅
### 上午
- 金阁寺（鹿苑寺）
- 龙安寺石庭

### 下午
- 岚山竹林小径
- 天龙寺
- 渡月桥

### 晚上
- 京都站周边购物

## 第六天：前往大阪
### 上午
- 前往大阪
- 大阪城参观

### 下午
- 心斋桥购物
- 道顿堀美食街

### 晚上
- 梅田空中庭园展望台
- 新世界通天阁

## 第七天：离别大阪
### 上午
- 最后购物时光
- 关西机场免税店

### 下午
- 搭乘航班返程

## 实用信息
### 交通
- JR Pass 7日券：约1800元
- 市内交通：地铁一日券
- 新干线：东京-京都-大阪

### 住宿推荐
- **东京**: 新宿/涩谷区域商务酒店
- **京都**: 京都站附近传统旅馆
- **大阪**: 心斋桥/梅田区域酒店

### 必备物品
- 护照及签证
- 日元现金及信用卡
- 便携WiFi或手机漫游
- 舒适的步行鞋
- 防晒用品

### 美食推荐
- **东京**: 筑地市场海鲜、拉面、寿司
- **京都**: 怀石料理、抹茶甜品、豆腐料理
- **大阪**: 章鱼烧、大阪烧、串炸

### 购物指南
- **电子产品**: 秋叶原、BIC Camera
- **时尚服饰**: 原宿、表参道、心斋桥
- **传统工艺**: 浅草、京都古街
- **药妆**: 松本清、唐吉坷德

## 注意事项
- 遵守当地礼仪和规则
- 垃圾分类投放
- 电车内保持安静
- 准备充足现金（部分店铺不接受信用卡）
- 提前预订热门餐厅和景点门票`;

  // 初始化AiPPT iframe
  useEffect(() => {
    if (isScriptLoaded && containerRef.current && code && appkey) {
      setLoading(true);
      const initializeAiPPT = async () => {
        try {
          await window.AipptIframe.show({
            appkey,
            channel,
            code,
            container: containerRef.current!,
            editorModel: false,
            options: {
              fc_plate: [2014],
              custom_generate: {
                step: 2,
                type: 7,
                content: contentProp || defaultContent,
              },
            },

            onMessage: (eventType, data) => {
              // console.log("AiPPT Event:", eventType, data);
              if (eventType === "START") {
                setLoading(false);
              }
              if (eventType === "GENERATE_PPT_SUCCESS") {
                // console.log("CREATE_TASK_SUCCESS", data);
                
                setPresentationDesignId(data.id);
                createPresentation(data.title || "Generated Presentation");
              }
              // if (onMessage) onMessage(eventType, data);
            },
          });
        } catch (e: any) {
          setLoading(false);
          console.error("AiPPT initialization error:", e);
          // setError(e.msg || "Failed to initialize AiPPT");
        }
      };

      initializeAiPPT();
    }
  }, [
    isScriptLoaded,
    code,
    appkey,
    channel,
    editorModel,
    contentProp,
    onMessage,
    setPresentationDesignId,
    createPresentation,
  ]);

  return (
    <div className="relative h-full w-full bg-white">
      {/* {loading && <LoadingAnimation />} */}
      <div
        ref={containerRef}
        className="h-full w-full"
        style={{
          minHeight: "400px",
          visibility: loading ? "hidden" : "visible",
        }}
      />
    </div>
  );
};

export default PresentationPreview;
