"use client";

import React, { useState, useEffect } from "react";
import <PERSON><PERSON> from "lottie-react";

const LoadingAnimation: React.FC = () => {
  const [animationData, setAnimationData] = useState<any>(null);

  useEffect(() => {
    fetch("/loading/AI agent loader.json")
      .then((response) => response.json())
      .then(setAnimationData)
      .catch((error) => {
        console.error("Failed to load animation:", error);
      });
  }, []);

  return (
    <div className="w-full h-full flex items-center justify-center bg-[#F7F7FC] rounded-lg">
      <div className="flex flex-col items-center gap-4">
        {animationData ? (
          <Lottie
            animationData={animationData}
            loop={true}
            autoplay={true}
            style={{ width: 120, height: 120 }}
            className="drop-shadow-lg"
          />
        ) : (
          <div className="relative">
            <div
              className="h-24 w-24 animate-spin rounded-full border-4 border-transparent border-t-[#5C62FF]"
              style={{ animationDuration: "1s" }}
            ></div>
            <div className="absolute inset-6 h-12 w-12 animate-pulse rounded-full bg-[#5C62FF] opacity-80"></div>
            <div className="absolute inset-8 h-8 w-8 animate-ping rounded-full bg-white"></div>
          </div>
        )}
        <div className="text-sm font-medium text-[#676F83]">
          {/* Generating your PPT... */}
        </div>
      </div>
    </div>
  );
};

export default LoadingAnimation;
