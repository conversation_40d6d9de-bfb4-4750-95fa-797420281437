import React, { useEffect, useState } from "react";

// 内联SVG组件，避免导入问题
const HotelIcon = ({ className }: { className?: string }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
    <path d="M12.125 5.375H3.5V4.25C3.5 4.15054 3.46049 4.05516 3.39017 3.98484C3.31984 3.91451 3.22446 3.875 3.125 3.875C3.02554 3.875 2.93016 3.91451 2.85983 3.98484C2.78951 4.05516 2.75 4.15054 2.75 4.25V11.75C2.75 11.8495 2.78951 11.9448 2.85983 12.0152C2.93016 12.0855 3.02554 12.125 3.125 12.125C3.22446 12.125 3.31984 12.0855 3.39017 12.0152C3.46049 11.9448 3.5 11.8495 3.5 11.75V10.25H13.25V11.75C13.25 11.8495 13.2895 11.9448 13.3598 12.0152C13.4302 12.0855 13.5255 12.125 13.625 12.125C13.7245 12.125 13.8198 12.0855 13.8902 12.0152C13.9605 11.9448 14 11.8495 14 11.75V7.25C14 6.75272 13.8025 6.27581 13.4508 5.92417C13.0992 5.57254 12.6223 5.375 12.125 5.375ZM3.5 6.125H6.875V9.5H3.5V6.125Z" fill="#17B26A"/>
  </svg>
);

const StarActiveIcon = ({ className }: { className?: string }) => (
  <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
    <path d="M6 1L7.545 4.13L11 4.635L8.5 7.07L9.09 10.5L6 8.885L2.91 10.5L3.5 7.07L1 4.635L4.455 4.13L6 1Z" fill="#FFAB00"/>
  </svg>
);

const StarInactiveIcon = ({ className }: { className?: string }) => (
  <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
    <path d="M6 1L7.545 4.13L11 4.635L8.5 7.07L9.09 10.5L6 8.885L2.91 10.5L3.5 7.07L1 4.635L4.455 4.13L6 1Z" fill="#E5E7EB"/>
  </svg>
);

const mockHotels = [
  {
    title: "Umasari Rice Terrace Villa by AGATA",
    link: "https://example.com",
    address: "Bali, Indonesia",
    thumbnail:
      "https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?auto=format&fit=facearea&w=400&h=400&q=80",
    rating: 4.6,
    reviews: 2700,
    price: 40,
  },
  {
    title: "Sample Hotel 2",
    link: "https://example.com",
    address: "Sample Address 2",
    thumbnail:
      "https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?auto=format&fit=facearea&w=400&h=400&q=80",
    rating: 4.2,
    reviews: 1500,
    price: 55,
  },
  {
    title: "Sample Hotel 3",
    link: "https://example.com",
    address: "Sample Address 3",
    thumbnail:
      "https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?auto=format&fit=facearea&w=400&h=400&q=80",
    rating: 4.8,
    reviews: 3200,
    price: 70,
  },
];

const HotelCard = ({ hotel }: { hotel: any }) => {
  const { title, link, thumbnail, rating, reviews, price } = hotel;

  // 计算星星显示（满星、半星、空星）
  const fullStars = Math.floor(rating);
  const halfStar = rating - fullStars >= 0.5;
  const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);

  return (
    <a
      href={link}
      target="_blank"
      rel="noopener noreferrer"
      className="block group"
      style={{ textDecoration: "none" }}
    >
      <div className="flex justify-between gap-[8px] rounded-lg border border-[#EFEFF3] shadow-md hover:shadow-lg transition-shadow duration-200 p-[12px] cursor-pointer">
        <div className="flex-1 min-w-0 flex flex-col justify-between">
          <div className="w-full">
            <div className="text-sm text-[#101828] truncate leading-[18px]">
              {title}
            </div>
            {/* star rating */}
            <div className="flex items-center gap-[2px] mt-[4px] h-[15px] leading-[15px]">
              <span className="text-xs text-[#676F83]">{rating}/5</span>
              <span className="flex items-center ml-[4px]">
                {Array.from({ length: fullStars }).map((_, i) => (
                  <StarActiveIcon key={i} className="w-[12px] h-[12px]" />
                ))}
                {halfStar && <StarActiveIcon className="w-[12px] h-[12px]" />}
                {Array.from({ length: emptyStars }).map((_, i) => (
                  <StarInactiveIcon key={i} className="w-[12px] h-[12px]" />
                ))}
              </span>
              <span className="text-xs text-[#676F83] ml-[4px]">
                ({(reviews / 1000).toFixed(1)}k)
              </span>
            </div>
          </div>
          <div className="text-sm text-[#101828] leading-[18px]">${price}</div>
        </div>
        {thumbnail && (
          <img
            src={thumbnail}
            alt={title}
            className="w-[70px] h-[70px] object-cover rounded-sm flex-shrink-0"
          />
        )}
      </div>
    </a>
  );
};

const WorkflowHotelsCard = ({ query = "tech hotels", extraParams = {} }) => {
  const [hotels, setHotels] = useState<any[]>(mockHotels);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // useEffect(() => {
  //   const fetchHotels = async () => {
  //     try {
  //       const response: any = await fetchSerpApi(
  //         "google_hotels",
  //         query,
  //         extraParams
  //       );
  //       const apiHotels =
  //         response && response.data && response.data.properties
  //           ? response.data.properties
  //           : [];
  //       setHotels(apiHotels.length > 0 ? apiHotels : mockHotels);
  //     } catch (err) {
  //       setError("无法加载酒店数据");
  //       setHotels(mockHotels);
  //     } finally {
  //       setLoading(false);
  //     }
  //   };
  //   fetchHotels();
  // }, [query, extraParams]);

  if (loading) return <div className="p-4">加载中...</div>;
  if (error) return <div className="p-4 text-red-500">{error}</div>;

  return (
    <div className="w-[400px] p-[16px] flex flex-col gap-[16px] rounded-xl border border-[#E8E9FF] bg-[#fff]">
      <div
        className="flex items-center gap-[2px] p-[4px] inline-block rounded-sm border border-[#EFEFF3]"
        style={{ width: "fit-content" }}
      >
        <HotelIcon className="w-[16px] h-[16px]" />
        <span className="text-[12px] text-[#101828]">Hotel</span>
      </div>
      <div className="text-[14px] text-[#101828]">Hotel card</div>
      <div className="flex flex-col gap-[8px]">
        {hotels.map((hotel, idx) => (
          <HotelCard hotel={hotel} key={idx} />
        ))}
      </div>
    </div>
  );
};

export default WorkflowHotelsCard;
