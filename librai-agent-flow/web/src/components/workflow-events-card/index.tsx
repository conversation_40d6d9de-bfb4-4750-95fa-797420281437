import React, { useEffect, useState } from "react";

// 内联SVG图标组件
const EventIcon = ({ className }: { className?: string }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
    <path d="M12 2V1C12 0.447715 11.5523 0 11 0C10.4477 0 10 0.447715 10 1V2H6V1C6 0.447715 5.55228 0 5 0C4.44772 0 4 0.447715 4 1V2H2C0.895431 2 0 2.89543 0 4V14C0 15.1046 0.895431 16 2 16H14C15.1046 16 16 15.1046 16 14V4C16 2.89543 15.1046 2 14 2H12ZM14 14H2V6H14V14Z" fill="#0B5394"/>
  </svg>
);

const mockEvents = [
  {
    title: "Austin Comedy KillersAustin Comedy Killers",
    link: "https://example.com",
    date: { when: "21 JUN" },
    address: "Sunset Strip Comedy & Entertainment, Austin, TX",
    thumbnail:
      "https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?auto=format&fit=facearea&w=400&h=400&q=80",
  },
  {
    title: "Austin Comedy KillersAustin Comedy Killers",
    link: "https://example.com",
    date: { when: "21 JUN" },
    address: "Sunset Strip Comedy & Entertainment, Austin, TX",
    thumbnail:
      "https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?auto=format&fit=facearea&w=400&h=400&q=80",
  },
  {
    title: "Austin Comedy KillersAustin Comedy Killers",
    link: "https://example.com",
    date: { when: "21 JUN" },
    address: "Sunset Strip Comedy & Entertainment, Austin, TX",
    thumbnail:
      "https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?auto=format&fit=facearea&w=400&h=400&q=80",
  },
];

const EventCard = ({ event }: { event: any }) => {
  const { title, link, date, address, thumbnail } = event;
  let day = "";
  let month = "";
  if (date && date.when) {
    const match = date.when.match(/(\d{1,2})\s*([A-Za-z]+)/);
    if (match) {
      day = match[1];
      month = match[2].toUpperCase();
    }
  }
  return (
    <a
      href={link}
      target="_blank"
      rel="noopener noreferrer"
      className="block group"
      style={{ textDecoration: "none" }}
    >
      <div className="flex justify-between gap-[12px] rounded-lg border border-[#EFEFF3] shadow-md hover:shadow-lg transition-shadow duration-200 p-[12px] cursor-pointer">
        <div className="flex flex-col items-center text-[#5C62FF] ml-[4px]">
          <span className="text-2xl font-bold leading-[24px]">{day}</span>
          <span className="text-xs uppercase">{month}</span>
        </div>
        <div className="flex-1 max-w-[219px]">
          <h2
            className="text-sm text-[#101828] leading-[18px] break-words line-clamp-2"
            style={{
              display: "-webkit-box",
              WebkitLineClamp: 2,
              WebkitBoxOrient: "vertical",
              overflow: "hidden",
            }}
          >
            {title}
          </h2>
          {address && (
            <p
              className="text-xs text-[#676F83] leading-[15px] line-clamp-2 mt-[4px]"
              style={{
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                overflow: "hidden",
              }}
            >
              {address}
            </p>
          )}
        </div>
        {thumbnail && (
          <img
            src={thumbnail}
            alt={title}
            className="w-[70px] h-[70px] object-cover rounded-sm flex-shrink-0"
          />
        )}
      </div>
    </a>
  );
};

const WorkflowEventsCard = ({ query = "tech events", extraParams = {} }) => {
  const [events, setEvents] = useState<any[]>(mockEvents);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // useEffect(() => {
  //   const fetchEvents = async () => {
  //     try {
  //       const response: any = await fetchSerpApi(
  //         "google_events",
  //         query,
  //         extraParams
  //       );
  //       const apiEvents =
  //         response && response.data && response.data.events_results
  //           ? response.data.events_results
  //           : [];
  //       setEvents(apiEvents.length > 0 ? apiEvents : mockEvents);
  //     } catch (err) {
  //       setError("无法加载事件数据");
  //       setEvents(mockEvents);
  //     } finally {
  //       setLoading(false);
  //     }
  //   };
  //   fetchEvents();
  // }, [query, extraParams]);

  if (loading) return <div className="p-4">加载中...</div>;
  if (error) return <div className="p-4 text-red-500">{error}</div>;

  return (
    <div className="max-w-[400px] w-full p-[16px] flex flex-col gap-[16px] rounded-xl border border-[#E8E9FF] bg-[#fff]">
      <div
        className="flex items-center gap-[2px] p-[4px] inline-block rounded-sm border border-[#EFEFF3]"
        style={{ width: "fit-content" }}
      >
        <EventIcon className="w-[16px] h-[16px]" />
        <span className="text-[12px] text-[#101828]">Event</span>
      </div>
      <div className="text-[14px] text-[#101828]">Event card</div>
      <div className="flex flex-col gap-[8px] overflow-hidden">
        {events.map((event, idx) => (
          <EventCard event={event} key={idx} />
        ))}
      </div>
    </div>
  );
};

export default WorkflowEventsCard;
