"use client";

import React, { useState, useCallback, useRef, useEffect } from "react";

import {
  ArrowsAltOutlined,
  DownloadOutlined,
  DownOutlined,
  RightOutlined,
} from "@ant-design/icons";
import { Input } from "antd";
import FileDetailModal from "./file-detail-modal";
import FiletextIcon from "./smallFileText.svg";
import { ChevronDownIcon, ChevronRightIcon } from "lucide-react";
// import { useFileStore } from '@/store/file-store'

interface MessageFile {
  id: string;
  filename: string;
  type: string;
  url: string;
  size: number;
  content?: string;
}

interface Message {
  id: string;
  message_files?: MessageFile[];
}

type FileItem = {
  id: string;
  name: string;
  type?: string;
  showActionButtons: boolean;
  isEditing?: boolean;
  content?: string;
  fileUrl?: string; // 实际文件的 URL（如果有的话）
  fileSize?: string; // 文件大小
};

type AgentFilesProps = {
  title?: string;
  description?: string;
  isExpanded?: boolean;
  fileList?: Array<{ name: string; url: string; }>;
  onToggle?: () => void;
  onAdd?: () => void;
  onDelete?: (id: string) => void;
  onEdit?: (id: string, newName: string) => void;
  onPreview?: (id: string) => void;
  onDownload?: (id: string) => void;
  className?: string;
  maxHeight?: string; // 文件列表的最大高度
  enableScroll?: boolean; // 是否启用滚动
};

// 自定义 hook 来处理 hover 状态
const useHoverState = (delay: number = 100) => {
  const [hoveredId, setHoveredId] = useState<string | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleMouseEnter = useCallback((id: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setHoveredId(id);
  }, []);

  const handleMouseLeave = useCallback(() => {
    timeoutRef.current = setTimeout(() => {
      setHoveredId(null);
    }, delay);
  }, [delay]);

  const cancelHide = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // 清理定时器
  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return { hoveredId, handleMouseEnter, handleMouseLeave, cancelHide };
};

declare global {
  interface Window {
    __messages__?: Message[];
    __messageId__?: string;
  }
}

const AgentFiles: React.FC<AgentFilesProps> = ({
  title,
  description,
  isExpanded = false,
  fileList,
  onToggle,
  onAdd,
  onDelete,
  onEdit,
  onPreview,
  onDownload,
  className = "",
  maxHeight = "400px",
  enableScroll = true,
}) => {

  // const [expanded, setExpanded] = useState(isExpanded);
  // // const storedFileList = useFileStore(state => state.fileList)
  // const [selectedFiles, setSelectedFiles] = useState<FileItem[]>([]);
  // const [editingValue, setEditingValue] = useState("");
  // const [isModalOpen, setIsModalOpen] = useState(false);
  // const [selectedFileForModal, setSelectedFileForModal] =
  //   useState<FileItem | null>(null);

  // // 使用自定义 hook 处理 hover 状态
  // const { hoveredId, handleMouseEnter, handleMouseLeave, cancelHide } =
  //   useHoverState(150);

  // useEffect(() => {
  //   // 使用传入的 fileList 或 store 中的 fileList
  //   const dataSource = fileList ;
  //   const files = dataSource.map((file: any, index: number) => ({
  //     id: String(index + 1),
  //     name: file.name || file.title,
  //     type: "text",
  //     showActionButtons: false,
  //     fileUrl: file.url,
  //     fileSize: "1 KB",
  //     content: file.description || "",
  //   }));
  //   setSelectedFiles(files);
  // }, [fileList]);

  // const handleToggle = () => {
  //   const newExpanded = !expanded;
  //   setExpanded(newExpanded);
  //   onToggle?.();
  // };

  // const handleDelete = (id: string) => {
  //   setSelectedFiles(selectedFiles.filter((item) => item.id !== id));
  //   onDelete?.(id);
  // };

  // const handlePreview = (id: string) => {
  //   const file = selectedFiles.find((item) => item.id === id);
  //   if (file) {
  //     setSelectedFileForModal(file);
  //     setIsModalOpen(true);
  //   }
  //   onPreview?.(id);
  // };

  // // 下载功能的实现
  // const downloadFile = (
  //   fileName: string,
  //   content: string,
  //   fileUrl?: string
  // ) => {
  //   if (fileUrl) {
  //     // 如果有实际文件 URL，直接下载文件
  //     const link = document.createElement("a");
  //     link.href = fileUrl;
  //     link.download = fileName;
  //     link.style.display = "none";
  //     document.body.appendChild(link);
  //     link.click();
  //     document.body.removeChild(link);
  //   } else {
  //     // 如果没有文件 URL，下载文件内容为文本文件
  //     const blob = new Blob([content], { type: "text/plain;charset=utf-8" });
  //     const url = URL.createObjectURL(blob);
  //     const link = document.createElement("a");

  //     // 根据文件扩展名生成合适的文件名
  //     const extension = fileName.split(".").pop()?.toLowerCase();
  //     let downloadFileName = fileName;

  //     // 如果是 office 文档，下载为 txt 格式以保持内容可读性
  //     if (["pptx", "docx", "doc", "ppt"].includes(extension || "")) {
  //       downloadFileName = fileName.replace(/\.[^/.]+$/, "") + ".txt";
  //     }

  //     link.href = url;
  //     link.download = downloadFileName;
  //     link.style.display = "none";
  //     document.body.appendChild(link);
  //     link.click();
  //     document.body.removeChild(link);
  //     URL.revokeObjectURL(url);
  //   }
  // };

  // const handleDownload = (id: string) => {
  //   const file = selectedFiles.find((item) => item.id === id);
  //   if (file) {
  //     downloadFile(file.name, file.content || "", file.fileUrl);

  //     // 显示下载成功提示
  //     console.log(
  //       `Downloading file: ${file.name}`
  //     );

  //     // 可以添加 toast 提示
  //     // toast.success(`File "${file.name}" downloaded successfully`);
  //   }
  //   onDownload?.(id);
  // };

  // const handleEditStart = (id: string, currentName: string) => {
  //   setSelectedFiles(
  //     selectedFiles.map((item) =>
  //       item.id === id
  //         ? { ...item, isEditing: true, showActionButtons: false }
  //         : { ...item, isEditing: false }
  //     )
  //   );
  //   setEditingValue(currentName);
  // };

  // const handleEditSave = (id: string) => {
  //   if (editingValue.trim()) {
  //     setSelectedFiles(
  //       selectedFiles.map((item) =>
  //         item.id === id
  //           ? { ...item, name: editingValue.trim(), isEditing: false }
  //           : item
  //       )
  //     );
  //     onEdit?.(id, editingValue.trim());
  //   } else {
  //     setSelectedFiles(
  //       selectedFiles.map((item) =>
  //         item.id === id ? { ...item, isEditing: false } : item
  //       )
  //     );
  //   }
  //   setEditingValue("");
  // };

  // const handleEditCancel = (id: string) => {
  //   setSelectedFiles(
  //     selectedFiles.map((item) =>
  //       item.id === id ? { ...item, isEditing: false } : item
  //     )
  //   );
  //   setEditingValue("");
  // };

  // const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   setEditingValue(e.target.value);
  // };

  // const handleInputPressEnter = (id: string) => {
  //   handleEditSave(id);
  // };

  // const handleInputBlur = (id: string) => {
  //   handleEditSave(id);
  // };

  // const handleModalClose = () => {
  //   setIsModalOpen(false);
  //   setSelectedFileForModal(null);
  // };

  // return (
  //   <div className={`flex flex-col self-stretch ${className}`}>
  //     {/* 分割线 */}
  //     <div className="h-px w-full bg-[#EFEFF3]" />

  //     {/* 容器 */}
  //     <div className="flex flex-col  self-stretch">
  //       {/* 标题容器 - 整个区域可点击 */}
  //       <div
  //         onClick={handleToggle}
  //         className="flex cursor-pointer items-center gap-2 py-3"
  //       >
  //         {/* 下拉图标 */}
  //         <div className="flex h-4 w-4 items-center justify-center text-[#101828]">
  //           {expanded ? (
  //             <ChevronDownIcon className="h-4 w-4" />
  //           ) : (
  //             <ChevronRightIcon className="h-4 w-4" />
  //           )}
  //         </div>

  //         {/* 标题 */}
  //         <h3 className="font-inter text-base font-bold leading-6 text-[#101828]">
  //           {title || "common.agentFiles.title"}
  //         </h3>
  //       </div>

  //       {/* 展开内容 */}
  //       {expanded && (
  //         <div
  //           className={`flex flex-col mb-2 ${
  //             selectedFiles.length > 0 ? "gap-1" : ""
  //           } self-stretch`}
  //         >
  //           {/* 文件列表容器，支持滚动 */}
  //           <div
  //             className={`flex flex-col ${
  //               selectedFiles.length > 0 ? "gap-1 pb-4" : ""
  //             } ${enableScroll ? "overflow-y-auto no-scrollbar" : ""}`}
  //             style={enableScroll ? { maxHeight } : {}}
  //           >
  //             {selectedFiles.map((item) => (
  //               <div
  //                 key={item.id}
  //                 className="flex cursor-pointer items-center rounded-lg border border-[#EFEFF3] bg-white p-2 transition-all duration-200 hover:bg-[#F7F7FC]"
  //                 onMouseEnter={() => handleMouseEnter(item.id)}
  //                 onMouseLeave={() => handleMouseLeave()}
  //               >
  //                 {/* 左侧：图标和文本容器 - 占据剩余空间 */}
  //                 <div className="flex items-center gap-1.5 flex-1 min-w-0">
  //                   {/* 图标容器 */}
  //                   <div className="flex h-6 w-6 items-center justify-center rounded bg-[#E8E9FF] flex-shrink-0">
  //                     <FiletextIcon className="text-[#5C62FF]" />
  //                   </div>

  //                   {/* 文件名或编辑输入框 */}
  //                   {item.isEditing ? (
  //                     <Input
  //                       value={editingValue}
  //                       onChange={handleInputChange}
  //                       onPressEnter={() => handleInputPressEnter(item.id)}
  //                       onBlur={() => handleInputBlur(item.id)}
  //                       autoFocus
  //                       className="border-none bg-transparent p-0 font-inter text-sm font-normal text-[#101828] focus:shadow-none flex-1"
  //                       style={{
  //                         boxShadow: "none",
  //                         border: "none",
  //                         outline: "none",
  //                       }}
  //                     />
  //                   ) : (
  //                     <div className="flex flex-col flex-1 min-w-0">
  //                       <span className="font-inter text-sm font-normal text-[#101828] truncate">
  //                         {item.name}
  //                       </span>
  //                     </div>
  //                   )}
  //                 </div>

  //                 {/* 右侧：操作按钮 - 固定宽度 */}
  //                 <div className="flex items-center gap-1 ml-2 flex-shrink-0">
  //                   {hoveredId === item.id && !item.isEditing ? (
  //                     <div
  //                       className="flex items-center gap-1"
  //                       onMouseEnter={() => {
  //                         // 当鼠标进入按钮区域时，取消隐藏定时器
  //                         cancelHide();
  //                       }}
  //                     >
  //                       {/* 预览按钮（放大图标） */}
  //                       <button
  //                         onClick={(e) => {
  //                           e.stopPropagation();
  //                           handlePreview(item.id);
  //                         }}
  //                         className="flex h-6 w-6 items-center justify-center rounded transition-colors hover:bg-[#EFEFF3]"
  //                       >
  //                         <ArrowsAltOutlined
  //                           className="text-[#676F83]"
  //                           style={{ fontSize: "14px" }}
  //                         />
  //                       </button>

  //                       {/* 下载按钮（下载图标） */}
  //                       <button
  //                         onClick={(e) => {
  //                           e.stopPropagation();
  //                           handleDownload(item.id);
  //                         }}
  //                         className="flex h-6 w-6 items-center justify-center rounded transition-colors hover:bg-[#EFEFF3]"
  //                       >
  //                         <DownloadOutlined
  //                           className="text-[#676F83]"
  //                           style={{ fontSize: "14px" }}
  //                         />
  //                       </button>
  //                     </div>
  //                   ) : (
  //                     // 预留操作按钮的空间，避免布局跳动
  //                     <div className="w-[52px] h-6"></div>
  //                   )}
  //                 </div>
  //               </div>
  //             ))}
  //           </div>

  //           {/* 描述文本 - 只在没有文件时显示 */}
  //           {selectedFiles.length === 0 && (
  //             <p className="font-inter self-stretch text-sm font-normal leading-5 text-[#676F83] pt-0.5">
  //               {description || "common.agentFiles.description"}
  //             </p>
  //           )}
  //         </div>
  //       )}
  //     </div>

  //     {/* 文件详情模态窗口 */}
  //     {isModalOpen && selectedFileForModal && (
  //       <FileDetailModal
  //         isOpen={isModalOpen}
  //         onClose={handleModalClose}
  //         file={{
  //           url:
  //             selectedFileForModal.fileUrl ||
  //             selectedFileForModal.content ||
  //             "",
  //           name: selectedFileForModal.name,
  //           size: selectedFileForModal.fileSize,
  //         }}
  //         onDownload={(file) => {
  //           downloadFile(file.name, file.url, file.url);
  //           console.log(
  //             `Downloading from preview: ${file.name}`
  //           );
  //           onDownload?.(selectedFileForModal.id);
  //         }}
  //       />
  //     )}
    // </div>
    return (
      <div>
        <h1>Agent Files</h1>
      </div>
    
  );
};

export default AgentFiles;
