"use client";

import React from 'react';

export type PresentationPreviewProps = {
  fileUrl?: string;
};

const PresentationPreview: React.FC<PresentationPreviewProps> = ({
  fileUrl,
}) => {
  // 检查fileUrl是否是可用的公共https链接
  const isPublicUrl = fileUrl && fileUrl.startsWith('https://');

  const viewerUrl = isPublicUrl 
    ? `https://docs.google.com/gview?url=${encodeURIComponent(fileUrl)}&embedded=true&hl=en`
    : null;

  return (
    <div className="w-full h-full bg-white">
      {viewerUrl ? (
        <div className="w-full h-full overflow-hidden" >
          <iframe
            src={viewerUrl}
            className="w-full h-full"
            frameBorder="0"
           
          
          />
        </div>
      ) : (
        <div className="h-full flex items-center justify-center p-4">
          <div className="text-center p-4 rounded-lg bg-yellow-50 border border-yellow-200">
            <h3 className="font-bold text-yellow-800">Preview Unavailable</h3>
          </div>
        </div>
      )}
    </div>
  );
};

export default PresentationPreview; 