"use client";

import React, { useCallback, useEffect, useMemo, useState } from "react";
import { DownloadOutlined, LeftOutlined } from "@ant-design/icons";
import { Spin } from "antd";
import FiletextIcon from "./FileText.svg";
// import FilePreview from "../file-preview";

type FileType =
  | "pdf"
  | "image"
  | "video"
  | "presentation"
  | "document"
  | "spreadsheet"
  | "archive"
  | "code";

type FileInfo = {
  url: string;
  name: string;
  size?: string;
  type?: FileType;
};

type FileDetailModalProps = {
  isOpen: boolean;
  onClose: () => void;
  file: FileInfo | FileInfo[]; // 支持单个文件或多个文件（如图片集合）
  onDownload?: (file: FileInfo) => void;
};

// 根据文件扩展名获取文件类型
const getFileType = (fileName: string): FileType => {
  const extension = fileName.split(".").pop()?.toLowerCase() || "";
  if (["pdf"].includes(extension)) return "pdf";
  if (["jpg", "jpeg", "png", "gif", "bmp", "svg", "webp"].includes(extension))
    return "image";
  if (
    [
      "mp4",
      "avi",
      "mov",
      "wmv",
      "flv",
      "webm",
      "mkv",
      "mp3",
      "m4a",
      "wav",
    ].includes(extension)
  )
    return "video";
  if (["ppt", "pptx"].includes(extension)) return "presentation";
  if (["xls", "xlsx", "csv"].includes(extension)) return "spreadsheet";
  if (["doc", "docx", "txt", "md", "rtf"].includes(extension))
    return "document";
  if (["zip", "rar", "7z", "tar", "gz"].includes(extension)) return "archive";
  if (
    ["json", "xml", "html", "css", "js", "ts", "py", "java"].includes(extension)
  )
    return "code";
  return "document";
};

const FileDetailModal: React.FC<FileDetailModalProps> = ({
  isOpen,
  onClose,
  file,
  onDownload,
}) => {

  const [zoomLevel, setZoomLevel] = useState(100);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(false);

  // 标准化文件数据，统一处理单个文件和文件数组
  const files = useMemo(() => {
    if (!file) return [];
    return Array.isArray(file) ? file : [file];
  }, [file]);

  const currentFile = files[currentIndex];

  // 获取当前文件类型
  const fileType = useMemo(() => {
    if (!currentFile) return "document";
    return currentFile.type || getFileType(currentFile.name);
  }, [currentFile]);

  // 重置状态当模态框关闭时
  useEffect(() => {
    if (!isOpen) {
      setZoomLevel(100);
      setCurrentIndex(0);
      setLoading(false);
    }
  }, [isOpen]);

  // 处理背景点击
  const handleBackdropClick = useCallback(
    (e: React.MouseEvent) => {
      if (e.target === e.currentTarget) onClose();
    },
    [onClose]
  );

  // 处理缩放
  const handleZoomChange = useCallback((newZoom: number) => {
    setZoomLevel(Math.max(50, Math.min(200, newZoom)));
  }, []);

  // 处理图片切换
  const handlePrevImage = useCallback(() => {
    setCurrentIndex((prev) => Math.max(0, prev - 1));
  }, []);

  const handleNextImage = useCallback(() => {
    setCurrentIndex((prev) => Math.min(files.length - 1, prev + 1));
  }, [files.length]);

  // 处理文件下载
  const handleDownload = useCallback(async () => {
    if (!currentFile) return;

    try {
      if (onDownload) {
        onDownload(currentFile);
        return;
      }

      // 默认下载行为
      const link = document.createElement("a");
      link.href = currentFile.url;
      link.download = currentFile.name;
      link.target = "_blank";
      link.rel = "noopener noreferrer";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("下载失败:", error);
    }
  }, [currentFile, onDownload]);

  if (!isOpen || !currentFile) return null;

  return (
    <div
      className="fixed inset-0 z-50 bg-black bg-opacity-50"
      onClick={handleBackdropClick}
    >
      <div className="h-full w-full bg-[#F7F7FC]">
        {/* 顶部工具栏 */}
        <div className="flex h-16 items-center justify-between px-4 flex-shrink-0">
          <div className="flex items-center gap-1">
            <button
              onClick={onClose}
              className="flex h-9 w-9 items-center justify-center rounded-lg transition-colors hover:bg-gray-100"
            >
              <LeftOutlined
                className="text-[#101828]"
                style={{ fontSize: "16px" }}
              />
            </button>

            <div className="flex items-center gap-2">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-[#E8E9FF] px-[6.67px]">
                <FiletextIcon
                  width={24}
                  height={24}
                  className="text-[#5C62FF]"
                />
              </div>

              <div className="flex flex-col">
                <span className="font-inter text-base font-normal leading-6 text-[#101828]">
                  {currentFile.name}
                </span>
                {currentFile.size && (
                  <span className="text-sm text-gray-500">
                    {currentFile.size}
                  </span>
                )}
                {files.length > 1 && (
                  <span className="text-xs text-gray-400">
                    {currentIndex + 1} / {files.length}
                  </span>
                )}
              </div>
            </div>
          </div>

          <button
            onClick={handleDownload}
            className="flex h-9 w-9 items-center justify-center rounded-lg transition-colors hover:bg-gray-100"
            title="下载文件"
          >
            <DownloadOutlined
              className="text-[#101828]"
              style={{ fontSize: "16px" }}
            />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="h-[calc(100vh-4rem)] bg-[#EFEFF3] relative">
          {loading && (
            <div className="h-full flex items-center justify-center">
              <Spin size="large" />
            </div>
          )}
          {/* {!loading && (
            <FilePreview
              fileUrls={files.map((f) => f.url)}
              currentIndex={currentIndex}
              fileName={currentFile.name}
              fileType={fileType}
              zoomLevel={zoomLevel}
              onZoomChange={handleZoomChange}
              isReady={true}
              onPrev={handlePrevImage}
              onNext={handleNextImage}
              showNavigation={files.length > 1}
            />
          )} */}
        </div>
      </div>
    </div>
  );
};

export default React.memo(FileDetailModal);
