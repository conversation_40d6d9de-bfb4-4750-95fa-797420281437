import Link from "next/link";

interface LibraiLogoProps {
  className?: string;
  href?: string;
  textColor?: 'white' | 'black';
}

export function LibraiLogo({ className = "", href = "/", textColor = 'white' }: LibraiLogoProps) {
  const textColorClass = textColor === 'white' ? 'text-[#ffffff]' : 'text-[#161616]';
  
  const LogoContent = () => (
    <div className={`inline-flex items-center ${className}`}>
      <div className="relative h-[40px]">
        <div className="px-[16px] py-[8px] h-full flex items-center">
          <span className="font-sans text-[24px] font-medium leading-none tracking-[2px]">
            <span className={textColorClass}>LIBR</span>
            <span className="text-[#5C62FF]">Δ</span>
            <span className="text-[#5C62FF]">I</span>
          </span>
        </div>
      </div>
    </div>
  );

  if (href) {
    return (
      <Link
        href={href}
        className="transition-opacity duration-300 hover:opacity-80"
      >
        <LogoContent />
      </Link>
    );
  }

  return <LogoContent />;
} 