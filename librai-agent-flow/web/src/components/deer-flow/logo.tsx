// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

"use client";

import Link from "next/link";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

interface LogoProps {
  textColor?: 'white' | 'black' | 'auto';
}

export function Logo({ textColor = 'auto' }: LogoProps = {}) {
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  if (!mounted) {
    return null; // 避免 hydration 不匹配
  }
  
  const getTextColor = () => {
    if (textColor !== 'auto') {
      return textColor;
    }
    
    // 使用 resolvedTheme 来获取实际的主题（包括 system 主题的解析结果）
    const currentTheme = resolvedTheme || theme;
    return currentTheme === 'dark' ? 'white' : 'black';
  };
  
  const currentTextColor = getTextColor();
  const textColorClass = currentTextColor === 'white' ? 'text-[#ffffff]' : 'text-[#161616]';
  
  return (
    <Link
      className="opacity-70 transition-opacity duration-300 hover:opacity-100"
      href="/"
    >
      <div className="inline-flex items-center">
        <div className="relative h-[32px]">
          <div className="px-[12px] py-[6px] h-full flex items-center">
            <span className="font-sans text-[18px] font-medium leading-none tracking-[1.5px]">
              <span className={textColorClass}>LIBR</span>
              <span className="text-[#5C62FF]">Δ</span>
              <span className="text-[#5C62FF]">I</span>
            </span>
          </div>
        </div>
      </div>
    </Link>
  );
}
