// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { Check, Copy } from "lucide-react";
import { useMemo, useState, useRef } from "react";
import ReactMarkdown, {
  type Options as ReactMarkdownOptions,
} from "react-markdown";
import rehypeKatex from "rehype-katex";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
import "katex/dist/katex.min.css";

import { Button } from "~/components/ui/button";
import { rehypeSplitWordsIntoSpans } from "~/core/rehype";
import { autoFixMarkdown } from "~/core/utils/markdown";
import { cn } from "~/lib/utils";

import Image from "./image";
import { Tooltip } from "./tooltip";
import { Link } from "./link";

// 缓存处理过的 markdown 内容，避免重复计算
const markdownCache = new Map<string, string>();
const maxCacheSize = 100; // 限制缓存大小

export function Markdown({
  className,
  children,
  style,
  enableCopy,
  animated = false,
  checkLinkCredibility = false,
  ...props
}: ReactMarkdownOptions & {
  className?: string;
  enableCopy?: boolean;
  style?: React.CSSProperties;
  animated?: boolean;
  checkLinkCredibility?: boolean;
}) {
  const components: ReactMarkdownOptions["components"] = useMemo(() => {
    return {
      a: ({ href, children }) => (
        <Link href={href} checkLinkCredibility={checkLinkCredibility}>
          {children}
        </Link>
      ),
      img: ({ src, alt }) => (
        <a href={src as string} target="_blank" rel="noopener noreferrer">
          <Image className="rounded" src={src as string} alt={alt ?? ""} />
        </a>
      ),
    };
  }, [checkLinkCredibility]);

  const rehypePlugins = useMemo(() => {
    const katexOptions = {
      strict: false,
      throwOnError: false,
      errorColor: '#cc0000',
      macros: {
        "\\RR": "\\mathbb{R}",
        "\\NN": "\\mathbb{N}",
        "\\ZZ": "\\mathbb{Z}",
        "\\QQ": "\\mathbb{Q}",
        "\\CC": "\\mathbb{C}"
      },
      // 添加错误处理回调
      output: 'html',
      trust: false,
    };

    // 只在动画模式且内容较短时使用 rehypeSplitWordsIntoSpans，避免性能问题
    if (animated && (children?.length ?? 0) < 1000) {
      return [[rehypeKatex, katexOptions], rehypeSplitWordsIntoSpans];
    }
    return [[rehypeKatex, katexOptions]];
  }, [animated, children?.length]);

  // 缓存处理过的内容，避免重复计算
  const processedContent = useMemo(() => {
    const rawContent = children ?? "";
    const cacheKey = `${rawContent}-${animated}`;

    if (markdownCache.has(cacheKey)) {
      return markdownCache.get(cacheKey)!;
    }

    const processed = autoFixMarkdown(
      dropMarkdownQuote(processKatexInMarkdown(rawContent)) ?? "",
    );

    // 管理缓存大小
    if (markdownCache.size >= maxCacheSize) {
      const firstKey = markdownCache.keys().next().value;
      if (firstKey) {
        markdownCache.delete(firstKey);
      }
    }

    markdownCache.set(cacheKey, processed);
    return processed;
  }, [children, animated]);

  // 错误处理状态
  const [hasError, setHasError] = useState(false);

  // 重置错误状态当内容改变时
  useMemo(() => {
    setHasError(false);
  }, [children]);

  if (hasError) {
    return (
      <div className={cn(className, "prose dark:prose-invert")} style={style}>
        <div className="text-red-500 text-sm">
          Math formula rendering failed, showing raw content:
        </div>
        <pre className="whitespace-pre-wrap text-sm">{children}</pre>
        {enableCopy && typeof children === "string" && (
          <div className="flex">
            <CopyButton content={children} />
          </div>
        )}
      </div>
    );
  }

  try {
    return (
      <div className={cn(className, "prose dark:prose-invert")} style={style}>
        <ReactMarkdown
          remarkPlugins={[remarkGfm, remarkMath]}
          rehypePlugins={rehypePlugins as any}
          components={components}
          {...props}
        >
          {processedContent}
        </ReactMarkdown>
        {enableCopy && typeof children === "string" && (
          <div className="flex">
            <CopyButton content={children} />
          </div>
        )}
      </div>
    );
  } catch (error) {
    console.error("Markdown rendering error:", error);
    setHasError(true);
    return null; // 这会触发重新渲染，显示错误状态
  }
}

function CopyButton({ content }: { content: string }) {
  const [copied, setCopied] = useState(false);
  return (
    <Tooltip title="Copy">
      <Button
        variant="outline"
        size="sm"
        className="rounded-full"
        onClick={async () => {
          try {
            await navigator.clipboard.writeText(content);
            setCopied(true);
            setTimeout(() => {
              setCopied(false);
            }, 1000);
          } catch (error) {
            console.error(error);
          }
        }}
      >
        {copied ? (
          <Check className="h-4 w-4" />
        ) : (
          <Copy className="h-4 w-4" />
        )}{" "}
      </Button>
    </Tooltip>
  );
}

function processKatexInMarkdown(markdown?: string | null) {
  if (!markdown) return markdown;

  try {
    // 更安全的 LaTeX 数学公式处理
    let processed = markdown;

    // 处理块级数学公式 \[ ... \] -> $$ ... $$
    processed = processed.replace(/\\\\\[([^]*?)\\\\\]/g, (_, content) => {
      return `$$${content}$$`;
    });

    // 处理行内数学公式 \( ... \) -> $ ... $
    processed = processed.replace(/\\\\\(([^]*?)\\\\\)/g, (_, content) => {
      return `$${content}$`;
    });

    // 处理单反斜杠的情况
    processed = processed.replace(/\\\[([^]*?)\\\]/g, (_, content) => {
      return `$$${content}$$`;
    });

    processed = processed.replace(/\\\(([^]*?)\\\)/g, (_, content) => {
      return `$${content}$`;
    });

    // 清理可能的错误格式
    processed = processed.replace(/\$\$\$\$/g, "$$");
    processed = processed.replace(/\$\$\$/g, "$$");

    return processed;
  } catch (error) {
    console.warn("Error processing KaTeX in markdown:", error);
    return markdown; // 出错时返回原始内容
  }
}

function dropMarkdownQuote(markdown?: string | null) {
  if (!markdown) return markdown;
  return markdown
    .replace(/^```markdown\n/gm, "")
    .replace(/^```text\n/gm, "")
    .replace(/^```\n/gm, "")
    .replace(/\n```$/gm, "");
}
