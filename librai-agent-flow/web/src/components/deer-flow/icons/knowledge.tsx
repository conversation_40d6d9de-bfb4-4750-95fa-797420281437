import { type SVGProps } from "react";

export function KnowledgeIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M14.4781 12.1598L12.4038 2.29732C12.3769 2.16835 12.3249 2.04594 12.2506 1.93715C12.1763 1.82835 12.0813 1.7353 11.9709 1.66334C11.8606 1.59138 11.7371 1.54194 11.6076 1.51784C11.4781 1.49375 11.3451 1.49548 11.2163 1.52295L8.29062 2.1517C8.03184 2.20836 7.8059 2.3649 7.66194 2.58729C7.51798 2.80967 7.46764 3.07989 7.52187 3.3392L9.59625 13.2017C9.64248 13.4267 9.76477 13.6289 9.94255 13.7744C10.1203 13.9199 10.3428 13.9997 10.5725 14.0004C10.6435 14.0004 10.7143 13.9928 10.7837 13.9779L13.7094 13.3492C13.9685 13.2924 14.1946 13.1355 14.3386 12.9128C14.4826 12.69 14.5327 12.4194 14.4781 12.1598ZM8.5 3.13482C8.5 3.13107 8.5 3.1292 8.5 3.1292L11.425 2.5042L11.6331 3.49607L8.70813 4.12545L8.5 3.13482ZM8.91375 5.1017L11.84 4.47357L12.0487 5.46732L9.125 6.09607L8.91375 5.1017ZM9.32875 7.07482L12.255 6.44607L13.0863 10.3986L10.16 11.0273L9.32875 7.07482ZM13.5 12.3717L10.575 12.9967L10.3669 12.0048L13.2919 11.3754L13.5 12.3661C13.5 12.3698 13.5 12.3717 13.5 12.3717ZM6.5 2.00045H3.5C3.23478 2.00045 2.98043 2.1058 2.79289 2.29334C2.60536 2.48087 2.5 2.73523 2.5 3.00045V13.0004C2.5 13.2657 2.60536 13.52 2.79289 13.7076C2.98043 13.8951 3.23478 14.0004 3.5 14.0004H6.5C6.76522 14.0004 7.01957 13.8951 7.20711 13.7076C7.39464 13.52 7.5 13.2657 7.5 13.0004V3.00045C7.5 2.73523 7.39464 2.48087 7.20711 2.29334C7.01957 2.1058 6.76522 2.00045 6.5 2.00045ZM3.5 3.00045H6.5V4.00045H3.5V3.00045ZM3.5 5.00045H6.5V11.0004H3.5V5.00045ZM6.5 13.0004H3.5V12.0004H6.5V13.0004Z"
        fill="#343A3E"
      />
    </svg>
  );
} 