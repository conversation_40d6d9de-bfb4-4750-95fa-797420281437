// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import {
  useCallback,
  useEffect,
  useImperativeHandle,
  useLayoutEffect,
  useRef,
  type ReactNode,
  type RefObject,
} from "react";
import { useStickToBottom } from "use-stick-to-bottom";

import { ScrollArea } from "~/components/ui/scroll-area";
import { cn } from "~/lib/utils";

export interface ScrollContainerProps {
  className?: string;
  children?: ReactNode;
  scrollShadow?: boolean;
  scrollShadowColor?: string;
  autoScrollToBottom?: boolean;
  scrollDebounceMs?: number; // 新增防抖延迟时间配置
  ref?: RefObject<ScrollContainerRef | null>;
}

export interface ScrollContainerRef {
  scrollToBottom(): void;
}

export function ScrollContainer({
  className,
  children,
  scrollShadow = true,
  scrollShadowColor = "var(--background)",
  autoScrollToBottom = false,
  scrollDebounceMs = 150, // 默认150ms防抖延迟
  ref,
}: ScrollContainerProps) {
  const { scrollRef, contentRef, scrollToBottom, isAtBottom } =
    useStickToBottom({ initial: "instant" });
  
  // 防抖定时器引用
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // 防抖版本的滚动到底部函数
  const debouncedScrollToBottom = useCallback(() => {
    // 清除之前的定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // 设置新的防抖定时器
    debounceTimerRef.current = setTimeout(() => {
      // 只有在用户没有手动滚动时才自动滚动
      if (isAtBottom && autoScrollToBottom) {
        scrollToBottom();
      }
    }, scrollDebounceMs);
  }, [isAtBottom, scrollToBottom, scrollDebounceMs, autoScrollToBottom]);
  
  // 清理函数，在组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  useImperativeHandle(ref, () => ({
    scrollToBottom() {
      debouncedScrollToBottom();
    },
  }));

  const tempScrollRef = useRef<HTMLElement>(null);
  const tempContentRef = useRef<HTMLElement>(null);
  const lastAutoScrollToBottom = useRef(autoScrollToBottom);
  
  useEffect(() => {
    // 只有当 autoScrollToBottom 真正发生变化时才执行
    if (lastAutoScrollToBottom.current !== autoScrollToBottom) {
      if (!autoScrollToBottom) {
        tempScrollRef.current = scrollRef.current;
        tempContentRef.current = contentRef.current;
        scrollRef.current = null;
        contentRef.current = null;
      } else if (tempScrollRef.current && tempContentRef.current) {
        scrollRef.current = tempScrollRef.current;
        contentRef.current = tempContentRef.current;
      }
      lastAutoScrollToBottom.current = autoScrollToBottom;
    }
  }, [autoScrollToBottom]); // 移除 contentRef 和 scrollRef 依赖项

  return (
    <div className={cn("relative", className)}>
      {scrollShadow && (
        <>
          <div
            className={cn(
              "absolute top-0 right-0 left-0 z-10 h-10 bg-gradient-to-t",
              `from-transparent to-[var(--scroll-shadow-color)]`,
            )}
            style={
              {
                "--scroll-shadow-color": scrollShadowColor,
              } as React.CSSProperties
            }
          ></div>
          <div
            className={cn(
              "absolute right-0 bottom-0 left-0 z-10 h-10 bg-gradient-to-b",
              `from-transparent to-[var(--scroll-shadow-color)]`,
            )}
            style={
              {
                "--scroll-shadow-color": scrollShadowColor,
              } as React.CSSProperties
            }
          ></div>
        </>
      )}
      <ScrollArea ref={scrollRef} className="h-full w-full">
        <div 
          className="h-fit w-full min-w-0" 
          ref={contentRef}
          style={{
            wordBreak: "break-word",
            overflowWrap: "break-word"
          }}
        >
          {children}
        </div>
      </ScrollArea>
    </div>
  );
}
