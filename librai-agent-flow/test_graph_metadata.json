{"metadata": {"nodes": {"node_a": {"name": "node_a", "function_name": "node_a(tags=None, recurse=True, explode_args=False, func_accepts_config=False, func_accepts={})", "metadata": null}, "node_b": {"name": "node_b", "function_name": "node_b(tags=None, recurse=True, explode_args=False, func_accepts_config=False, func_accepts={})", "metadata": null}}, "edges": [{"source": "node_a", "target": "node_b"}, {"source": "__start__", "target": "node_a"}, {"source": "node_b", "target": "__end__"}], "conditional_edges": [], "entry_points": [], "finish_points": [], "state_schema_name": "State", "state_schema_module": "__main__"}, "version": "1.0"}