# Environment Configuration Example
# Copy this file to .env and fill in your actual values

# Frontend API Configuration
NEXT_PUBLIC_API_URL=https://librai-agent-flow-test.up.railway.app/

# Backend Configuration
# Add your backend environment variables here if needed

# Optional: Analytics (for production deployments)
AMPLITUDE_API_KEY=your_amplitude_key_here

# Optional: GitHub integration
GITHUB_OAUTH_TOKEN=your_github_token_here

# Optional: Static website mode
NEXT_PUBLIC_STATIC_WEBSITE_ONLY=false