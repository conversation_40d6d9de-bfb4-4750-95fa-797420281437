#!/usr/bin/env python3
"""
Simple test script for SerpApi tools - minimal examples.

Events API Filter Reference:
- event_type: "Virtual-Event", "In-Person"
- date: "today", "tomorrow", "this_week", "this_weekend", "next_week", "next_month"
- price: "free", "paid"
- Multiple filters: "event_type:Virtual-Event,price:free"

Flight API Parameter Reference:
- Travel Class: "1"=Economy, "2"=Premium Economy, "3"=Business, "4"=First
- Time Preferences: "1"=Morning, "2"=Afternoon, "3"=Evening  
- Trip Type: 1=Round trip, 2=One way (set automatically)

Hotels API Parameter Reference:
- hotel_class: "2", "3", "4", "5" (star rating, HOTELS ONLY - not with vacation_rentals=True)
- rating: "7" (3.5+), "8" (4.0+), "9" (4.5+)
- sort_by: "3" (lowest_price), "8" (highest_rating), "13" (most_reviewed)
- vacation_rentals: True/False (include vacation rentals)
- free_cancellation: True/False (HOTELS ONLY - not with vacation_rentals=True)
- bedrooms/bathrooms: Integer filters (vacation rentals only)

GOOGLE HOTELS API PARAMETER REFERENCE:
=====================================

REQUIRED PARAMETERS:
- q: Search query (location or hotel name)
- check_in_date: Check-in date in YYYY-MM-DD format
- check_out_date: Check-out date in YYYY-MM-DD format

BASIC PARAMETERS:
- adults: Number of adult guests (default: 2)
- children: Number of child guests (default: 0)
- children_ages: Ages of children - "5" or "5,8,10" (ages 1-17, must match children count)
- currency: Currency code - "USD", "EUR", "GBP", etc. (default: "USD")
- hl: Language code - "en", "es", "fr", etc. (default: "en")
- gl: Country code - "us", "uk", "de", etc. (default: "us")

FILTERING PARAMETERS:
- min_price: Minimum price per night (integer)
- max_price: Maximum price per night (integer)
- rating: Minimum rating filter (string):
  * "7" = 3.5+ rating
  * "8" = 4.0+ rating
  * "9" = 4.5+ rating
- hotel_class: Hotel star class (string, HOTELS ONLY - not for vacation rentals):
  * "2" = 2-star hotels
  * "3" = 3-star hotels
  * "4" = 4-star hotels
  * "5" = 5-star hotels
  * Multiple: "2,3,4" (comma-separated)
- sort_by: Sort results (string):
  * "3" = Lowest price
  * "8" = Highest rating
  * "13" = Most reviewed

ADVANCED FILTERING:
- property_types: Property type IDs - "17" or "17,12,18" (see API docs)
- amenities: Amenity IDs - "35" or "35,9,19" (see API docs)
- brands: Hotel brand IDs - "33" or "33,67,101" (HOTELS ONLY - not for vacation rentals)
- free_cancellation: Filter for free cancellation (boolean, HOTELS ONLY - not for vacation rentals)
- special_offers: Filter for special offers (boolean, HOTELS ONLY - not for vacation rentals)
- eco_certified: Filter for eco-certified properties (boolean, HOTELS ONLY - not for vacation rentals)

VACATION RENTAL SPECIFIC:
- vacation_rentals: Search vacation rentals instead of hotels (boolean)
- bedrooms: Minimum number of bedrooms (integer, vacation rentals only)
- bathrooms: Minimum number of bathrooms (integer, vacation rentals only)

PAGINATION & DETAILS:
- next_page_token: Token for next page of results
- property_token: Token for specific property details

NOTES:
- Parameter values with multiple options use comma-separated strings
- VACATION RENTAL RESTRICTIONS: hotel_class, brands, free_cancellation, special_offers, eco_certified are HOTELS ONLY
- When vacation_rentals=True, use bedrooms/bathrooms filters instead of hotel_class
- Brand IDs are available in the API response brands array
- Property type and amenity IDs are documented in SerpApi's reference pages

GOOGLE LOCAL API PARAMETER REFERENCE:
===================================

REQUIRED PARAMETERS:
- query: Search query for local businesses (e.g., "restaurants", "coffee shops near me")

BASIC PARAMETERS:
- location: Location to search in (e.g., "New York, NY", "London, UK") - OPTIONAL (uses IP-based detection if not provided)
- hl: Language code - "en", "es", "fr", "de", etc. (default: "en")
- gl: Country code - "us", "uk", "ca", "de", etc. (default: "us")  
- num: Number of results to return (default: 10, max: 20)

PAGINATION:
- start: Starting point for pagination (e.g., 0, 10, 20)

RESPONSE STRUCTURE:
- local_results: Array of local business results
- search_metadata: Search parameters and metadata
- discover_more_places: Related search suggestions
- pagination: Pagination information with next page details

NOTES:
- Location parameter is optional - API can use IP-based location detection
- Query can include location hints like "near me", "in downtown", etc.
- Response includes comprehensive business info: ratings, reviews, service options, GPS coordinates
- Service options include: dine_in, takeout, delivery, curbside_pickup, etc.
"""

from src.tools.serpapi_tools import (
    google_events_search,
    google_flights_search,
    google_hotels_search,
    google_local_search
)
import os
import sys
import json
from datetime import datetime, timedelta

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def test_events():
    """Simple events test."""
    print("\n🎭 Testing Events Search...")
    result = google_events_search.invoke({
        "query": "concerts",
        "location": "Los Angeles",
        "num_results": 2
    })

    # Handle potential error responses
    if isinstance(result, str) and result.startswith("Error"):
        print(f"API Error: {result}")
        return

    try:
        data = json.loads(result)
        if data.get('error'):
            print(f"API Error: {data['error']}")
            return

        events = data.get('events', [])
        print(f"Found {len(events)} events")
        if events:
            event = events[0]
            print(f"First event: {event['title']}")
            print(f"Date: {event['date']['when']}")
            if event['venue']['name']:
                print(f"Venue: {event['venue']['name']}")
    except json.JSONDecodeError:
        print(f"Failed to parse response: {result[:200]}...")
    except Exception as e:
        print(f"Unexpected error: {e}")


def test_events_advanced():
    """Advanced events test with filtering."""
    print("\n🎭 Testing Advanced Events Search with Filters...")
    result = google_events_search.invoke({
        "query": "tech meetup",
        "location": "San Francisco",
        "htichips": "event_type:Virtual-Event,price:free",
        "num_results": 3
    })

    try:
        data = json.loads(result)
        if data.get('error'):
            print(f"API Error: {data['error']}")
            return

        events = data.get('events', [])
        search_meta = data.get('search_metadata', {})

        print(f"Found {len(events)} virtual free tech events")
        print(f"Applied filters: {search_meta.get('filters', 'None')}")

        for i, event in enumerate(events[:2]):
            print(f"\n{i+1}. {event['title']}")
            print(f"   Type: {event.get('event_type', 'N/A')}")
            print(f"   Price: {event.get('price') or 'Free'}")

    except json.JSONDecodeError:
        print(f"Failed to parse response: {result[:200]}...")
    except Exception as e:
        print(f"Unexpected error: {e}")


def test_flights():
    """Simple flights test."""
    print("\n✈️  Testing Flights Search...")
    # Use future dates
    today = datetime.now()
    outbound_date = (today + timedelta(days=30)).strftime("%Y-%m-%d")

    result = google_flights_search.invoke({
        "departure_id": "JFK",
        "arrival_id": "LAX",
        "outbound_date": outbound_date,
        "adults": 1,
        "travel_class": "1"  # Economy
    })

    # Handle potential error responses
    if isinstance(result, str) and result.startswith("Error"):
        print(f"API Error: {result}")
        return

    try:
        data = json.loads(result)
        if data.get('error'):
            print(f"API Error: {data['error']}")
            return

        print(f"Found {len(data.get('best_flights', []))} best flight options")
        if data.get('best_flights'):
            best_flight = data['best_flights'][0]
            print(f"Cheapest: {best_flight.get('price', 'N/A')}")
            print(
                f"Duration: {best_flight.get('total_duration', 'N/A')} minutes")
    except json.JSONDecodeError:
        print(f"Failed to parse response: {result[:200]}...")
    except Exception as e:
        print(f"Unexpected error: {e}")


def test_hotels():
    """Simple hotels test."""
    print("\n🏨 Testing Hotels Search...")
    # Use future dates
    today = datetime.now()
    check_in_date = (today + timedelta(days=30)).strftime("%Y-%m-%d")
    check_out_date = (today + timedelta(days=32)).strftime("%Y-%m-%d")

    result = google_hotels_search.invoke({
        "q": "San Francisco",
        "check_in_date": check_in_date,
        "check_out_date": check_out_date
    })

    # Handle potential error responses
    if isinstance(result, str) and result.startswith("Error"):
        print(f"API Error: {result}")
        return

    try:
        data = json.loads(result)
        if data.get('error'):
            print(f"API Error: {data['error']}")
            return

        if 'property_details' in data:
            # Single property details
            details = data['property_details']
            print(f"Property Details: {details['name']}")
            print(f"Rate: {details['rate_per_night'].get('lowest', 'N/A')}")
        else:
            # Multiple properties
            properties = data.get('properties', [])
            print(f"Found {len(properties)} properties")
            if properties:
                hotel = properties[0]
                print(
                    f"First hotel: {hotel['name']} - {hotel['rate_per_night']['lowest']}")
                if hotel['overall_rating']:
                    print(
                        f"Rating: {hotel['overall_rating']} ({hotel['reviews']} reviews)")
    except json.JSONDecodeError:
        print(f"Failed to parse response: {result[:200]}...")
    except Exception as e:
        print(f"Unexpected error: {e}")


def test_hotels_advanced():
    """Advanced hotels test with vacation rentals."""
    print("\n🏨 Testing Advanced Hotels Search with Vacation Rentals...")
    today = datetime.now()
    check_in_date = (today + timedelta(days=30)).strftime("%Y-%m-%d")
    check_out_date = (today + timedelta(days=37)).strftime("%Y-%m-%d")

    result = google_hotels_search.invoke({
        "q": "Miami Beach",
        "check_in_date": check_in_date,
        "check_out_date": check_out_date,
        "adults": 4,
        "vacation_rentals": True,
        "min_price": 150
        # Note: hotel_class and free_cancellation not available for vacation rentals
    })

    try:
        data = json.loads(result)
        if data.get('error'):
            print(f"API Error: {data['error']}")
            return

        properties = data.get('properties', [])
        search_meta = data.get('search_metadata', {})

        print(f"Found {len(properties)} properties (hotels + vacation rentals)")
        print(f"For {search_meta['guests']['adults']} adults")

        for i, property_item in enumerate(properties[:2]):
            print(f"\n{i+1}. {property_item['name']}")
            print(f"   Nightly: {property_item['rate_per_night']['lowest']}")
            print(f"   Total: {property_item['total_rate']['lowest']}")
            if property_item.get('essential_info'):
                print(
                    f"   Details: {', '.join(property_item['essential_info'][:2])}")

        pagination = data.get('pagination', {})
        if pagination.get('has_next_page'):
            print(f"\nPagination: More results available")

    except json.JSONDecodeError:
        print(f"Failed to parse response: {result[:200]}...")
    except Exception as e:
        print(f"Unexpected error: {e}")


def test_local():
    """Simple local search test."""
    print("\n📍 Testing Local Search...")
    result = google_local_search.invoke({
        "query": "pizza",
        "location": "Chicago",
        "num": 3
    })

    # Handle potential error responses
    if isinstance(result, str) and result.startswith("Error"):
        print(f"API Error: {result}")
        return

    try:
        data = json.loads(result)
        if data.get('error'):
            print(f"API Error: {data['error']}")
            return

        local_results = data.get('local_results', [])
        search_metadata = data.get('search_metadata', {})

        print(f"Found {len(local_results)} places")
        print(
            f"Search query: '{search_metadata.get('query')}' in {search_metadata.get('location')}")

        for i, place in enumerate(local_results):
            print(f"{i+1}. {place['title']} (Rating: {place['rating']})")
            if place.get('price_level'):
                print(f"   Price: {place['price_level']}")
            if place.get('address'):
                print(f"   Address: {place['address']}")

    except json.JSONDecodeError:
        print(f"Failed to parse response: {result[:200]}...")
    except Exception as e:
        print(f"Unexpected error: {e}")


def test_local_no_location():
    """Test local search without explicit location (uses IP-based detection)."""
    print("\n📍 Testing Local Search without Location (IP-based)...")
    result = google_local_search.invoke({
        "query": "coffee shops near me",
        "num": 2
    })

    try:
        data = json.loads(result)
        if data.get('error'):
            print(f"API Error: {data['error']}")
            return

        local_results = data.get('local_results', [])
        search_metadata = data.get('search_metadata', {})

        print(f"Found {len(local_results)} places")
        print(f"IP-based location detection used")
        print(f"Search query: '{search_metadata.get('search_query')}'")

        for i, place in enumerate(local_results):
            print(f"{i+1}. {place['title']}")
            if place.get('service_options'):
                options = []
                if place['service_options'].get('dine_in'):
                    options.append('dine-in')
                if place['service_options'].get('takeout'):
                    options.append('takeout')
                if place['service_options'].get('delivery'):
                    options.append('delivery')
                if options:
                    print(f"   Options: {', '.join(options)}")

    except json.JSONDecodeError:
        print(f"Failed to parse response: {result[:200]}...")
    except Exception as e:
        print(f"Unexpected error: {e}")


def test_local_advanced():
    """Test local search with advanced parameters (language, country, pagination)."""
    print("\n📍 Testing Advanced Local Search...")
    result = google_local_search.invoke({
        "query": "restaurants",
        "location": "Paris, France",
        "hl": "fr",  # French language
        "gl": "fr",  # France country
        "num": 2,
        "start": 0
    })

    try:
        data = json.loads(result)
        if data.get('error'):
            print(f"API Error: {data['error']}")
            return

        local_results = data.get('local_results', [])
        search_metadata = data.get('search_metadata', {})
        pagination = data.get('pagination', {})
        discover_more = data.get('discover_more_places', [])

        print(f"Found {len(local_results)} restaurants")
        print(
            f"Language: {search_metadata.get('language')}, Country: {search_metadata.get('country')}")

        for i, place in enumerate(local_results):
            print(f"\n{i+1}. {place['title']}")
            print(
                f"   Rating: {place.get('rating')} ({place.get('reviews', {}).get('original_text', 'No reviews')})")
            print(f"   Type: {place.get('type', 'N/A')}")
            if place.get('gps_coordinates', {}).get('latitude'):
                coords = place['gps_coordinates']
                print(
                    f"   Location: {coords['latitude']:.4f}, {coords['longitude']:.4f}")

        # Show discover more section
        if discover_more:
            print(f"\nDiscover More Places:")
            for item in discover_more[:2]:
                print(f"  - {item.get('title', 'N/A')}")

        # Show pagination info
        if pagination.get('has_more_results'):
            print(f"\nPagination: More results available")
            print(f"Current page: {pagination.get('current', 1)}")

    except json.JSONDecodeError:
        print(f"Failed to parse response: {result[:200]}...")
    except Exception as e:
        print(f"Unexpected error: {e}")


def test_flights_advanced():
    """Advanced flights test with more parameters."""
    print("\n✈️  Testing Advanced Flights Search...")
    today = datetime.now()
    outbound_date = (today + timedelta(days=30)).strftime("%Y-%m-%d")
    return_date = (today + timedelta(days=37)).strftime("%Y-%m-%d")

    result = google_flights_search.invoke({
        "departure_id": "LAX",
        "arrival_id": "NRT",  # Tokyo Narita
        "outbound_date": outbound_date,
        "return_date": return_date,
        "adults": 2,
        "travel_class": "3",  # Business class
        "currency": "USD",
        "stops": 1,  # Max 1 stop
        "max_price": 5000,
        "include_airlines": "UA,NH,AA"  # United, ANA, American
    })

    try:
        data = json.loads(result)
        if data.get('error'):
            print(f"API Error: {data['error']}")
            return

        print(f"Found {len(data.get('best_flights', []))} best flight options")
        if data.get('search_metadata'):
            metadata = data['search_metadata']
            print(
                f"Route: {metadata['departure_id']} → {metadata['arrival_id']}")
            print(f"Trip Type: {metadata['trip_type']}")
            print(f"Travel Class: {metadata['travel_class']}")

        if data.get('best_flights'):
            best_flight = data['best_flights'][0]
            print(f"Best Option: {best_flight.get('price', 'N/A')}")
            print(f"Layovers: {len(best_flight.get('layovers', []))}")

    except json.JSONDecodeError:
        print(f"Failed to parse response: {result[:200]}...")
    except Exception as e:
        print(f"Unexpected error: {e}")


def test_google_hotels_basic():
    """Test basic Google Hotels search functionality."""
    try:
        # Use future dates
        today = datetime.now()
        check_in_date = (today + timedelta(days=30)).strftime("%Y-%m-%d")
        check_out_date = (today + timedelta(days=33)).strftime("%Y-%m-%d")

        result = google_hotels_search.invoke({
            "q": "Paris, France",
            "check_in_date": check_in_date,
            "check_out_date": check_out_date
        })

        data = json.loads(result)
        print("\n=== GOOGLE HOTELS BASIC TEST ===")
        print(f"Search query: Paris, France")
        print(f"Properties found: {len(data.get('properties', []))}")

        # Show first property
        if data.get('properties'):
            prop = data['properties'][0]
            print(f"Sample property: {prop.get('name', 'N/A')}")
            print(f"Rating: {prop.get('overall_rating', 'N/A')}")
            print(
                f"Price per night: {prop.get('rate_per_night', {}).get('lowest', 'N/A')}")

    except Exception as e:
        print(f"Google Hotels basic test failed: {e}")


def test_google_hotels_advanced():
    """Test Google Hotels search with advanced filtering and new parameters."""
    try:
        # Use future dates
        today = datetime.now()
        check_in_date = (today + timedelta(days=30)).strftime("%Y-%m-%d")
        check_out_date = (today + timedelta(days=33)).strftime("%Y-%m-%d")

        result = google_hotels_search.invoke({
            "q": "New York",
            "check_in_date": check_in_date,
            "check_out_date": check_out_date,
            "adults": 2,
            "children": 2,
            "children_ages": "8,12",  # NEW: Ages of children
            "currency": "USD",
            "hl": "en",
            "gl": "us",
            "min_price": 150,
            "max_price": 400,
            "rating": "8",  # UPDATED: 4.0+ rating (string value)
            # UPDATED: 4 and 5 star hotels (comma-separated)
            "hotel_class": "4,5",
            "sort_by": "8",  # UPDATED: Sort by highest rating (numeric string)
            "free_cancellation": True,
            "amenities": "35,9,19",  # NEW: Multiple amenity IDs
            "brands": "33,67",  # NEW: Multiple brand IDs
            "special_offers": True,  # NEW: Filter for special offers
            "eco_certified": True  # NEW: Filter for eco-certified properties
        })

        data = json.loads(result)
        print("\n=== GOOGLE HOTELS ADVANCED TEST ===")
        print(f"Search query: New York (4-5 star, 4.0+ rating)")
        print(f"Adults: 2, Children: 2 (ages 8, 12)")
        print(f"Properties found: {len(data.get('properties', []))}")

        # Show filtering results
        if data.get('properties'):
            for i, prop in enumerate(data['properties'][:3]):
                print(f"\nProperty {i+1}: {prop.get('name', 'N/A')}")
                print(f"  Stars: {prop.get('hotel_class', 'N/A')}")
                print(f"  Rating: {prop.get('overall_rating', 'N/A')}")
                print(
                    f"  Price: {prop.get('rate_per_night', {}).get('lowest', 'N/A')}")
                print(
                    f"  Free cancellation: {prop.get('free_cancellation', 'N/A')}")

    except Exception as e:
        print(f"Google Hotels advanced test failed: {e}")


def test_google_hotels_vacation_rentals():
    """Test Google Hotels search for vacation rentals with new parameters."""
    try:
        # Use future dates
        today = datetime.now()
        check_in_date = (today + timedelta(days=30)).strftime("%Y-%m-%d")
        check_out_date = (today + timedelta(days=37)).strftime("%Y-%m-%d")

        result = google_hotels_search.invoke({
            "q": "Bali",
            "check_in_date": check_in_date,
            "check_out_date": check_out_date,
            "adults": 4,
            "vacation_rentals": True,
            "bedrooms": 2,  # NEW: Minimum bedrooms for vacation rentals
            "bathrooms": 2,  # NEW: Minimum bathrooms for vacation rentals
            "sort_by": "3",  # UPDATED: Sort by lowest price
            "min_price": 50,
            "max_price": 200
            # Note: hotel_class, brands, free_cancellation, special_offers, eco_certified not available for vacation rentals
        })

        data = json.loads(result)
        print("\n=== GOOGLE HOTELS VACATION RENTALS TEST ===")
        print(f"Search query: Bali vacation rentals")
        print(f"Requirements: 4 adults, 2+ bedrooms, 2+ bathrooms")
        print(f"Properties found: {len(data.get('properties', []))}")

        # Show vacation rental details
        if data.get('properties'):
            for i, prop in enumerate(data['properties'][:2]):
                print(f"\nVacation Rental {i+1}: {prop.get('name', 'N/A')}")
                print(f"  Type: {prop.get('type', 'N/A')}")
                print(f"  Essential info: {prop.get('essential_info', [])}")
                print(
                    f"  Price: {prop.get('rate_per_night', {}).get('lowest', 'N/A')}")
                # Show first 3
                print(f"  Amenities: {prop.get('amenities', [])[:3]}")

    except Exception as e:
        print(f"Google Hotels vacation rentals test failed: {e}")


if __name__ == "__main__":
    if not os.getenv("SERPAPI_API_KEY"):
        print("❌ ERROR: Set SERPAPI_API_KEY environment variable first!")
        sys.exit(1)

    print("SerpApi Tools Quick Test")
    print("=" * 30)

    try:
        # Events Tests
        test_events()
        test_events_advanced()

        # Flights Tests
        test_flights()
        test_flights_advanced()

        # Hotels Tests
        test_hotels()
        test_hotels_advanced()
        test_google_hotels_basic()
        test_google_hotels_advanced()
        test_google_hotels_vacation_rentals()

        # Local Search Tests
        test_local()
        test_local_no_location()
        test_local_advanced()

        print("\n✅ All tests completed!")
    except Exception as e:
        print(f"\n❌ Error: {e}")
