# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from typing import Optional
from langgraph.graph import MessagesState

from src.prompts.planner_model import Plan, Step
from src.rag import Resource


class State(MessagesState):
    """State for the agent system, extends MessagesState with next field."""

    # Runtime Variables
    research_topic: str
    observations: list[str]
    resources: list[Resource]
    plan_iterations: int
    current_plan: Optional[Plan | str]
    final_report: str
    auto_accepted_plan: bool
    enable_background_investigation: bool
    background_investigation_results: Optional[str]
    agent_type: str
    step_feedback_enabled: bool
