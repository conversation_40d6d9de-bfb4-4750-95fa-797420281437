# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from src.prompts.planner_model import StepType
import logging

from .types import State
from .nodes import (
    coordinator_node,
    planner_node,
    reporter_node,
    research_team_node,
    researcher_node,
    coder_node,
    human_feedback_node,
    background_investigation_node,
    step_feedback_node,
)

logger = logging.getLogger(__name__)


def continue_to_running_research_team(state: State):
    current_plan = state.get("current_plan")
    if not current_plan:
        logger.info("No current plan, going to planner")
        return "planner"
    
    # 处理current_plan可能是字符串的情况
    if isinstance(current_plan, str):
        logger.info("Current plan is string, going to planner")
        return "planner"
    
    if not current_plan.steps:
        logger.info("No steps in current plan, going to planner")
        return "planner"
    
    logger.info(f"=== FLOW CONTROL DEBUG ===")
    logger.info(f"Total steps: {len(current_plan.steps)}")
    for i, step in enumerate(current_plan.steps):
        logger.info(f"Step {i}: title='{step.title}', execution_res={bool(step.execution_res)}, feedback_confirmed={step.feedback_confirmed}")
    
    # 优先进入step_feedback：有execution_res但未确认的step
    for i, step in enumerate(current_plan.steps):
        if step.execution_res and not step.feedback_confirmed:
            logger.info(f"=== DECISION: Found step {i} needing feedback, going to step_feedback ===")
            return "step_feedback"
    
    # 找到第一个未完成或未确认的step，继续执行当前step
    for i, step in enumerate(current_plan.steps):
        if not step.execution_res or not step.feedback_confirmed:
            logger.info(f"=== DECISION: Found step {i} needing execution: execution_res={bool(step.execution_res)}, feedback_confirmed={step.feedback_confirmed} ===")
            if step.step_type == StepType.RESEARCH:
                logger.info("=== GOING TO: researcher ===")
                return "researcher"
            elif step.step_type == StepType.PROCESSING:
                logger.info("=== GOING TO: coder ===")
                return "coder"
            else:
                logger.info("=== GOING TO: planner (unknown step type) ===")
                return "planner"
    
    # 所有step都已完成且已确认，进入planner
    logger.info("=== DECISION: All steps completed and confirmed, going to planner ===")
    return "planner"


def _build_base_graph():
    """Build and return the base state graph with all nodes and edges."""
    builder = StateGraph(State)
    builder.add_edge(START, "coordinator")
    builder.add_node("coordinator", coordinator_node)
    builder.add_node("background_investigator", background_investigation_node)
    builder.add_node("planner", planner_node)
    builder.add_node("reporter", reporter_node)
    builder.add_node("research_team", research_team_node)
    builder.add_node("researcher", researcher_node)
    builder.add_node("coder", coder_node)
    builder.add_node("human_feedback", human_feedback_node)
    builder.add_node("step_feedback", step_feedback_node)
    builder.add_edge("background_investigator", "planner")
    builder.add_conditional_edges(
        "research_team",
        continue_to_running_research_team,
        ["planner", "researcher", "coder", "step_feedback"],
    )
    builder.add_edge("reporter", END)
    return builder


def build_graph_with_memory():
    """Build and return the agent workflow graph with memory."""
    # use persistent memory to save conversation history
    # TODO: be compatible with SQLite / PostgreSQL
    memory = MemorySaver()

    # build state graph
    builder = _build_base_graph()
    return builder.compile(checkpointer=memory)


def build_graph():
    """Build and return the agent workflow graph without memory."""
    # build state graph
    builder = _build_base_graph()
    return builder.compile()


graph = build_graph()
