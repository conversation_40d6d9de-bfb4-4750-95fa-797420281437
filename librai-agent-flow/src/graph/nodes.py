# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import os
from typing import Annotated, Literal

from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from langgraph.types import Command, interrupt
from langgraph.graph.message import add_messages
from langchain_mcp_adapters.client import MultiServerMCPClient

from src.agents import create_agent
from src.tools.search import LoggedTavilySearch
from src.tools import (
    crawl_tool,
    get_web_search_tool,
    get_retriever_tool,
    python_repl_tool,
)

from src.config.agents import AGENT_LLM_MAP
from src.config.configuration import Configuration
from src.llms.llm import get_llm_by_type
from src.prompts.planner_model import Plan, Step
from src.prompts.template import apply_prompt_template
from src.utils.json_utils import repair_json_output
from src.utils.step_quality_checker import check_step_errors

from .types import State
from ..config import SELECTED_SEARCH_ENGINE, SearchEngine

logger = logging.getLogger(__name__)


@tool
def handoff_to_planner(
    research_topic: Annotated[str, "The topic of the research task to be handed off."],
    agent_type: Annotated[str, "The type of agent to use for this task (e.g., deep_search, tourism_planner)."] = "deep_search",
):
    """Handoff to planner agent to do plan."""
    # This tool is not returning anything: we're just using it
    # as a way for LLM to signal that it needs to hand off to planner agent
    return


def background_investigation_node(state: State, config: RunnableConfig):
    logger.info("background investigation node is running.")
    configurable = Configuration.from_runnable_config(config)
    query = state.get("research_topic")
    background_investigation_results = None
    if SELECTED_SEARCH_ENGINE == SearchEngine.TAVILY.value:
        searched_content = LoggedTavilySearch(
            max_results=configurable.max_search_results
        ).invoke(query)
        if isinstance(searched_content, list):
            background_investigation_results = [
                f"## {elem['title']}\n\n{elem['content']}" for elem in searched_content
            ]
            return {
                "background_investigation_results": "\n\n".join(
                    background_investigation_results
                )
            }
        else:
            logger.error(
                f"Tavily search returned malformed response: {searched_content}"
            )
    else:
        background_investigation_results = get_web_search_tool(
            configurable.max_search_results
        ).invoke(query)
    return {
        "background_investigation_results": json.dumps(
            background_investigation_results, ensure_ascii=False
        )
    }


def planner_node(
    state: State, config: RunnableConfig
) -> Command[Literal["human_feedback", "reporter"]]:
    """Planner node that generate the full plan."""
    logger.info("Planner generating full plan")
    configurable = Configuration.from_runnable_config(config)
    plan_iterations = state["plan_iterations"] if state.get("plan_iterations", 0) else 0
    messages = apply_prompt_template("planner", state, configurable)

    if state.get("enable_background_investigation") and state.get(
        "background_investigation_results"
    ):
        messages += [
            {
                "role": "user",
                "content": (
                    "background investigation results of user query:\n"
                    + state["background_investigation_results"]
                    + "\n"
                ),
            }
        ]

    if configurable.enable_deep_thinking:
        llm = get_llm_by_type("reasoning")
    elif AGENT_LLM_MAP["planner"] == "basic":
        llm = get_llm_by_type("basic").with_structured_output(
            Plan,
            method="json_mode",
        )
    else:
        llm = get_llm_by_type(AGENT_LLM_MAP["planner"])

    # if the plan iterations is greater than the max plan iterations, return the reporter node
    if plan_iterations >= configurable.max_plan_iterations:
        return Command(goto="reporter")

    full_response = ""
    if AGENT_LLM_MAP["planner"] == "basic" and not configurable.enable_deep_thinking:
        response = llm.invoke(messages)
        full_response = response.model_dump_json(indent=4, exclude_none=True)
    else:
        response = llm.stream(messages)
        for chunk in response:
            full_response += chunk.content
    logger.debug(f"Current state messages: {state['messages']}")
    # Log the response (truncated to 200 characters for readability)
    response_preview = full_response[:200] + "... (truncated)" if len(full_response) > 200 else full_response
    logger.info(f"Planner response: {response_preview}")

    try:
        curr_plan = json.loads(repair_json_output(full_response))
    except json.JSONDecodeError:
        logger.warning("Planner response is not a valid JSON")
        if plan_iterations > 0:
            return Command(goto="reporter")
        else:
            return Command(goto="__end__")
    if curr_plan.get("has_enough_context"):
        logger.info("Planner response has enough context.")
        new_plan = Plan.model_validate(curr_plan)
        return Command(
            update={
                "messages": [AIMessage(content=full_response, name="planner")],
                "current_plan": new_plan,
            },
            goto="reporter",
        )
    return Command(
        update={
            "messages": [AIMessage(content=full_response, name="planner")],
            "current_plan": full_response,
        },
        goto="human_feedback",
    )


def step_feedback_node(
    state: State, config: RunnableConfig
) -> Command:
    """Step feedback node that asks user to confirm step completion."""
    logger.info("=== STEP FEEDBACK NODE START ===")
    current_plan = state.get("current_plan")
    
    # 检查current_plan是否存在且为Plan对象
    if not current_plan or isinstance(current_plan, str):
        logger.info("No valid current_plan found, going to research_team")
        return Command(goto="research_team")
    
    if not current_plan.steps:
        logger.info("No steps in current_plan, going to research_team")
        return Command(goto="research_team")
    
    # 找到第一个已执行但未确认的step
    current_step = None
    current_index = None
    for idx, step in enumerate(current_plan.steps):
        if step.execution_res and not step.feedback_confirmed:
            current_step = step
            current_index = idx
            break
    
    if not current_step or current_index is None:
        logger.info("No step needs feedback, going to research_team")
        return Command(goto="research_team")
    
    logger.info(f"=== REQUESTING FEEDBACK FOR STEP {current_index}: {current_step.title} ===")
    
    # 构建反馈消息
    feedback_message = f"""## Step Execution Result

**Step Title**: {current_step.title}
**Step Description**: {current_step.description}

**Execution Result**:
{current_step.execution_res}

"""
    
    # 添加错误提示
    if current_step.has_errors:
        feedback_message += """## ⚠️ Error Detected

The system detected potential issues with this step execution. Please review carefully.

"""
        
        # 添加具体的错误信息
        if current_step.error_summary:
            feedback_message += f"""### Error Details

{current_step.error_summary}

"""
    
    feedback_message += """**Please review the above result and provide feedback:**

1. **If the step is complete and satisfactory**: Reply with `[STEP_ACCEPTED]`
2. **If the step needs more work**: Reply with `[STEP_NEEDS_MORE_WORK]` followed by your specific requirements
3. **If the step needs to be redone**: Reply with `[STEP_REDO]` followed by your specific requirements

**Note**: This feedback is specifically for this individual step, not the overall plan."""
    
    # 获取用户反馈
    feedback = interrupt(feedback_message)
    logger.info(f"=== USER FEEDBACK RECEIVED: {feedback[:100]}... ===")
    
    # 处理用户反馈
    if feedback and (str(feedback).upper().startswith("[STEP_ACCEPTED]") or str(feedback).upper().startswith("[[STEP_ACCEPTED]]")):
        logger.info(f"=== PROCESSING: Step '{current_step.title}' accepted by user ===")
        # 创建新的step对象，标记为已确认
        new_step = Step(
            need_search=current_step.need_search,
            title=current_step.title,
            description=current_step.description,
            step_type=current_step.step_type,
            execution_res=current_step.execution_res,
            feedback_confirmed=True,
            has_errors=current_step.has_errors,
            error_reasons=current_step.error_reasons,
            error_summary=current_step.error_summary
        )
        # 更新steps列表
        new_steps = current_plan.steps.copy()
        new_steps[current_index] = new_step
        # 创建新的plan对象
        new_plan = Plan(
            has_enough_context=current_plan.has_enough_context,
            thought=current_plan.thought,
            title=current_plan.title,
            steps=new_steps,
            clarification_questions=current_plan.clarification_questions,
            clarification_questions_with_options=current_plan.clarification_questions_with_options
        )
        logger.info(f"=== RESULT: Step {current_index} marked as confirmed, going to research_team ===")
        return Command(
            update={
                "current_plan": new_plan,
                "messages": add_messages(state.get("messages", []), [HumanMessage(content=feedback, name="step_feedback")]),
            },
            goto="research_team",
        )
    
    elif feedback and (str(feedback).upper().startswith("[STEP_NEEDS_MORE_WORK]") or str(feedback).upper().startswith("[[STEP_NEEDS_MORE_WORK]]")):
        logger.info(f"=== PROCESSING: Step '{current_step.title}' needs more work ===")
        # 保留当前信息，标记为未确认
        new_step = Step(
            need_search=current_step.need_search,
            title=current_step.title,
            description=current_step.description,
            step_type=current_step.step_type,
            execution_res=current_step.execution_res,
            feedback_confirmed=False,
            has_errors=current_step.has_errors,
            error_reasons=current_step.error_reasons,
            error_summary=current_step.error_summary
        )
        # 更新steps列表
        new_steps = current_plan.steps.copy()
        new_steps[current_index] = new_step
        # 创建新的plan对象
        new_plan = Plan(
            has_enough_context=current_plan.has_enough_context,
            thought=current_plan.thought,
            title=current_plan.title,
            steps=new_steps,
            clarification_questions=current_plan.clarification_questions,
            clarification_questions_with_options=current_plan.clarification_questions_with_options
        )
        next_node = "researcher" if current_step.step_type == "research" else "coder"
        logger.info(f"=== RESULT: Step {current_index} needs more work, going to {next_node} ===")
        return Command(
            update={
                "current_plan": new_plan,
                "messages": add_messages(state.get("messages", []), [HumanMessage(content=feedback, name="step_feedback")]),
            },
            goto=next_node,
        )
    
    elif feedback and (str(feedback).upper().startswith("[STEP_REDO]") or str(feedback).upper().startswith("[[STEP_REDO]]")):
        logger.info(f"=== PROCESSING: Step '{current_step.title}' needs to be redone ===")
        # 清除当前信息，重新执行
        new_step = Step(
            need_search=current_step.need_search,
            title=current_step.title,
            description=current_step.description,
            step_type=current_step.step_type,
            execution_res=None,
            feedback_confirmed=False,
            has_errors=False,  # 重新执行时重置错误状态
            error_reasons=None,
            error_summary=None
        )
        # 更新steps列表
        new_steps = current_plan.steps.copy()
        new_steps[current_index] = new_step
        # 创建新的plan对象
        new_plan = Plan(
            has_enough_context=current_plan.has_enough_context,
            thought=current_plan.thought,
            title=current_plan.title,
            steps=new_steps,
            clarification_questions=current_plan.clarification_questions,
            clarification_questions_with_options=current_plan.clarification_questions_with_options
        )
        next_node = "researcher" if current_step.step_type == "research" else "coder"
        logger.info(f"=== RESULT: Step {current_index} will be redone, going to {next_node} ===")
        return Command(
            update={
                "current_plan": new_plan,
                "messages": add_messages(state.get("messages", []), [HumanMessage(content=feedback, name="step_feedback")]),
            },
            goto=next_node,
        )
    
    else:
        # 默认情况下，认为step已完成
        logger.info(f"=== PROCESSING: Default case, Step '{current_step.title}' marked as completed ===")
        logger.info(f"=== DEBUG: Unrecognized feedback format: '{feedback}' ===")
        new_step = Step(
            need_search=current_step.need_search,
            title=current_step.title,
            description=current_step.description,
            step_type=current_step.step_type,
            execution_res=current_step.execution_res,
            feedback_confirmed=True,
            has_errors=current_step.has_errors,
            error_reasons=current_step.error_reasons,
            error_summary=current_step.error_summary
        )
        # 更新steps列表
        new_steps = current_plan.steps.copy()
        new_steps[current_index] = new_step
        # 创建新的plan对象
        new_plan = Plan(
            has_enough_context=current_plan.has_enough_context,
            thought=current_plan.thought,
            title=current_plan.title,
            steps=new_steps,
            clarification_questions=current_plan.clarification_questions,
            clarification_questions_with_options=current_plan.clarification_questions_with_options
        )
        logger.info(f"=== RESULT: Step {current_index} marked as completed by default, going to research_team ===")
        return Command(
            update={
                "current_plan": new_plan,
                "messages": add_messages(state.get("messages", []), [HumanMessage(content=feedback, name="step_feedback")]),
            },
            goto="research_team",
        )


def human_feedback_node(
    state,
) -> Command[Literal["planner", "research_team", "reporter", "__end__"]]:
    current_plan = state.get("current_plan", "")
    # check if the plan is auto accepted
    auto_accepted_plan = state.get("auto_accepted_plan", False)
    
    if not auto_accepted_plan:
        # 检查是否有选项化的追问
        plan_data = None
        try:
            if isinstance(current_plan, str):
                plan_data = json.loads(repair_json_output(current_plan))
            elif hasattr(current_plan, 'model_dump'):
                plan_data = current_plan.model_dump()
            else:
                plan_data = current_plan
        except:
            plan_data = None
            
        has_interactive_questions = (
            plan_data and 
            plan_data.get("clarification_questions_with_options") and 
            len(plan_data.get("clarification_questions_with_options", [])) > 0
        )
        
        if has_interactive_questions:
            # 使用选项化的追问
            feedback = interrupt("Please answer the clarification questions to improve the research plan.")
        else:
            # 使用传统的追问方式
            feedback = interrupt("Please Review the Plan.")

        # if the feedback is not accepted, return the planner node
        if feedback and str(feedback).upper().startswith("[EDIT_PLAN]"):
            return Command(
                update={
                    "messages": add_messages(state["messages"], [HumanMessage(content=feedback, name="feedback")]),
                },
                goto="planner",
            )
        elif feedback and str(feedback).upper().startswith("[ACCEPTED]"):
            logger.info("Plan is accepted by user.")
        elif feedback and str(feedback).upper().startswith("[START_SEARCH]"):
            logger.info("User clicked Start Search button.")
        elif feedback and str(feedback).upper().startswith("[CLARIFICATION_RESPONSE]"):
            # 处理追问响应，重新生成计划
            logger.info("User provided clarification responses, regenerating plan.")
            return Command(
                update={
                    "messages": add_messages(state["messages"], [HumanMessage(content=feedback, name="clarification_feedback")]),
                },
                goto="planner",
            )
        else:
            raise TypeError(f"Interrupt value of {feedback} is not supported.")

    # if the plan is accepted, run the following node
    plan_iterations = state["plan_iterations"] if state.get("plan_iterations", 0) else 0
    goto = "research_team"
    try:
        current_plan = repair_json_output(current_plan)
        # increment the plan iterations
        plan_iterations += 1
        # parse the plan
        new_plan = json.loads(current_plan)
        if new_plan["has_enough_context"]:
            goto = "reporter"
    except json.JSONDecodeError:
        logger.warning("Planner response is not a valid JSON")
        if plan_iterations > 1:  # the plan_iterations is increased before this check
            return Command(goto="reporter")
        else:
            return Command(goto="__end__")

    return Command(
        update={
            "current_plan": Plan.model_validate(new_plan),
            "plan_iterations": plan_iterations,
        },
        goto=goto,
    )


def coordinator_node(
    state: State, config: RunnableConfig
) -> Command[Literal["planner", "background_investigator", "__end__"]]:
    """Coordinator node that communicate with customers."""
    logger.info("Coordinator talking.")
    configurable = Configuration.from_runnable_config(config)
    messages = apply_prompt_template("coordinator", state)
    response = (
        get_llm_by_type(AGENT_LLM_MAP["coordinator"])
        .bind_tools([handoff_to_planner])
        .invoke(messages)
    )
    logger.debug(f"Current state messages: {state['messages']}")

    goto = "__end__"
    research_topic = state.get("research_topic", "")
    agent_type = state.get("agent_type", "deep_search")  # Default agent_type if not specified

    if len(response.tool_calls) > 0:
        goto = "planner"
        if state.get("enable_background_investigation"):
            # if the search_before_planning is True, add the web search tool to the planner agent
            goto = "background_investigator"
        try:
            for tool_call in response.tool_calls:
                if tool_call.get("name", "") != "handoff_to_planner":
                    continue
                args = tool_call.get("args", {})
                if args.get("research_topic"):
                    research_topic = args.get("research_topic")
                    # Set agent_type if provided, otherwise keep default
                    if args.get("agent_type"):
                        agent_type = args.get("agent_type")
                    break
        except Exception as e:
            logger.error(f"Error processing tool calls: {e}")
    else:
        logger.warning(
            "Coordinator response contains no tool calls. Terminating workflow execution."
        )
        logger.debug(f"Coordinator response: {response}")

    return Command(
        update={
            "research_topic": research_topic,
            "agent_type": agent_type,
            "resources": configurable.resources,
        },
        goto=goto,
    )


def reporter_node(state: State, config: RunnableConfig):
    """Reporter node that write a final report."""
    logger.info("Reporter write final report")
    configurable = Configuration.from_runnable_config(config)
    current_plan = state.get("current_plan")
    input_ = {
        "messages": [
            HumanMessage(
                f"# Research Requirements\n\n## Task\n\n{current_plan.title}\n\n## Description\n\n{current_plan.thought}"
            )
        ],
        "agent_type": state.get("agent_type", "deep_search"),
    }
    invoke_messages = apply_prompt_template("reporter", input_, configurable)
    observations = state.get("observations", [])

    # Add a reminder about the new report format, citation style, and table usage
    invoke_messages.append(
        HumanMessage(
            content="IMPORTANT: Structure your report according to the format in the prompt. Remember to include:\n\n1. Key Points - A bulleted list of the most important findings\n2. Overview - A brief introduction to the topic\n3. Detailed Analysis - Organized into logical sections\n4. Survey Note (optional) - For more comprehensive reports\n5. Key Citations - List all references at the end\n\nFor citations, DO NOT include inline citations in the text. Instead, place all citations in the 'Key Citations' section at the end using the format: `- [Source Title](URL)`. Include an empty line between each citation for better readability.\n\nPRIORITIZE USING MARKDOWN TABLES for data presentation and comparison. Use tables whenever presenting comparative data, statistics, features, or options. Structure tables with clear headers and aligned columns. Example table format:\n\n| Feature | Description | Pros | Cons |\n|---------|-------------|------|------|\n| Feature 1 | Description 1 | Pros 1 | Cons 1 |\n| Feature 2 | Description 2 | Pros 2 | Cons 2 |",
            name="system",
        )
    )

    for observation in observations:
        invoke_messages.append(
            HumanMessage(
                content=f"Below are some observations for the research task:\n\n{observation}",
                name="observation",
            )
        )
    logger.debug(f"Current invoke messages: {invoke_messages}")
    response = get_llm_by_type(AGENT_LLM_MAP["reporter"]).invoke(invoke_messages)
    response_content = response.content
    # Log the response (truncated to 200 characters for readability)
    response_preview = response_content[:200] + "... (truncated)" if len(response_content) > 200 else response_content
    logger.info(f"reporter response: {response_preview}")

    return {"final_report": response_content}


def research_team_node(state: State):
    """Research team node that collaborates on tasks."""
    logger.info("Research team is collaborating on tasks.")
    pass


async def _execute_agent_step(
    state: State, agent, agent_name: str
) -> Command:
    """Helper function to execute a step using the specified agent."""
    current_plan = state.get("current_plan")
    observations = state.get("observations", [])

    # 检查current_plan是否为Plan对象
    if not current_plan or isinstance(current_plan, str):
        logger.warning("No valid current_plan found")
        return Command(goto="research_team")

    # Find the first unexecuted step
    current_step = None
    completed_steps = []
    current_step_index = None
    for idx, step in enumerate(current_plan.steps):
        if not step.execution_res:
            current_step = step
            current_step_index = idx
            break
        else:
            completed_steps.append(step)

    if not current_step:
        logger.warning("No unexecuted step found")
        return Command(goto="research_team")

    logger.info(f"Executing step: {current_step.title}, agent: {agent_name}")

    # Format completed steps information
    completed_steps_info = ""
    if completed_steps:
        completed_steps_info = "# Existing Research Findings\n\n"
        for i, step in enumerate(completed_steps):
            completed_steps_info += f"## Existing Finding {i + 1}: {step.title}\n\n"
            completed_steps_info += f"<finding>\n{step.execution_res}\n</finding>\n\n"

    # Prepare the input for the agent with completed steps info
    agent_input = {
        "messages": [
            HumanMessage(
                content=f"{completed_steps_info}# Current Task\n\n## Title\n\n{current_step.title}\n\n## Description\n\n{current_step.description}"
            )
        ]
    }

    # Add citation reminder for researcher agent
    if agent_name == "researcher":
        if state.get("resources"):
            resources_info = "**The user mentioned the following resource files:**\n\n"
            for resource in state.get("resources"):
                resources_info += f"- {resource.title} ({resource.description})\n"

            agent_input["messages"].append(
                HumanMessage(
                    content=resources_info
                    + "\n\n"
                    + "You MUST use the **local_search_tool** to retrieve the information from the resource files.",
                )
            )

        agent_input["messages"].append(
            HumanMessage(
                content="IMPORTANT: DO NOT include inline citations in the text. Instead, track all sources and include a References section at the end using link reference format. Include an empty line between each citation for better readability. Use this format for each reference:\n- [Source Title](URL)\n\n- [Another Source](URL)",
                name="system",
            )
        )

    # Invoke the agent
    default_recursion_limit = 25
    try:
        env_value_str = os.getenv("AGENT_RECURSION_LIMIT", str(default_recursion_limit))
        parsed_limit = int(env_value_str)

        if parsed_limit > 0:
            recursion_limit = parsed_limit
            logger.info(f"Recursion limit set to: {recursion_limit}")
        else:
            logger.warning(
                f"AGENT_RECURSION_LIMIT value '{env_value_str}' (parsed as {parsed_limit}) is not positive. "
                f"Using default value {default_recursion_limit}."
            )
            recursion_limit = default_recursion_limit
    except ValueError:
        raw_env_value = os.getenv("AGENT_RECURSION_LIMIT")
        logger.warning(
            f"Invalid AGENT_RECURSION_LIMIT value: '{raw_env_value}'. "
            f"Using default value {default_recursion_limit}."
        )
        recursion_limit = default_recursion_limit

    logger.info(f"Agent input: {agent_input}")
    result = await agent.ainvoke(
        input=agent_input, config={"recursion_limit": recursion_limit}
    )

    # Process the result
    response_content = result["messages"][-1].content
    # Log the response (truncated to 200 characters for readability)
    response_preview = response_content[:200] + "... (truncated)" if len(response_content) > 200 else response_content
    logger.debug(f"{agent_name.capitalize()} full response: {response_preview}")

    # 改进的错误检测
    error_check_result = check_step_errors(response_content)
    has_errors = error_check_result["has_errors"]
    
    # 创建新的step对象
    new_step = Step(
        need_search=current_step.need_search,
        title=current_step.title,
        description=current_step.description,
        step_type=current_step.step_type,
        execution_res=response_content,
        feedback_confirmed=not has_errors,  # 如果没有错误，自动确认
        has_errors=has_errors,
        error_reasons=error_check_result.get("error_reasons"),
        error_summary=error_check_result.get("error_summary")
    )
    
    # 更新steps列表
    new_steps = current_plan.steps.copy()
    new_steps[current_step_index] = new_step
    
    # 创建新的plan对象
    new_plan = Plan(
        has_enough_context=current_plan.has_enough_context,
        thought=current_plan.thought,
        title=current_plan.title,
        steps=new_steps,
        clarification_questions=current_plan.clarification_questions,
        clarification_questions_with_options=current_plan.clarification_questions_with_options
    )
    
    logger.info(f"Step '{current_step.title}' execution completed by {agent_name}")
    logger.info(f"Error detection result: has_errors={has_errors}")
    
    # 根据错误检测结果决定下一步
    if has_errors:
        logger.info("Errors detected, going to step_feedback")
        return Command(
            update={
                "current_plan": new_plan,
                "messages": [
                    HumanMessage(
                        content=response_content,
                        name=agent_name,
                    )
                ],
                "observations": observations + [response_content],
            },
            goto="step_feedback",
        )
    else:
        logger.info("No errors detected, auto-confirming step")
        return Command(
            update={
                "current_plan": new_plan,
                "messages": [
                    HumanMessage(
                        content=response_content,
                        name=agent_name,
                    )
                ],
                "observations": observations + [response_content],
            },
            goto="research_team",
        )


async def _setup_and_execute_agent_step(
    state: State,
    config: RunnableConfig,
    agent_type: str,
    default_tools: list,
) -> Command[Literal["research_team"]]:
    """Helper function to set up an agent with appropriate tools and execute a step.

    This function handles the common logic for both researcher_node and coder_node:
    1. Configures MCP servers and tools based on agent type
    2. Creates an agent with the appropriate tools or uses the default agent
    3. Executes the agent on the current step

    Args:
        state: The current state
        config: The runnable config
        agent_type: The type of agent ("researcher" or "coder")
        default_tools: The default tools to add to the agent

    Returns:
        Command to update state and go to research_team
    """
    configurable = Configuration.from_runnable_config(config)
    mcp_servers = {}
    enabled_tools = {}

    # Extract MCP server configuration for this agent type
    if configurable.mcp_settings:
        for server_name, server_config in configurable.mcp_settings["servers"].items():
            if (
                server_config["enabled_tools"]
                and agent_type in server_config["add_to_agents"]
            ):
                mcp_servers[server_name] = {
                    k: v
                    for k, v in server_config.items()
                    if k in ("transport", "command", "args", "url", "env")
                }
                for tool_name in server_config["enabled_tools"]:
                    enabled_tools[tool_name] = server_name

    # Create and execute agent with MCP tools if available
    if mcp_servers:
        async with MultiServerMCPClient(mcp_servers) as client:
            loaded_tools = default_tools[:]
            for tool in client.get_tools():
                if tool.name in enabled_tools:
                    tool.description = (
                        f"Powered by '{enabled_tools[tool.name]}'.\n{tool.description}"
                    )
                    loaded_tools.append(tool)
            agent = create_agent(agent_type, agent_type, loaded_tools, agent_type)
            return await _execute_agent_step(state, agent, agent_type)
    else:
        # Use default tools if no MCP servers are configured
        agent = create_agent(agent_type, agent_type, default_tools, agent_type)
        return await _execute_agent_step(state, agent, agent_type)


async def researcher_node(
    state: State, config: RunnableConfig
) -> Command[Literal["research_team"]]:
    """Researcher node that do research"""
    logger.info("Researcher node is researching.")
    configurable = Configuration.from_runnable_config(config)
    
    # 基础研究工具
    tools = [get_web_search_tool(configurable.max_search_results), crawl_tool]
    
    # 本地知识库工具（最高优先级）
    retriever_tool = get_retriever_tool(state.get("resources", []))
    if retriever_tool:
        tools.insert(0, retriever_tool)
    
    # # 地理编码工具（保留，用于其他需要坐标的场景）
    # from src.tools.geocode import geocode_tool
    # tools.append(geocode_tool)
    
    # # 统一天气工具（支持地址和坐标两种输入）
    # from src.tools.weather import weather_tool
    # tools.append(weather_tool)
    
    # # 时区转换工具（保留独立功能）
    # from src.tools.timeconvert import time_convert_tool
    # tools.append(time_convert_tool)
    
    # # 货币转换工具（保留核心功能）
    # from src.tools.currency import currency_convert_tool
    # tools.append(currency_convert_tool)
    
    # # SerpAPI工具
    # from src.tools.serpapi_tools import (
    #     google_events_search,
    #     google_flights_search,
    #     google_hotels_search,
    #     google_local_search
    # )
    # tools.extend([google_events_search, google_flights_search, google_hotels_search, google_local_search])
    
    logger.info(f"Researcher tools: {[tool.name if hasattr(tool, 'name') else tool.__name__ for tool in tools]}")
    return await _setup_and_execute_agent_step(
        state,
        config,
        "researcher",
        tools,
    )


async def coder_node(
    state: State, config: RunnableConfig
) -> Command[Literal["research_team"]]:
    """Coder node that do code analysis."""
    logger.info("Coder node is coding.")
    return await _setup_and_execute_agent_step(
        state,
        config,
        "coder",
        [python_repl_tool],
    )
