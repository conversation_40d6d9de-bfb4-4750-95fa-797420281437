# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from enum import Enum
from typing import List, Optional, Literal

from pydantic import BaseModel, Field, field_validator


class StepType(str, Enum):
    RESEARCH = "research"
    PROCESSING = "processing"


class ClarificationOption(BaseModel):
    """单个追问选项"""
    text: str = Field(..., description="选项显示文本")
    value: str = Field(..., description="选项值")
    description: Optional[str] = Field(default=None, description="选项详细描述")


class ClarificationQuestion(BaseModel):
    """追问问题模型"""
    question: str = Field(..., description="问题文本")
    options: List[ClarificationOption] = Field(..., description="选项列表")
    allow_custom_input: bool = Field(default=True, description="是否允许用户自定义输入")
    required: bool = Field(default=True, description="是否必答")


class Step(BaseModel):
    need_search: bool = Field(..., description="Must be explicitly set for each step")
    title: str
    description: str = Field(..., description="Specify exactly what data to collect")
    step_type: Literal["research", "processing"] = Field(..., description="Indicates the nature of the step")
    execution_res: Optional[str] = Field(
        default=None, description="The Step execution result"
    )
    feedback_confirmed: Optional[bool] = Field(
        default=False, description="Whether user has confirmed this step is complete"
    )
    has_errors: Optional[bool] = Field(
        default=False, description="Whether the step execution has errors"
    )
    error_reasons: Optional[List[str]] = Field(
        default=None, description="List of error reasons detected"
    )
    error_summary: Optional[str] = Field(
        default=None, description="Summary of detected errors"
    )

    @field_validator('step_type')
    @classmethod
    def validate_step_type(cls, v):
        if v not in ["research", "processing"]:
            raise ValueError('step_type must be either "research" or "processing"')
        return v


class Plan(BaseModel):
    has_enough_context: bool
    thought: str
    title: str
    steps: List[Step] = Field(
        default_factory=list,
        description="Research & Processing steps to get more context",
    )
    clarification_questions: Optional[List[str]] = Field(
        default=None,
        description="Questions to ask the user for additional context when information is insufficient"
    )
    clarification_questions_with_options: Optional[List[ClarificationQuestion]] = Field(
        default=None,
        description="Interactive questions with options for user to provide additional context"
    )

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "has_enough_context": False,
                    "thought": (
                        "To understand the current market trends in AI, we need to gather comprehensive information."
                    ),
                    "title": "AI Market Research Plan",
                    "steps": [
                        {
                            "need_search": True,
                            "title": "Current AI Market Analysis",
                            "description": (
                                "Collect data on market size, growth rates, major players, and investment trends in AI sector."
                            ),
                            "step_type": "research",
                        }
                    ],
                    "clarification_questions": [
                        "What specific AI technologies or sectors are you most interested in?",
                        "Do you have a particular time frame for this analysis (e.g., last 5 years, current trends)?",
                        "Are you looking for global market data or focusing on specific regions?"
                    ],
                    "clarification_questions_with_options": [
                        {
                            "question": "What specific AI technologies are you most interested in?",
                            "options": [
                                {"text": "Machine Learning", "value": "machine_learning"},
                                {"text": "Natural Language Processing", "value": "nlp"},
                                {"text": "Computer Vision", "value": "computer_vision"},
                                {"text": "Robotics", "value": "robotics"},
                                {"text": "Other", "value": "other"}
                            ],
                            "allow_custom_input": True,
                            "required": True
                        }
                    ]
                }
            ]
        }
