# Author: <PERSON><PERSON>

import re
import logging
from typing import List, Dict, Any, Optional
from src.llms.llm import get_llm_by_type
from langchain_core.messages import HumanMessage, SystemMessage

logger = logging.getLogger(__name__)

# 常量定义
ERROR_PATTERNS = [
    (r"error|Error|ERROR", "Error detected"),
    (r"exception|Exception|EXCEPTION", "Exception detected"), 
    (r"failed|Failed|FAILED", "Failure detected"),
    (r"timeout|Timeout|TIMEOUT", "Timeout detected"),
    (r"connection refused|Connection refused", "Connection refused"),
    (r"not found|Not found|NOT FOUND", "Resource not found"),
    (r"permission denied|Permission denied", "Permission denied"),
    (r"invalid|Invalid|INVALID", "Invalid input"),
    (r"unable to|Unable to|UNABLE TO", "Unable to perform operation"),
    (r"could not|Could not|COULD NOT", "Could not complete operation"),
    (r"failed to|Failed to|FAILED TO", "Operation failed"),
    (r"抱歉", "Chinese error message detected"),
    (r"无法", "Chinese unable message detected"),
    (r"失败", "Chinese failure message detected"),
    (r"错误", "Chinese error message detected"),
    (r"超时", "Chinese timeout message detected")
]

# 新增：美化的错误模式检测
BEAUTIFIED_ERROR_PATTERNS = [
    (r"unfortunately|Unfortunately", "Beautified error message detected"),
    (r"regrettably|Regrettably", "Beautified error message detected"),
    (r"it seems|It seems", "Beautified error message detected"),
    (r"appears to|Appears to", "Beautified error message detected"),
    (r"we were unable|We were unable", "Beautified error message detected"),
    (r"could not retrieve|Could not retrieve", "Beautified error message detected"),
    (r"no information found|No information found", "Beautified error message detected"),
    (r"limited information|Limited information", "Beautified error message detected"),
    (r"unable to find|Unable to find", "Beautified error message detected"),
    (r"no results|No results", "Beautified error message detected"),
    (r"empty response|Empty response", "Beautified error message detected"),
    (r"no data available|No data available", "Beautified error message detected"),
    (r"information not available|Information not available", "Beautified error message detected"),
    (r"暂时无法|暂时不能", "Chinese beautified error message detected"),
    (r"很抱歉|非常抱歉", "Chinese beautified error message detected"),
    (r"未能找到|无法找到", "Chinese beautified error message detected"),
    (r"信息不足|资料不足", "Chinese beautified error message detected"),
    # 新增：针对重试和错误处理的模式 - 更精确的匹配
    (r"resulted in an error|resulted in error", "Error occurred during execution"),
    (r"will reattempt|will retry|will try again", "Retry attempt after error"),
    (r"reattempt the search|retry the search", "Search retry after error"),
    (r"verify.*date.*error|confirm.*date.*error", "Date verification after error"),
    (r"alternative approach.*error|different approach.*error", "Alternative approach after error"),
    (r"hold on while.*error|please hold on.*error", "Processing delay after error"),
    (r"system.*recognizes.*as.*past.*error", "System date recognition error"),
    (r"limitation.*within.*api.*error", "API limitation detected"),
    (r"specific check.*within.*error", "API specific check error"),
    (r"unexpected.*given.*date.*error", "Unexpected date behavior"),
    (r"error persists|error.*persists", "Persistent error detected"),
    (r"might be.*limitation.*error|could be.*limitation.*error", "Potential limitation detected")
]

# 新增：内容质量检测模式 - 更精确的匹配
CONTENT_QUALITY_PATTERNS = [
    (r"no information.*found|no data.*available", "No information found"),
    (r"empty.*response|response.*empty", "Empty response"),
    (r"not available.*information|information.*not available", "Information not available"),
    (r"limited.*information.*only", "Limited information"),
    (r"insufficient.*information.*provided", "Insufficient information"),
    (r"抱歉，没有找到|很抱歉，没有找到", "Chinese no information found"),
    (r"暂无信息|暂无数据", "Chinese no data available"),
    (r"信息不足|资料不足", "Chinese insufficient information")
]

# 新增：重试和多次尝试检测模式 - 更精确的匹配
RETRY_PATTERNS = [
    (r"let me try.*again|let me search.*again", "Multiple attempts detected"),
    (r"let me check.*again|let me look.*again", "Multiple check attempts detected"),
    (r"let me find.*again|let me get.*again", "Multiple find attempts detected"),
    (r"let me retrieve.*again|let me attempt.*again", "Multiple retrieve attempts detected"),
    (r"让我再试|让我再尝试", "Chinese retry attempt detected"),
    (r"让我再搜索|让我再查找", "Chinese search retry detected"),
    (r"让我再检查|让我再确认", "Chinese check retry detected"),
    (r"让我再找|让我再寻找", "Chinese find retry detected"),
    (r"让我再获取|让我再获取", "Chinese get retry detected"),
    (r"让我再检索|让我再检索", "Chinese retrieve retry detected"),
    (r"让我再尝试|让我再尝试", "Chinese attempt retry detected")
]

# 新增：工具调用错误模式 - 更精确的匹配
TOOL_CALL_ERROR_PATTERNS = [
    (r"tool.*failed.*to|failed.*tool.*call", "Tool call failed"),
    (r"api.*failed.*to|failed.*api.*call", "API call failed"),
    (r"search.*failed.*to|failed.*search.*tool", "Search tool failed"),
    (r"crawl.*failed.*to|failed.*crawl.*tool", "Crawler tool failed"),
    (r"python.*failed.*to|failed.*python.*execution", "Python code execution failed"),
    (r"repl.*failed.*to|failed.*repl.*execution", "Code interpreter failed"),
    (r"tool.*error.*occurred|error.*tool.*call", "Tool call error"),
    (r"api.*error.*occurred|error.*api.*call", "API call error"),
    (r"search.*error.*occurred|error.*search.*tool", "Search tool error"),
    (r"crawl.*error.*occurred|error.*crawl.*tool", "Crawler tool error"),
    (r"python.*error.*occurred|error.*python.*execution", "Python code execution error"),
    (r"repl.*error.*occurred|error.*repl.*execution", "Code interpreter error"),
    (r"tool.*exception.*occurred|exception.*tool.*call", "Tool call exception"),
    (r"api.*exception.*occurred|exception.*api.*call", "API call exception"),
    (r"search.*exception.*occurred|exception.*search.*tool", "Search tool exception"),
    (r"crawl.*exception.*occurred|exception.*crawl.*tool", "Crawler tool exception"),
    (r"python.*exception.*occurred|exception.*python.*execution", "Python code execution exception"),
    (r"repl.*exception.*occurred|exception.*repl.*execution", "Code interpreter exception"),
    # 新增：针对SerpAPI和具体API错误的模式 - 更精确的匹配
    (r"serpapi.*error.*occurred|serpapi error.*detected", "SerpAPI error detected"),
    (r"outbound_date.*cannot.*past.*error", "Date validation error"),
    (r"cannot.*in.*past.*error", "Past date error"),
    (r"date.*validation.*error.*occurred", "Date validation error"),
    (r"parameter.*error.*occurred|parameter error.*detected", "Parameter error"),
    (r"invalid.*parameter.*error|invalid parameter.*detected", "Invalid parameter error"),
    (r"required.*parameter.*missing.*error", "Missing required parameter"),
    (r"api.*response.*error.*occurred", "API response error"),
    (r"api.*returned.*error.*message", "API returned error"),
    (r"search.*parameters.*error.*occurred", "Search parameters error"),
    (r"flight.*search.*error.*occurred", "Flight search error"),
    (r"hotel.*search.*error.*occurred", "Hotel search error"),
    (r"event.*search.*error.*occurred", "Event search error"),
    (r"local.*search.*error.*occurred", "Local search error"),
    (r"currency.*conversion.*error.*occurred", "Currency conversion error"),
    (r"weather.*forecast.*error.*occurred", "Weather forecast error"),
    (r"geocode.*error.*occurred", "Geocoding error"),
    (r"time.*conversion.*error.*occurred", "Time conversion error")
]

def check_step_errors(execution_result: str) -> Dict[str, Any]:
    """
    改进的错误检测函数
    返回包含错误信息和具体原因的字典
    """
    if not execution_result:
        return {
            "has_errors": True,
            "error_reasons": ["Empty execution result"],
            "error_details": "System returned no execution result, which may indicate execution failure or tool call exception."
        }
    
    # 转换为小写进行检查
    result_lower = execution_result.lower()
    error_reasons = []
    error_details = []
    

    
    # 检测原始错误关键词 - 添加上下文检查
    for pattern, reason in ERROR_PATTERNS:
        matches = re.finditer(pattern, execution_result, re.IGNORECASE)
        for match in matches:
            # 检查上下文，避免误报
            start = max(0, match.start() - 100)
            end = min(len(execution_result), match.end() + 100)
            context = execution_result[start:end]
            
            # 如果上下文包含明显的成功信息，跳过这个匹配
            if _is_success_context(context):
                continue
                
            # 如果上下文包含明显的正常信息，跳过这个匹配
            if _is_normal_context(context):
                continue
            
            error_reasons.append(reason)
            error_details.append(f"Found '{match.group()}' related error in text: ...{context}...")
    
    # 新增：检测美化的错误信息 - 添加上下文检查
    for pattern, reason in BEAUTIFIED_ERROR_PATTERNS:
        matches = re.finditer(pattern, execution_result, re.IGNORECASE)
        for match in matches:
            # 检查上下文，避免误报
            start = max(0, match.start() - 100)
            end = min(len(execution_result), match.end() + 100)
            context = execution_result[start:end]
            
            # 如果上下文包含明显的成功信息，跳过这个匹配
            if _is_success_context(context):
                continue
                
            # 如果上下文包含明显的正常信息，跳过这个匹配
            if _is_normal_context(context):
                continue
            
            if reason not in error_reasons:
                error_reasons.append(reason)
            error_details.append(f"Found beautified error '{match.group()}' in text: ...{context}...")
    
    # 新增：检测内容质量问题 - 添加上下文检查
    for pattern, reason in CONTENT_QUALITY_PATTERNS:
        matches = re.finditer(pattern, execution_result, re.IGNORECASE)
        for match in matches:
            # 检查上下文，避免误报
            start = max(0, match.start() - 100)
            end = min(len(execution_result), match.end() + 100)
            context = execution_result[start:end]
            
            # 如果上下文包含明显的成功信息，跳过这个匹配
            if _is_success_context(context):
                continue
                
            # 如果上下文包含明显的正常信息，跳过这个匹配
            if _is_normal_context(context):
                continue
            
            if reason not in error_reasons:
                error_reasons.append(reason)
    
    # 新增：检测重试和多次尝试 - 添加上下文检查
    retry_count = 0
    for pattern, reason in RETRY_PATTERNS:
        matches = re.finditer(pattern, execution_result, re.IGNORECASE)
        for match in matches:
            # 检查上下文，避免误报
            start = max(0, match.start() - 100)
            end = min(len(execution_result), match.end() + 100)
            context = execution_result[start:end]
            
            # 如果上下文包含明显的成功信息，跳过这个匹配
            if _is_success_context(context):
                continue
                
            # 如果上下文包含明显的正常信息，跳过这个匹配
            if _is_normal_context(context):
                continue
            
            retry_count += 1
            if reason not in error_reasons:
                error_reasons.append(reason)
    
    # 如果检测到多次重试，添加重试次数信息
    if retry_count > 0:
        error_reasons.append(f"Multiple retry attempts detected ({retry_count} attempts)")
    
    # 检测结果过短
    if len(execution_result.strip()) < 30:
        error_reasons.append("Execution result too short")
        error_details.append(f"Execution result has only {len(execution_result.strip())} characters, may be incomplete.")
    
    # 新增：检测markdown结构中的问题
    markdown_issues = _detect_markdown_issues(execution_result)
    if markdown_issues:
        error_reasons.extend(markdown_issues)
    
    # 检测工具调用相关的错误模式 - 添加上下文检查
    for pattern, reason in TOOL_CALL_ERROR_PATTERNS:
        matches = re.finditer(pattern, execution_result, re.IGNORECASE)
        for match in matches:
            # 检查上下文，避免误报
            start = max(0, match.start() - 100)
            end = min(len(execution_result), match.end() + 100)
            context = execution_result[start:end]
            
            # 如果上下文包含明显的成功信息，跳过这个匹配
            if _is_success_context(context):
                continue
                
            # 如果上下文包含明显的正常信息，跳过这个匹配
            if _is_normal_context(context):
                continue
            
            if reason not in error_reasons:
                error_reasons.append(reason)
    
    # 检测网络相关错误
    network_error_patterns = [
        (r"connection.*error|error.*connection", "Network connection error"),
        (r"timeout.*error|error.*timeout", "Network timeout error"),
        (r"dns.*error|error.*dns", "DNS resolution error"),
        (r"ssl.*error|error.*ssl", "SSL certificate error")
    ]
    
    for pattern, reason in network_error_patterns:
        if re.search(pattern, result_lower, re.IGNORECASE):
            if reason not in error_reasons:
                error_reasons.append(reason)
    
    # 新增：检测执行流程中的问题
    execution_flow_issues = _detect_execution_flow_issues(execution_result)
    if execution_flow_issues:
        error_reasons.extend(execution_flow_issues)
    
    has_errors = len(error_reasons) > 0
    
    return {
        "has_errors": has_errors,
        "error_reasons": error_reasons,
        "error_details": error_details,
        "error_summary": _generate_error_summary(error_reasons, error_details, execution_result),
        "retry_count": retry_count
    }

def _is_success_context(context: str) -> bool:
    """检查上下文是否包含明显的成功信息"""
    success_indicators = [
        "successfully", "completed", "found", "retrieved", "obtained",
        "available", "confirmed", "verified", "success", "working",
        "flight options", "prices", "airlines", "departure", "arrival",
        "duration", "features", "carbon emissions", "airport options",
        "visa requirements", "covid-19", "travel restrictions"
    ]
    
    context_lower = context.lower()
    return any(indicator in context_lower for indicator in success_indicators)

def _is_normal_context(context: str) -> bool:
    """检查上下文是否包含明显的正常信息"""
    normal_indicators = [
        "confirmed closer to travel date", "policies may change",
        "check the latest guidelines", "should verify specific",
        "depending on current policies", "as of now",
        "summary:", "references:", "flight options",
        "airport options", "visa requirements", "covid-19 travel restrictions"
    ]
    
    context_lower = context.lower()
    return any(indicator in context_lower for indicator in normal_indicators)



def _detect_markdown_issues(content: str) -> List[str]:
    """检测markdown结构中的问题"""
    issues = []
    
    # 检测是否包含标准的markdown结构
    has_problem_statement = bool(re.search(r"#\s*Problem Statement|##\s*Problem Statement", content, re.IGNORECASE))
    has_research_findings = bool(re.search(r"#\s*Research Findings|##\s*Research Findings", content, re.IGNORECASE))
    has_conclusion = bool(re.search(r"#\s*Conclusion|##\s*Conclusion", content, re.IGNORECASE))
    has_references = bool(re.search(r"#\s*References|##\s*References", content, re.IGNORECASE))
    
    # 如果包含markdown结构，检查内容质量
    if has_problem_statement and has_research_findings and has_conclusion:
        # 检查Research Findings部分是否为空或内容不足
        findings_match = re.search(r"#\s*Research Findings|##\s*Research Findings(.*?)(?=#\s*Conclusion|##\s*Conclusion|$)", content, re.IGNORECASE | re.DOTALL)
        if findings_match and findings_match.group(1) is not None:
            findings_content = findings_match.group(1).strip()
            if len(findings_content) < 50:  # 如果Research Findings内容过少
                issues.append("Research findings section is too short or empty")
            
            # 检查是否包含"no information"等关键词
            if re.search(r"no information|no data|empty|not available", findings_content, re.IGNORECASE):
                issues.append("Research findings indicate no useful information was found")
    
    return issues

def _detect_execution_flow_issues(content: str) -> List[str]:
    """检测执行流程中的问题"""
    issues = []
    
    # 检测是否包含工具调用的错误信息
    tool_call_patterns = [
        r"调用.*工具|调用.*API|调用.*搜索",
        r"tool.*call|api.*call|search.*call",
        r"工具.*调用|API.*调用|搜索.*调用"
    ]
    
    # 检测是否包含错误后的成功信息
    error_success_patterns = [
        r"error.*success|success.*error",
        r"failed.*success|success.*failed",
        r"错误.*成功|成功.*错误",
        r"失败.*成功|成功.*失败"
    ]
    
    # 检测是否包含多次尝试的信息
    multiple_attempt_patterns = [
        r"first.*try|second.*try|third.*try",
        r"第一次.*尝试|第二次.*尝试|第三次.*尝试",
        r"attempt.*1|attempt.*2|attempt.*3",
        r"尝试.*1|尝试.*2|尝试.*3"
    ]
    
    # 新增：检测API错误和重试模式
    api_error_retry_patterns = [
        r"serpapi.*error.*reattempt",
        r"api.*error.*will.*retry",
        r"error.*will.*reattempt",
        r"failed.*will.*try.*again",
        r"error.*will.*verify",
        r"error.*will.*confirm",
        r"error.*alternative.*approach",
        r"error.*different.*approach",
        r"error.*hold.*on",
        r"error.*please.*hold",
        r"error.*system.*recognizes",
        r"error.*limitation.*within",
        r"error.*specific.*check",
        r"error.*unexpected.*given",
        r"error.*persists",
        r"error.*might.*be.*limitation",
        r"error.*could.*be.*limitation"
    ]
    
    # 检测工具调用错误
    for pattern in tool_call_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            issues.append("Tool call errors detected in execution flow")
            break
    
    # 检查错误后的成功
    for pattern in error_success_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            issues.append("Error followed by success indicates initial failure")
            break
    
    # 检查多次尝试
    for pattern in multiple_attempt_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            issues.append("Multiple attempts detected in execution flow")
            break
    
    # 新增：检查API错误和重试模式
    for pattern in api_error_retry_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            issues.append("API error followed by retry attempt")
            break
    
    # 新增：检查具体的API错误信息
    specific_api_errors = [
        r"outbound_date.*cannot.*past",
        r"cannot.*in.*past",
        r"date.*validation.*error",
        r"parameter.*error",
        r"invalid.*parameter",
        r"required.*parameter.*missing",
        r"api.*response.*error",
        r"api.*returned.*error",
        r"search.*parameters.*error",
        r"flight.*search.*error",
        r"hotel.*search.*error",
        r"event.*search.*error",
        r"local.*search.*error",
        r"currency.*conversion.*error",
        r"weather.*forecast.*error",
        r"geocode.*error",
        r"time.*conversion.*error"
    ]
    
    for pattern in specific_api_errors:
        if re.search(pattern, content, re.IGNORECASE):
            issues.append("Specific API error detected in execution flow")
            break
    
    return issues

def _generate_dynamic_suggestions(error_reasons: List[str], error_details: List[str], execution_result: str) -> str:
    """使用LLM动态生成针对性的建议"""
    try:
        # 获取LLM实例
        llm = get_llm_by_type("basic")
        
        # 构建系统提示
        system_prompt = """You are an expert system analyst. Generate concise, actionable recommendations to resolve the identified issues.

Requirements:
- Keep each recommendation under 50 characters
- Provide 2-3 specific, practical steps
- Focus on immediate actionable solutions
- Use bullet points format
- Total response should be under 300 characters"""
        
        # 构建用户提示
        user_prompt = f"""Error: {', '.join(error_reasons[:2])}
Context: {execution_result[:200]}...

Generate 2-3 brief, actionable recommendations."""
        
        # 调用LLM
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        response = llm.invoke(messages)
        
        # 格式化建议并确保长度限制
        suggestions = response.content.strip()
        if suggestions and len(suggestions) <= 300:
            return f"**Recommended Actions:**\n{suggestions}"
        else:
            # 如果LLM生成的建议过长，使用备用建议
            return _get_fallback_suggestions()
            
    except Exception as e:
        logger.warning(f"Failed to generate dynamic suggestions: {e}")
        return _get_fallback_suggestions()

def _get_fallback_suggestions() -> str:
    """提供备用建议（当LLM调用失败时使用）"""
    return """**Recommended Actions:**
• Check input parameters
• Verify network connection
• Try again later"""

def _generate_error_summary(error_reasons: List[str], error_details: List[str], execution_result: str = "") -> str:
    """生成用户友好的错误摘要（英文版本）"""
    if not error_reasons:
        return ""
    
    # 错误类型映射，将技术性错误描述转换为用户友好的英文描述
    error_type_mapping = {
        "Error detected": "Error occurred during execution",
        "Exception detected": "Exception occurred during execution",
        "Failure detected": "Operation failed",
        "Timeout detected": "Operation timed out",
        "Connection refused": "Connection refused",
        "Resource not found": "Resource not found",
        "Permission denied": "Permission denied",
        "Invalid input": "Invalid input parameters",
        "Unable to perform operation": "Unable to perform operation",
        "Could not complete operation": "Could not complete operation",
        "Operation failed": "Operation failed",
        "Chinese error message detected": "Chinese error message detected",
        "Chinese unable message detected": "Chinese unable message detected",
        "Chinese failure message detected": "Chinese failure message detected",
        "Chinese timeout message detected": "Chinese timeout message detected",
        "Beautified error message detected": "Beautified error message detected",
        "Chinese beautified error message detected": "Chinese beautified error message detected",
        "No information found": "No information found",
        "No data available": "No data available",
        "Empty response": "Empty response",
        "Information not available": "Information not available",
        "Limited information": "Limited information",
        "Insufficient information": "Insufficient information",
        "Chinese no information found": "Chinese no information found",
        "Chinese no data available": "Chinese no data available",
        "Chinese insufficient information": "Chinese insufficient information",
        "Multiple attempts detected": "Multiple attempts detected",
        "Multiple search attempts detected": "Multiple search attempts detected",
        "Multiple check attempts detected": "Multiple check attempts detected",
        "Multiple lookup attempts detected": "Multiple lookup attempts detected",
        "Multiple find attempts detected": "Multiple find attempts detected",
        "Multiple get attempts detected": "Multiple get attempts detected",
        "Multiple retrieve attempts detected": "Multiple retrieve attempts detected",
        "Multiple attempt detected": "Multiple attempt detected",
        "Retry attempt detected": "Retry attempt detected",
        "Search retry detected": "Search retry detected",
        "Check retry detected": "Check retry detected",
        "Lookup retry detected": "Lookup retry detected",
        "Find retry detected": "Find retry detected",
        "Get retry detected": "Get retry detected",
        "Retrieve retry detected": "Retrieve retry detected",
        "Attempt retry detected": "Attempt retry detected",
        "Chinese retry attempt detected": "Chinese retry attempt detected",
        "Chinese search retry detected": "Chinese search retry detected",
        "Chinese check retry detected": "Chinese check retry detected",
        "Chinese find retry detected": "Chinese find retry detected",
        "Chinese get retry detected": "Chinese get retry detected",
        "Chinese retrieve retry detected": "Chinese retrieve retry detected",
        "Chinese attempt retry detected": "Chinese attempt retry detected",
        "Execution result too short": "Execution result too short",
        "Research findings section is too short or empty": "Research findings section is too short or empty",
        "Research findings indicate no useful information was found": "Research findings indicate no useful information was found",
        "Tool call error": "Tool call error",
        "API call error": "API call error",
        "Search tool error": "Search tool error",
        "Crawler tool error": "Crawler tool error",
        "Python code execution error": "Python code execution error",
        "Code interpreter error": "Code interpreter error",
        "Tool call failed": "Tool call failed",
        "API call failed": "API call failed",
        "Search tool failed": "Search tool failed",
        "Crawler tool failed": "Crawler tool failed",
        "Python code execution failed": "Python code execution failed",
        "Code interpreter failed": "Code interpreter failed",
        "Tool call exception": "Tool call exception",
        "API call exception": "API call exception",
        "Search tool exception": "Search tool exception",
        "Crawler tool exception": "Crawler tool exception",
        "Python code execution exception": "Python code execution exception",
        "Code interpreter exception": "Code interpreter exception",
        "SerpAPI error detected": "SerpAPI error detected",
        "Date validation error": "Date validation error",
        "Past date error": "Past date error",
        "Parameter error": "Parameter error",
        "Invalid parameter error": "Invalid parameter error",
        "Missing required parameter": "Missing required parameter",
        "API response error": "API response error",
        "API returned error": "API returned error",
        "Search parameters error": "Search parameters error",
        "Flight search error": "Flight search error",
        "Hotel search error": "Hotel search error",
        "Event search error": "Event search error",
        "Local search error": "Local search error",
        "Currency conversion error": "Currency conversion error",
        "Weather forecast error": "Weather forecast error",
        "Geocoding error": "Geocoding error",
        "Time conversion error": "Time conversion error",
        "Error occurred during execution": "Error occurred during execution",
        "Retry attempt after error": "Retry attempt after error",
        "Search retry after error": "Search retry after error",
        "Date verification after error": "Date verification after error",
        "Alternative approach after error": "Alternative approach after error",
        "Processing delay after error": "Processing delay after error",
        "System date recognition error": "System date recognition error",
        "API limitation detected": "API limitation detected",
        "API specific check error": "API specific check error",
        "Unexpected date behavior": "Unexpected date behavior",
        "Persistent error detected": "Persistent error detected",
        "Potential limitation detected": "Potential limitation detected",
        "Tool call errors detected in execution flow": "Tool call errors detected in execution flow",
        "Error followed by success indicates initial failure": "Error followed by success indicates initial failure",
        "Multiple attempts detected in execution flow": "Multiple attempts detected in execution flow",
        "API error followed by retry attempt": "API error followed by retry attempt",
        "Specific API error detected in execution flow": "Specific API error detected in execution flow"
    }
    
    # 生成用户友好的错误摘要
    summary = "## Execution Issue Summary\n\n"
    
    # 添加重试信息（如果有）
    retry_info = []
    for reason in error_reasons:
        if "retry" in reason.lower() or "reattempt" in reason.lower():
            if "Multiple retry attempts detected" in reason:
                retry_match = re.search(r"\((\d+) attempts\)", reason)
                if retry_match:
                    retry_count = retry_match.group(1)
                    retry_info.append(f"System performed {retry_count} retry attempts")
            else:
                retry_info.append("System performed retry attempts")
    
    if retry_info:
        summary += f"**Retry Information:**\n"
        for info in retry_info:
            summary += f"• {info}\n"
        summary += "\n"
    
    # 使用LLM动态生成建议
    summary += f"{_generate_dynamic_suggestions(error_reasons, error_details, execution_result)}"
    
    return summary

