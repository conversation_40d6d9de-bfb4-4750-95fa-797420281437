# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import os
import requests
from src.rag.retriever import Chunk, Document, Resource, Retriever
from urllib.parse import urlparse


class DifyKnowledgeProvider(Retriever):
    """
    DifyKnowledgeProvider is a provider that uses Dify Knowledge to retrieve documents.
    """

    api_url: str
    api_key: str
    page_size: int = 10

    def __init__(self):
        api_url = os.getenv("DIFY_KNOWLEDGE_API_URL")
        if not api_url:
            raise ValueError("DIFY_KNOWLEDGE_API_URL is not set")
        self.api_url = api_url

        api_key = os.getenv("DIFY_KNOWLEDGE_API_KEY")
        if not api_key:
            raise ValueError("DIFY_KNOWLEDGE_API_KEY is not set")
        self.api_key = api_key

        page_size = os.getenv("DIFY_KNOWLEDGE_PAGE_SIZE")
        if page_size:
            self.page_size = int(page_size)


    def _query_dataset(self, dataset_id: str, query: str) -> list[Document]:
        url = f"{self.api_url}/v1/datasets/{dataset_id}/retrieve"
        print(f"===========retrieval url {url}===========")
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        payload = {
            "query": query,
        }
        print(f"===========payload===========")
        print(payload)

        response = requests.post(
                url, headers=headers, json=payload
            )

        if response.status_code != 200:
            print(f"===========response: {response.text}===========")
            raise Exception(f"Failed to query documents: {response.text}")

        result = response.json()
        print(f"===========result===========")
        print(result)
        records = result.get("records", [])
        print(f"===========records: length {len(records)}===========")
        docs: dict[str, Document] = {}
        for record in records:
            print(f"===========record: {record}===========")
            doc_id = record.get("segment").get("document").get("id")
            if doc_id not in docs:
                docs[doc_id] = Document(
                    id=doc_id,
                    title=record.get("segment").get("document").get("name"),
                    chunks=[],
                )
            docs[doc_id].chunks.append(
                Chunk(
                    content=record.get("segment").get("content"),
                    similarity=record.get("score", 0),
                )
            )
        print(f"===========docs: length {len(docs)}===========")

        return docs



    def query_relevant_documents(
        self, query: str, resources: list[Resource] = []
    ) -> list[Document]:

        print(f"===========query_relevant_documents===========")

        docs: dict[str, Document] = {}
        for resource in resources:
            print(f"===========resource {resource.uri}===========")
            dataset_id, document_id = parse_uri(resource.uri)
            print(f"===========dataset_id {dataset_id}===========")

            docs.update(self._query_dataset(dataset_id, query))

        print(f"===========docs: length {len(docs)}===========")
        print(f"===========docs: {docs}===========")
        return list(docs.values())

    def list_resources(self, query: str | None = None) -> list[Resource]:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        params = {}
        tag_ids = ["5142bd43-1001-4f25-a95e-04088b558f74"]
        if query:
            params["keywords"] = query
        if tag_ids:
            params["tag_ids"] = tag_ids
        params["page"] = 1
        params["limit"] = 3

        response = requests.get(
            f"{self.api_url}/v1/datasets", headers=headers, params=params
        )

        if response.status_code != 200:
            print(f"===========response: {response.text}===========")
            raise Exception(f"Failed to list resources: {response.text}")

        result = response.json()
        resources = []

        for item in result.get("data", []):
            print(f"==========={item}===========")
            item = Resource(
                uri=f"rag://dataset/{item.get('id')}",
                title=item.get("name", ""),
                description=item.get("description", ""),
            )
            resources.append(item)

        return resources


def parse_uri(uri: str) -> tuple[str, str]:
    parsed = urlparse(uri)
    if parsed.scheme != "rag":
        raise ValueError(f"Invalid URI: {uri}")
    return parsed.path.split("/")[1], parsed.fragment
