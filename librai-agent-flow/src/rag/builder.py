# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from src.config.tools import SELECTED_RAG_PROVIDER, RAGProvider
from src.rag.dify_knowledge import DifyKnowledgeProvider
from src.rag.ragflow import RAGFlowProvider
from src.rag.retriever import Retriever


def build_retriever() -> Retriever | None:
    if SELECTED_RAG_PROVIDER == RAGProvider.RAGFLOW.value:
        print("RAGFlowProvider")
        return RAGFlowProvider()
    elif SELECTED_RAG_PROVIDER == RAGProvider.DIFY_KNOWLEDGE.value:
        print("DifyKnowledgeProvider")
        return DifyKnowledgeProvider()
    elif SELECTED_RAG_PROVIDER:
        raise ValueError(f"Unsupported RAG provider: {SELECTED_RAG_PROVIDER}")
    return None
