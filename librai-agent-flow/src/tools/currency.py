import logging
import os
import requests
from typing import Annotated, Dict, Any, Optional, List
from datetime import datetime

from langchain_core.tools import tool
from .decorators import log_io

logger = logging.getLogger(__name__)


@tool
@log_io
def currency_rates_tool(
    base_currency: Annotated[str,
                             "Base currency code (e.g., 'USD', 'EUR', 'GBP')."] = "USD"
) -> str:
    """Use this to get current exchange rates for a base currency against all supported currencies."""
    try:
        api_key = os.getenv("EXCHANGE_RATE_API_KEY")
        if not api_key:
            # Use hardcoded key as fallback
            api_key = "a9689d62114f38e427b2e667"
            logger.info("Using hardcoded ExchangeRate API key")

        # Validate base currency format
        base_currency = base_currency.upper().strip()
        if len(base_currency) != 3:
            error_msg = f"Invalid currency code: {base_currency}. Must be 3-letter ISO code (e.g., USD, EUR)."
            logger.error(error_msg)
            return error_msg

        # ExchangeRate API endpoint
        url = f"https://v6.exchangerate-api.com/v6/{api_key}/latest/{base_currency}"

        logger.info(
            f"Getting exchange rates for base currency: {base_currency}")
        response = requests.get(url)
        response.raise_for_status()

        data = response.json()

        if data.get("result") != "success":
            error_msg = f"Currency API failed with result: {data.get('result')}. {data.get('error-type', 'Unknown error')}"
            logger.error(error_msg)
            return error_msg

        # Extract rate information
        base_code = data.get("base_code", base_currency)
        conversion_rates = data.get("conversion_rates", {})
        last_update = data.get("time_last_update_utc", "Unknown")
        next_update = data.get("time_next_update_utc", "Unknown")

        # Format major currencies for display
        major_currencies = ["USD", "EUR", "GBP", "JPY",
                            "CAD", "AUD", "CHF", "CNY", "INR", "BRL"]
        major_rates = {}
        for currency in major_currencies:
            if currency in conversion_rates and currency != base_code:
                major_rates[currency] = conversion_rates[currency]

        # Create summary
        rates_summary = []
        rates_summary.append(f"Exchange Rates for {base_code}")
        rates_summary.append(f"Last Updated: {last_update}")
        rates_summary.append(f"Next Update: {next_update}")
        rates_summary.append("=" * 50)

        rates_summary.append("\nMAJOR CURRENCY RATES:")
        for currency, rate in major_rates.items():
            rates_summary.append(f"  1 {base_code} = {rate:.4f} {currency}")

        rates_summary.append(
            f"\nTotal Currencies Available: {len(conversion_rates)}")

        # Show some additional currencies
        other_currencies = []
        count = 0
        for currency, rate in conversion_rates.items():
            if currency not in major_currencies and currency != base_code and count < 10:
                other_currencies.append(
                    f"  1 {base_code} = {rate:.4f} {currency}")
                count += 1

        if other_currencies:
            rates_summary.append("\nOTHER CURRENCIES (sample):")
            rates_summary.extend(other_currencies)
            if len(conversion_rates) > len(major_currencies) + 10:
                rates_summary.append(
                    f"  ... and {len(conversion_rates) - len(major_currencies) - 10} more")

        # Include full data for reference
        rates_result = {
            "base_currency": base_code,
            "last_update": last_update,
            "next_update": next_update,
            "total_currencies": len(conversion_rates),
            "major_rates": major_rates,
            "all_rates": conversion_rates,
            "full_response": data
        }

        rates_summary.append(f"\nFull rates data: {rates_result}")

        return "\n".join(rates_summary)

    except requests.exceptions.RequestException as e:
        error_msg = f"Failed to get exchange rates for {base_currency}. Network error: {repr(e)}"
        logger.error(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"Failed to get exchange rates for {base_currency}. Error: {repr(e)}"
        logger.error(error_msg)
        return error_msg


@tool
@log_io
def currency_convert_tool(
    amount: Annotated[float, "Amount to convert."],
    from_currency: Annotated[str, "Source currency code (e.g., 'USD', 'EUR')."],
    to_currency: Annotated[str, "Target currency code (e.g., 'JPY', 'GBP')."]
) -> str:
    """Use this to convert an amount from one currency to another using current exchange rates."""
    try:
        api_key = os.getenv("EXCHANGE_RATE_API_KEY")
        if not api_key:
            # Use hardcoded key as fallback
            api_key = "a9689d62114f38e427b2e667"
            logger.info("Using hardcoded ExchangeRate API key")

        # Validate inputs
        if amount <= 0:
            error_msg = f"Invalid amount: {amount}. Must be greater than 0."
            logger.error(error_msg)
            return error_msg

        from_currency = from_currency.upper().strip()
        to_currency = to_currency.upper().strip()

        if len(from_currency) != 3 or len(to_currency) != 3:
            error_msg = f"Invalid currency codes. Use 3-letter ISO codes (e.g., USD, EUR)."
            logger.error(error_msg)
            return error_msg

        # ExchangeRate API endpoint for conversion
        url = f"https://v6.exchangerate-api.com/v6/{api_key}/pair/{from_currency}/{to_currency}/{amount}"

        logger.info(f"Converting {amount} {from_currency} to {to_currency}")
        response = requests.get(url)
        response.raise_for_status()

        data = response.json()

        if data.get("result") != "success":
            error_msg = f"Currency conversion failed with result: {data.get('result')}. {data.get('error-type', 'Unknown error')}"
            logger.error(error_msg)
            return error_msg

        # Extract conversion information
        base_code = data.get("base_code", from_currency)
        target_code = data.get("target_code", to_currency)
        conversion_rate = data.get("conversion_rate", 0)
        conversion_result = data.get("conversion_result", 0)
        last_update = data.get("time_last_update_utc", "Unknown")

        # Format results
        conversion_summary = []
        conversion_summary.append(
            f"Currency Conversion: {base_code} → {target_code}")
        conversion_summary.append(f"Amount: {amount:,.2f} {base_code}")
        conversion_summary.append(
            f"Exchange Rate: 1 {base_code} = {conversion_rate:.6f} {target_code}")
        conversion_summary.append(
            f"Converted Amount: {conversion_result:,.2f} {target_code}")
        conversion_summary.append(f"Last Updated: {last_update}")

        # Add some helpful context
        if conversion_rate > 1:
            conversion_summary.append(
                f"💡 {base_code} is stronger than {target_code}")
        else:
            conversion_summary.append(
                f"💡 {target_code} is stronger than {base_code}")

        # Show reverse conversion for reference
        reverse_rate = 1 / conversion_rate if conversion_rate > 0 else 0
        reverse_amount = amount / conversion_rate if conversion_rate > 0 else 0
        conversion_summary.append(
            f"Reverse: 1 {target_code} = {reverse_rate:.6f} {base_code}")
        conversion_summary.append(
            f"Reverse Amount: {reverse_amount:,.2f} {base_code} = {amount:,.2f} {target_code}")

        # Include full data for reference
        conversion_data = {
            "amount": amount,
            "from_currency": base_code,
            "to_currency": target_code,
            "exchange_rate": conversion_rate,
            "converted_amount": conversion_result,
            "reverse_rate": reverse_rate,
            "last_update": last_update,
            "full_response": data
        }

        conversion_summary.append(f"\nFull conversion data: {conversion_data}")

        return "\n".join(conversion_summary)

    except requests.exceptions.RequestException as e:
        error_msg = f"Failed to convert {amount} {from_currency} to {to_currency}. Network error: {repr(e)}"
        logger.error(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"Failed to convert {amount} {from_currency} to {to_currency}. Error: {repr(e)}"
        logger.error(error_msg)
        return error_msg


@tool
@log_io
def supported_currencies_tool() -> str:
    """Use this to get a list of all supported currency codes and their names."""
    try:
        api_key = os.getenv("EXCHANGE_RATE_API_KEY")
        if not api_key:
            # Use hardcoded key as fallback
            api_key = "a9689d62114f38e427b2e667"
            logger.info("Using hardcoded ExchangeRate API key")

        # ExchangeRate API endpoint for supported codes
        url = f"https://v6.exchangerate-api.com/v6/{api_key}/codes"

        logger.info("Getting list of supported currencies")
        response = requests.get(url)
        response.raise_for_status()

        data = response.json()

        if data.get("result") != "success":
            error_msg = f"Failed to get supported currencies with result: {data.get('result')}. {data.get('error-type', 'Unknown error')}"
            logger.error(error_msg)
            return error_msg

        # Extract supported codes
        supported_codes = data.get("supported_codes", [])

        # Organize currencies by region/type
        major_currencies = []
        crypto_currencies = []
        other_currencies = []

        major_codes = ["USD", "EUR", "GBP", "JPY", "CAD", "AUD", "CHF",
                       "CNY", "INR", "BRL", "RUB", "KRW", "MXN", "SGD", "HKD"]
        crypto_codes = ["BTC", "ETH", "LTC", "XRP", "ADA", "DOT", "BNB"]

        for code, name in supported_codes:
            if code in major_codes:
                major_currencies.append(f"  {code} - {name}")
            elif code in crypto_codes:
                crypto_currencies.append(f"  {code} - {name}")
            else:
                other_currencies.append(f"  {code} - {name}")

        # Create summary
        currency_summary = []
        currency_summary.append(f"Supported Currency Codes")
        currency_summary.append(f"Total Currencies: {len(supported_codes)}")
        currency_summary.append("=" * 50)

        currency_summary.append("\nMAJOR CURRENCIES:")
        currency_summary.extend(major_currencies)

        if crypto_currencies:
            currency_summary.append("\nCRYPTOCURRENCIES:")
            currency_summary.extend(crypto_currencies)

        currency_summary.append(
            f"\nOTHER CURRENCIES ({len(other_currencies)} total):")
        # Show first 20 other currencies
        currency_summary.extend(other_currencies[:20])
        if len(other_currencies) > 20:
            currency_summary.append(
                f"  ... and {len(other_currencies) - 20} more")

        # Include usage examples
        currency_summary.append("\nUSAGE EXAMPLES:")
        currency_summary.append("  currency_convert_tool(100, 'USD', 'EUR')")
        currency_summary.append("  currency_rates_tool('GBP')")
        currency_summary.append("  currency_convert_tool(1000, 'JPY', 'USD')")

        # Include full data for reference
        currencies_data = {
            "total_currencies": len(supported_codes),
            "major_currencies": len(major_currencies),
            "crypto_currencies": len(crypto_currencies),
            "other_currencies": len(other_currencies),
            "all_codes": supported_codes,
            "full_response": data
        }

        currency_summary.append(f"\nFull currencies data: {currencies_data}")

        return "\n".join(currency_summary)

    except requests.exceptions.RequestException as e:
        error_msg = f"Failed to get supported currencies. Network error: {repr(e)}"
        logger.error(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"Failed to get supported currencies. Error: {repr(e)}"
        logger.error(error_msg)
        return error_msg
