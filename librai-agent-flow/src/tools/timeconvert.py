import logging
import os
import requests
import json
from typing import Annotated, Dict, Any, Optional
from datetime import datetime, timezone, timedelta
import time

from langchain_core.tools import tool
from .decorators import log_io

logger = logging.getLogger(__name__)


@tool
@log_io
def timezone_info_tool(
    latitude: Annotated[float, "The latitude coordinate for timezone lookup."],
    longitude: Annotated[float, "The longitude coordinate for timezone lookup."],
    timestamp: Annotated[Optional[int],
                         "Unix timestamp for the time to check (default: current time)."] = None
) -> str:
    """Use this to get timezone information for specific coordinates (latitude and longitude)."""
    try:
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            error_msg = "Google API key not found in environment variables. Set GOOGLE_API_KEY."
            logger.error(error_msg)
            return error_msg

        # Validate inputs
        if not (-90 <= latitude <= 90):
            error_msg = f"Invalid latitude: {latitude}. Must be between -90 and 90."
            logger.error(error_msg)
            return error_msg

        if not (-180 <= longitude <= 180):
            error_msg = f"Invalid longitude: {longitude}. Must be between -180 and 180."
            logger.error(error_msg)
            return error_msg

        # Use current time if no timestamp provided
        if timestamp is None:
            timestamp = int(time.time())

        # Google Timezone API endpoint
        url = "https://maps.googleapis.com/maps/api/timezone/json"

        params = {
            "location": f"{latitude},{longitude}",
            "timestamp": timestamp,
            "key": api_key
        }

        logger.info(
            f"Getting timezone info for coordinates: {latitude}, {longitude} at timestamp: {timestamp}")
        response = requests.get(url, params=params)
        response.raise_for_status()

        data = response.json()

        if data.get("status") != "OK":
            error_msg = f"Timezone API failed with status: {data.get('status')}. {data.get('errorMessage', 'Unknown error')}"
            logger.error(error_msg)
            return error_msg

        # Extract timezone information
        timezone_id = data.get("timeZoneId", "Unknown")
        timezone_name = data.get("timeZoneName", "Unknown")
        # Daylight saving time offset in seconds
        dst_offset = data.get("dstOffset", 0)
        raw_offset = data.get("rawOffset", 0)  # Raw offset from UTC in seconds

        # Calculate total offset
        total_offset_seconds = raw_offset + dst_offset
        total_offset_hours = total_offset_seconds / 3600

        # Convert timestamp to local time
        utc_time = datetime.fromtimestamp(timestamp, tz=timezone.utc)
        local_offset = timezone(offset=timedelta(seconds=total_offset_seconds))
        local_time = utc_time.astimezone(local_offset)

        # Create comprehensive result in JSON format
        timezone_result = {
            "coordinates": {"latitude": latitude, "longitude": longitude},
            "timestamp": timestamp,
            "timezone_id": timezone_id,
            "timezone_name": timezone_name,
            "raw_offset_seconds": raw_offset,
            "dst_offset_seconds": dst_offset,
            "total_offset_seconds": total_offset_seconds,
            "total_offset_hours": total_offset_hours,
            "local_time": local_time.isoformat(),
            "utc_time": utc_time.isoformat(),
            "formatted_local_time": local_time.strftime("%Y-%m-%d %H:%M:%S"),
            "formatted_utc_time": utc_time.strftime("%Y-%m-%d %H:%M:%S UTC"),
            "formatted_offset": f"{total_offset_hours:+.1f} hours ({total_offset_seconds:+d} seconds)",
            "formatted_raw_offset": f"{raw_offset/3600:+.1f} hours (Standard Time)",
            "formatted_dst_offset": f"{dst_offset/3600:+.1f} hours (Daylight Saving)",
            "full_response": data
        }

        return json.dumps(timezone_result, indent=2, ensure_ascii=False)

    except requests.exceptions.RequestException as e:
        error_msg = f"Failed to get timezone info for coordinates {latitude}, {longitude}. Network error: {repr(e)}"
        logger.error(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"Failed to get timezone info for coordinates {latitude}, {longitude}. Error: {repr(e)}"
        logger.error(error_msg)
        return error_msg


@tool
@log_io
def time_convert_tool(
    from_timezone: Annotated[str, "Source timezone ID (e.g., 'America/New_York', 'Europe/London')."],
    to_timezone: Annotated[str, "Target timezone ID (e.g., 'Asia/Tokyo', 'Australia/Sydney')."],
    time_str: Annotated[str, "Time string in format 'YYYY-MM-DD HH:MM:SS' or 'HH:MM:SS'."],
    date_str: Annotated[Optional[str],
                        "Date string in format 'YYYY-MM-DD' (default: today)."] = None
) -> str:
    """Use this to convert time between different timezones using timezone IDs."""
    try:
        from datetime import datetime, timedelta
        import pytz

        # Parse input time
        if date_str:
            full_time_str = f"{date_str} {time_str}"
            time_format = "%Y-%m-%d %H:%M:%S"
        else:
            if len(time_str.split()) == 2:  # Already includes date
                full_time_str = time_str
                time_format = "%Y-%m-%d %H:%M:%S"
            else:  # Only time provided, use today's date
                today = datetime.now().strftime("%Y-%m-%d")
                full_time_str = f"{today} {time_str}"
                time_format = "%Y-%m-%d %H:%M:%S"

        # Parse the datetime
        try:
            naive_datetime = datetime.strptime(full_time_str, time_format)
        except ValueError as e:
            return f"Invalid time format. Use 'YYYY-MM-DD HH:MM:SS' or 'HH:MM:SS'. Error: {e}"

        # Get timezone objects
        try:
            from_tz = pytz.timezone(from_timezone)
            to_tz = pytz.timezone(to_timezone)
        except pytz.exceptions.UnknownTimeZoneError as e:
            return f"Unknown timezone: {e}. Use standard timezone IDs like 'America/New_York', 'Europe/London'."

        # Localize the datetime to the source timezone
        localized_datetime = from_tz.localize(naive_datetime)

        # Convert to target timezone
        converted_datetime = localized_datetime.astimezone(to_tz)

        # Calculate time difference
        time_difference = converted_datetime.utcoffset() - localized_datetime.utcoffset()

        # Create comprehensive result in JSON format
        conversion_result = {
            "original_time": time_str,
            "original_date": date_str or datetime.now().strftime("%Y-%m-%d"),
            "from_timezone": from_timezone,
            "to_timezone": to_timezone,
            "source_datetime": localized_datetime.isoformat(),
            "target_datetime": converted_datetime.isoformat(),
            "source_formatted": localized_datetime.strftime("%Y-%m-%d %H:%M:%S %Z %z"),
            "target_formatted": converted_datetime.strftime("%Y-%m-%d %H:%M:%S %Z %z"),
            "source_detailed": localized_datetime.strftime("%A, %B %d, %Y at %I:%M:%S %p %Z"),
            "target_detailed": converted_datetime.strftime("%A, %B %d, %Y at %I:%M:%S %p %Z"),
            "time_difference_seconds": time_difference.total_seconds() if time_difference else 0,
            "time_difference_formatted": str(time_difference) if time_difference else "No difference"
        }

        return json.dumps(conversion_result, indent=2, ensure_ascii=False)

    except ImportError:
        return "Error: pytz library is not installed. Install it with: pip install pytz"
    except Exception as e:
        error_msg = f"Failed to convert time from {from_timezone} to {to_timezone}. Error: {repr(e)}"
        logger.error(error_msg)
        return error_msg


@tool
@log_io
def timezone_by_address_tool(
    address: Annotated[str, "The address or location to get timezone info for."],
    timestamp: Annotated[Optional[int],
                         "Unix timestamp for the time to check (default: current time)."] = None
) -> str:
    """Use this to get timezone information for a specific address. This tool first geocodes the address to get coordinates, then fetches timezone data."""
    try:
        # First, we need to geocode the address to get coordinates
        from .geocode import geocode_tool

        logger.info(f"Getting timezone info for address: {address}")
        geocode_result = geocode_tool.invoke({"address": address})

        if isinstance(geocode_result, str) and geocode_result.startswith("Error"):
            return f"Failed to geocode address '{address}' for timezone lookup: {geocode_result}"

        if "Google Maps API key not found" in geocode_result or "Google API key not found" in geocode_result:
            return f"Timezone lookup failed: {geocode_result}"

        if "No geocoding results found" in geocode_result:
            return f"Timezone lookup failed: No location found for address '{address}'"

        # Extract coordinates from geocode result
        try:
            # Parse the geocode result to extract latitude and longitude
            import re
            coord_match = re.search(
                r'Coordinates: ([-\d.]+), ([-\d.]+)', geocode_result)
            if not coord_match:
                return f"Failed to extract coordinates from geocode result for address '{address}'"

            latitude = float(coord_match.group(1))
            longitude = float(coord_match.group(2))

            logger.info(
                f"Extracted coordinates for '{address}': {latitude}, {longitude}")

            # Get timezone information using coordinates
            timezone_result = timezone_info_tool.invoke({
                "latitude": latitude,
                "longitude": longitude,
                "timestamp": timestamp
            })

            # Parse the JSON result and add address information
            try:
                timezone_data = json.loads(timezone_result)
                timezone_data["address"] = address
                timezone_data["formatted_address"] = address
                
                return json.dumps(timezone_data, indent=2, ensure_ascii=False)
            except json.JSONDecodeError:
                # If the timezone result is not JSON (error case), return it as is
                return timezone_result

        except (ValueError, AttributeError) as e:
            error_msg = f"Failed to parse coordinates from geocode result for '{address}': {repr(e)}"
            logger.error(error_msg)
            return error_msg

    except Exception as e:
        error_msg = f"Failed to get timezone info for address '{address}'. Error: {repr(e)}"
        logger.error(error_msg)
        return error_msg
