# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import logging
import os
import requests
from typing import Annotated, Dict, Any, Optional

from langchain_core.tools import tool
from .decorators import log_io

logger = logging.getLogger(__name__)


@tool
@log_io
def geocode_tool(
    address: Annotated[str, "The address or location to geocode."],
) -> str:
    """Use this to get geographic coordinates (latitude and longitude) and location details for a given address."""
    try:
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            error_msg = "Google Maps API key not found in environment variables. Set GOOGLE_MAPS_API_KEY."
            logger.error(error_msg)
            return error_msg

        # Google Maps Geocoding API endpoint
        url = "https://maps.googleapis.com/maps/api/geocode/json"

        params = {
            "address": address,
            "key": api_key
        }

        logger.info(f"Geocoding address: {address}")
        response = requests.get(url, params=params)
        response.raise_for_status()

        data = response.json()

        if data.get("status") != "OK":
            error_msg = f"Geocoding failed with status: {data.get('status')}. {data.get('error_message', '')}"
            logger.error(error_msg)
            return error_msg

        if not data.get("results"):
            return f"No geocoding results found for address: {address}"

        # Extract the first (most relevant) result
        result = data["results"][0]

        # Extract key information
        formatted_address = result.get("formatted_address", "")
        geometry = result.get("geometry", {})
        location = geometry.get("location", {})
        lat = location.get("lat")
        lng = location.get("lng")
        place_id = result.get("place_id", "")

        # Extract address components for more detailed information
        address_components = result.get("address_components", [])
        location_types = result.get("types", [])

        geocode_result = {
            "address": address,
            "formatted_address": formatted_address,
            "latitude": lat,
            "longitude": lng,
            "place_id": place_id,
            "location_types": location_types,
            "address_components": address_components,
            "geometry": geometry
        }

        return f"Geocoded location for '{address}':\n" \
            f"Formatted Address: {formatted_address}\n" \
            f"Coordinates: {lat}, {lng}\n" \
            f"Place ID: {place_id}\n" \
            f"Location Types: {', '.join(location_types)}\n" \
            f"Full result: {geocode_result}"

    except requests.exceptions.RequestException as e:
        error_msg = f"Failed to geocode address '{address}'. Network error: {repr(e)}"
        logger.error(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"Failed to geocode address '{address}'. Error: {repr(e)}"
        logger.error(error_msg)
        return error_msg


