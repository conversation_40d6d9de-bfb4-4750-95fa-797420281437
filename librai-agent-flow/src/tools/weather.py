# Author: <PERSON>g <PERSON>

import logging
import os
import requests
import re
import json
from typing import Annotated, Dict, Any, Optional

from langchain_core.tools import tool
from .decorators import log_io

logger = logging.getLogger(__name__)


def _geocode_address(address: str) -> tuple[float, float, str]:
    """Internal function to geocode an address and return coordinates."""
    try:
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            raise ValueError("Google Maps API key not found in environment variables. Set GOOGLE_API_KEY.")

        # Google Maps Geocoding API endpoint
        url = "https://maps.googleapis.com/maps/api/geocode/json"
        params = {
            "address": address,
            "key": api_key
        }

        logger.info(f"Geocoding address: {address}")
        response = requests.get(url, params=params)
        response.raise_for_status()

        data = response.json()

        if data.get("status") != "OK":
            error_msg = f"Geocoding failed with status: {data.get('status')}. {data.get('error_message', '')}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if not data.get("results"):
            raise ValueError(f"No geocoding results found for address: {address}")

        # Extract the first (most relevant) result
        result = data["results"][0]
        formatted_address = result.get("formatted_address", "")
        geometry = result.get("geometry", {})
        location = geometry.get("location", {})
        lat = location.get("lat")
        lng = location.get("lng")

        if lat is None or lng is None:
            raise ValueError(f"Invalid coordinates returned for address: {address}")

        return lat, lng, formatted_address

    except requests.exceptions.RequestException as e:
        error_msg = f"Failed to geocode address '{address}'. Network error: {repr(e)}"
        logger.error(error_msg)
        raise ValueError(error_msg)
    except Exception as e:
        error_msg = f"Failed to geocode address '{address}'. Error: {repr(e)}"
        logger.error(error_msg)
        raise ValueError(error_msg)





@tool
@log_io
def weather_tool(
    location: Annotated[str, "The address, city, or location to get weather for (e.g., 'New York', 'Tokyo, Japan', '123 Main St, Los Angeles')."],
    days: Annotated[int, "Number of forecast days to retrieve (default: 7, max: 14)."] = 7
) -> str:
    """Use this to get daily weather forecast for any location by address or city name. This tool automatically handles geocoding and weather lookup in one step.
    
    Args:
        location: Any address, city, or location name (e.g., 'Paris, France', 'Times Square, New York', 'Mount Everest')
        days: Number of forecast days (1-14, default: 7)
    
    Returns:
        JSON format daily weather forecast data with comprehensive information
    
    Examples:
        weather_tool("Tokyo, Japan")  # Daily forecast for 7 days
        weather_tool("Central Park, New York", 10)  # Daily forecast for 10 days
        weather_tool("Eiffel Tower, Paris", 14)  # Daily forecast for 14 days
    """
    try:
        # Validate days parameter
        if not (1 <= days <= 14):
            error_msg = f"Invalid days: {days}. Must be between 1 and 14."
            logger.error(error_msg)
            return error_msg

        logger.info(f"Getting daily weather for location: {location}")

        # Step 1: Geocode the address to get coordinates
        try:
            latitude, longitude, formatted_address = _geocode_address(location)
            logger.info(f"Geocoded '{location}' to coordinates: {latitude}, {longitude}")
        except ValueError as e:
            return f"Weather lookup failed: {str(e)}"

        # Step 2: Get daily weather forecast using coordinates
        try:
            api_key = os.getenv("GOOGLE_API_KEY")
            if not api_key:
                error_msg = "Google Weather API key not found in environment variables. Set GOOGLE_API_KEY."
                logger.error(error_msg)
                return error_msg

            # Google Weather API daily forecast endpoint
            url = "https://weather.googleapis.com/v1/forecast/days:lookup"

            params = {
                "key": api_key,
                "location.latitude": latitude,
                "location.longitude": longitude,
                "days": days
            }

            logger.info(f"Getting daily weather forecast for coordinates: {latitude}, {longitude} for {days} days")
            response = requests.get(url, params=params)
            response.raise_for_status()

            data = response.json()

            if "error" in data:
                error_msg = f"Weather API error: {data['error'].get('message', 'Unknown error')}"
                logger.error(error_msg)
                return error_msg

            forecast_days = data.get("forecastDays", [])
            if not forecast_days:
                return f"No daily weather forecast data available for location: {location}"

            # Extract timezone information
            timezone_info = data.get("timeZone", {})
            timezone_id = timezone_info.get("id", "UTC")

            # Process and enhance daily forecast data
            enhanced_forecast_days = []
            for i, day_data in enumerate(forecast_days):
                enhanced_day = day_data.copy()
                
                # Add formatted date string
                display_date = day_data.get("displayDate", {})
                if display_date:
                    year = display_date.get('year', 'N/A')
                    month = display_date.get('month', 'N/A')
                    day = display_date.get('day', 'N/A')
                    enhanced_day["formatted_date"] = f"{year}-{month:02d}-{day:02d}"
                
                # Add formatted high/low temperatures
                high_temp = day_data.get("highTemperature", {})
                low_temp = day_data.get("lowTemperature", {})
                if high_temp and high_temp.get("degrees") is not None:
                    temp_celsius = high_temp.get("degrees")
                    temp_unit = high_temp.get("unit", "CELSIUS")
                    enhanced_day["formatted_high_temp"] = f"{temp_celsius}°{temp_unit[0]}"
                if low_temp and low_temp.get("degrees") is not None:
                    temp_celsius = low_temp.get("degrees")
                    temp_unit = low_temp.get("unit", "CELSIUS")
                    enhanced_day["formatted_low_temp"] = f"{temp_celsius}°{temp_unit[0]}"
                
                # Add formatted weather condition
                weather_condition = day_data.get("weatherCondition", {})
                if weather_condition:
                    condition_desc = weather_condition.get("description", {}).get("text")
                    condition_type = weather_condition.get("type")
                    if condition_desc and condition_type:
                        enhanced_day["formatted_condition"] = f"{condition_desc} ({condition_type})"
                
                enhanced_forecast_days.append(enhanced_day)

            # Create comprehensive result
            weather_result = {
                "location": location,
                "formatted_address": formatted_address,
                "coordinates": {"latitude": latitude, "longitude": longitude},
                "timezone": timezone_id,
                "forecast_days_count": len(forecast_days),
                "forecast_data": enhanced_forecast_days,
                "current_conditions": enhanced_forecast_days[0] if enhanced_forecast_days else None,
                "full_response": data
            }

            return json.dumps(weather_result, indent=2, ensure_ascii=False)

        except requests.exceptions.RequestException as e:
            error_msg = f"Failed to get daily weather forecast for location '{location}'. Network error: {repr(e)}"
            logger.error(error_msg)
            return error_msg
        except Exception as e:
            error_msg = f"Failed to get daily weather forecast for location '{location}'. Error: {repr(e)}"
            logger.error(error_msg)
            return error_msg

    except Exception as e:
        error_msg = f"Failed to get daily weather for location '{location}'. Error: {repr(e)}"
        logger.error(error_msg)
        return error_msg



