# Author: <PERSON>g <PERSON>i

import os
import json
import logging
import re
from typing import Optional, Dict, Any
from langchain_core.tools import tool
from src.tools.decorators import log_io
import requests

logger = logging.getLogger(__name__)

# Get API key from environment
SERPAPI_API_KEY = os.getenv("SERPAPI_API_KEY", "")

# Constants for location formatting
LOCATION_FORMAT_MAPPING = {
    # Common country abbreviations
    "UAE": "United Arab Emirates",
    "USA": "United States",
    "UK": "United Kingdom",
    "CA": "Canada",
    "AU": "Australia",
    "DE": "Germany",
    "FR": "France",
    "IT": "Italy",
    "ES": "Spain",
    "JP": "Japan",
    "CN": "China",
    "IN": "India",
    "BR": "Brazil",
    "MX": "Mexico",
    "RU": "Russia",
    "KR": "South Korea",
    "SG": "Singapore",
    "HK": "Hong Kong",
    "TW": "Taiwan",
    "TH": "Thailand",
    "VN": "Vietnam",
    "MY": "Malaysia",
    "ID": "Indonesia",
    "PH": "Philippines",
    "TR": "Turkey",
    "SA": "Saudi Arabia",
    "EG": "Egypt",
    "ZA": "South Africa",
    "NL": "Netherlands",
    "SE": "Sweden",
    "NO": "Norway",
    "DK": "Denmark",
    "FI": "Finland",
    "CH": "Switzerland",
    "AT": "Austria",
    "BE": "Belgium",
    "PL": "Poland",
    "CZ": "Czech Republic",
    "HU": "Hungary",
    "RO": "Romania",
    "BG": "Bulgaria",
    "HR": "Croatia",
    "SI": "Slovenia",
    "SK": "Slovakia",
    "LT": "Lithuania",
    "LV": "Latvia",
    "EE": "Estonia",
    "IE": "Ireland",
    "PT": "Portugal",
    "GR": "Greece",
    "CY": "Cyprus",
    "MT": "Malta",
    "LU": "Luxembourg",
    "IS": "Iceland",
    "NZ": "New Zealand",
    "IL": "Israel",
    "AE": "United Arab Emirates",
    "QA": "Qatar",
    "KW": "Kuwait",
    "BH": "Bahrain",
    "OM": "Oman",
    "JO": "Jordan",
    "LB": "Lebanon",
    "SY": "Syria",
    "IQ": "Iraq",
    "IR": "Iran",
    "PK": "Pakistan",
    "AF": "Afghanistan",
    "BD": "Bangladesh",
    "LK": "Sri Lanka",
    "NP": "Nepal",
    "BT": "Bhutan",
    "MM": "Myanmar",
    "LA": "Laos",
    "KH": "Cambodia",
    "MN": "Mongolia",
    "KZ": "Kazakhstan",
    "UZ": "Uzbekistan",
    "KG": "Kyrgyzstan",
    "TJ": "Tajikistan",
    "TM": "Turkmenistan",
    "AZ": "Azerbaijan",
    "GE": "Georgia",
    "AM": "Armenia",
    "BY": "Belarus",
    "MD": "Moldova",
    "UA": "Ukraine",
    "RS": "Serbia",
    "ME": "Montenegro",
    "BA": "Bosnia and Herzegovina",
    "MK": "North Macedonia",
    "AL": "Albania",
    "XK": "Kosovo"
}

# Common location patterns that need special handling
LOCATION_PATTERNS = {
    r"dubai,\s*uae": "Dubai, United Arab Emirates",
    r"abu\s*dhabi,\s*uae": "Abu Dhabi, United Arab Emirates",
    r"sharjah,\s*uae": "Sharjah, United Arab Emirates",
    r"ajman,\s*uae": "Ajman, United Arab Emirates",
    r"ras\s*al\s*khaimah,\s*uae": "Ras Al Khaimah, United Arab Emirates",
    r"fujairah,\s*uae": "Fujairah, United Arab Emirates",
    r"umm\s*al\s*quwain,\s*uae": "Umm Al Quwain, United Arab Emirates"
}


def _format_location(location: str) -> str:
    """Format location string to be compatible with SerpAPI.
    
    Args:
        location: Raw location string
    
    Returns:
        Formatted location string compatible with SerpAPI
    """
    if not location:
        return location
    
    # Convert to lowercase for pattern matching
    location_lower = location.lower().strip()
    
    # Check for specific patterns first
    for pattern, replacement in LOCATION_PATTERNS.items():
        if re.search(pattern, location_lower):
            return replacement
    
    # Handle general country code replacements
    parts = location.split(',')
    if len(parts) >= 2:
        # Get the last part (country)
        country_part = parts[-1].strip()
        if country_part.upper() in LOCATION_FORMAT_MAPPING:
            # Replace the country code with full name
            parts[-1] = " " + LOCATION_FORMAT_MAPPING[country_part.upper()]
            return ','.join(parts)
    
    # If no specific formatting needed, return as is
    return location.strip()


def _validate_location(location: str) -> bool:
    """Validate if a location string is likely to be supported by SerpAPI.
    
    Args:
        location: Location string to validate
    
    Returns:
        True if location is likely valid, False otherwise
    """
    if not location:
        return True
    
    # Check for common problematic patterns
    problematic_patterns = [
        r'^\s*$',  # Empty or whitespace only
        r'[<>{}[\]\\|`~!@#$%^&*+=]',  # Special characters that might cause issues
        r'^\d+$',  # Numbers only
    ]
    
    for pattern in problematic_patterns:
        if re.search(pattern, location):
            return False
    
    return True


def _make_serpapi_request(params: Dict[str, Any]) -> Dict[str, Any]:
    """Make a request to SerpApi with the given parameters."""
    if not SERPAPI_API_KEY:
        raise ValueError("SERPAPI_API_KEY environment variable is not set")

    params["api_key"] = SERPAPI_API_KEY
    params["output"] = "json"

    # Log the request parameters for debugging (without the API key)
    debug_params = {k: v for k, v in params.items() if k != "api_key"}
    logger.info(f"SerpApi request params: {debug_params}")

    try:
        response = requests.get("https://serpapi.com/search", params=params)

        # Check if we got a successful response
        if response.status_code == 200:
            return response.json()
        else:
            # Try to get error details from response
            try:
                error_data = response.json()
                error_msg = error_data.get(
                    "error", f"HTTP {response.status_code}")
            except:
                error_msg = f"HTTP {response.status_code}: {response.text[:200]}"

            logger.error(f"SerpApi request failed: {error_msg}")
            raise ValueError(f"SerpApi error: {error_msg}")

    except requests.exceptions.RequestException as e:
        logger.error(f"SerpApi request failed: {e}")
        raise ValueError(f"Request failed: {str(e)}")


def _validate_event_filters(filters: str) -> str:
    """Convert simple filter format to SerpAPI htichips format.
    
    Args:
        filters: Simple filter string (e.g., "virtual,free,weekend")
    
    Returns:
        Converted SerpAPI htichips format string
    """
    if not filters:
        return None
    
    # Mapping from simple filters to SerpAPI format
    filter_mapping = {
        "virtual": "event_type:Virtual-Event",
        "online": "event_type:Virtual-Event",
        "in_person": "event_type:In-Person",
        "free": "price:free",
        "paid": "price:paid",
        "today": "date:today",
        "tomorrow": "date:tomorrow",
        "this_week": "date:this_week",
        "this_weekend": "date:this_weekend",
        "next_week": "date:next_week",
        "next_month": "date:next_month"
    }
    
    # Convert simple filters to SerpAPI format
    converted_filters = []
    for filter_item in filters.split(","):
        filter_item = filter_item.strip().lower()
        if filter_item in filter_mapping:
            converted_filters.append(filter_mapping[filter_item])
        else:
            logger.warning(f"Unknown filter: {filter_item}")
    
    return ",".join(converted_filters) if converted_filters else None


def _validate_flight_filters(filters: str) -> Dict[str, Any]:
    """Convert simple flight filter format to SerpAPI parameters.
    
    Args:
        filters: Simple filter string (e.g., "nonstop,cheapest,morning")
    
    Returns:
        Dictionary of SerpAPI parameters
    """
    if not filters:
        return {}
    
    # Mapping from simple filters to SerpAPI parameters
    filter_mapping = {
        # Stops
        "nonstop": {"stops": 0},
        "direct": {"stops": 0},
        "one_stop": {"stops": 1},
        "any_stops": {"stops": 2},
        
        # Travel class
        "economy": {"travel_class": "1"},
        "premium_economy": {"travel_class": "2"},
        "business": {"travel_class": "3"},
        "first": {"travel_class": "4"},
        
        # Time preferences
        "morning": {"departure_time": "1"},
        "afternoon": {"departure_time": "2"},
        "evening": {"departure_time": "3"},
        
        # Price preferences
        "cheapest": {"sort_by": "price"},
        "fastest": {"sort_by": "duration"},
        
        # Special options
        "hidden_city": {"show_hidden": True}
    }
    
    # Convert simple filters to SerpAPI parameters
    converted_params = {}
    for filter_item in filters.split(","):
        filter_item = filter_item.strip().lower()
        if filter_item in filter_mapping:
            converted_params.update(filter_mapping[filter_item])
        else:
            logger.warning(f"Unknown flight filter: {filter_item}")
    
    return converted_params


def _validate_hotel_filters(filters: str) -> Dict[str, Any]:
    """Convert simple hotel filter format to SerpAPI parameters.
    
    Args:
        filters: Simple filter string (e.g., "luxury,free_cancellation,pool")
    
    Returns:
        Dictionary of SerpAPI parameters
    """
    if not filters:
        return {}
    
    # Mapping from simple filters to SerpAPI parameters
    filter_mapping = {
        # Hotel class
        "budget": {"hotel_class": "2,3"},
        "midrange": {"hotel_class": "3,4"},
        "luxury": {"hotel_class": "4,5"},
        "5_star": {"hotel_class": "5"},
        "4_star": {"hotel_class": "4"},
        "3_star": {"hotel_class": "3"},
        
        # Rating
        "highly_rated": {"rating": "8"},
        "top_rated": {"rating": "9"},
        
        # Property type
        "hotels": {"vacation_rentals": False},
        "vacation_rentals": {"vacation_rentals": True},
        "apartments": {"vacation_rentals": True},
        
        # Amenities (simplified)
        "pool": {"amenities": "35"},
        "gym": {"amenities": "9"},
        "wifi": {"amenities": "19"},
        "parking": {"amenities": "20"},
        
        # Special features
        "free_cancellation": {"free_cancellation": True},
        "special_offers": {"special_offers": True},
        "eco_friendly": {"eco_certified": True},
        
        # Sort options
        "cheapest": {"sort_by": "3"},
        "highest_rated": {"sort_by": "8"},
        "most_reviewed": {"sort_by": "13"}
    }
    
    # Convert simple filters to SerpAPI parameters
    converted_params = {}
    for filter_item in filters.split(","):
        filter_item = filter_item.strip().lower()
        if filter_item in filter_mapping:
            converted_params.update(filter_mapping[filter_item])
        else:
            logger.warning(f"Unknown hotel filter: {filter_item}")
    
    return converted_params


@tool
@log_io
def google_events_search(
    query: str,
    location: Optional[str] = None,
    filters: Optional[str] = None,
    num_results: int = 10
) -> str:
    """Search for events using Google Events API.
    
    Args:
        query: The search query for events (e.g., "concerts", "tech conferences", "workshops")
        location: Location for events (e.g., "New York, NY", "San Francisco, CA", "London, UK")
        filters: Event filters in simple format:
                - "virtual" or "online" for virtual events
                - "in_person" for in-person events
                - "free" for free events
                - "paid" for paid events
                - "today", "tomorrow", "this_week", "this_weekend", "next_week", "next_month" for date filters
                - Multiple filters can be combined with commas: "virtual,free,weekend"
        num_results: Number of results to return (default: 10, max: 20)
    
    Returns:
        JSON string containing comprehensive event search results
    
    Examples:
        # Basic search
        google_events_search("music concerts", "Los Angeles")
        
        # Virtual events only
        google_events_search("tech conference", filters="virtual")
        
        # Free events this weekend
        google_events_search("workshops", "Chicago", filters="free,this_weekend")
        
        # Virtual free events next week
        google_events_search("webinars", filters="virtual,free,next_week")
    """
    # Format and validate location
    formatted_location = None
    if location:
        if not _validate_location(location):
            return json.dumps({
                "error": f"Invalid location format: {location}",
                "search_parameters": {
                    "query": query,
                    "location": location,
                    "filters": filters
                }
            }, indent=2)
        
        formatted_location = _format_location(location)
        logger.info(f"Original location: {location}, Formatted location: {formatted_location}")
    
    # Convert simple filters to SerpAPI format
    htichips = _validate_event_filters(filters)
    
    params = {
        "engine": "google_events",
        "q": query,
        "hl": "en",  # Default language
        "gl": "us"   # Default country
    }

    # Add optional parameters
    if formatted_location:
        params["location"] = formatted_location
    if htichips:
        params["htichips"] = htichips

    try:
        results = _make_serpapi_request(params)

        # Extract search metadata
        search_metadata = {
            "query": query,
            "location": formatted_location or location,
            "filters": filters,
            "total_results": len(results.get("events_results", []))
        }

        # Process events
        events = results.get("events_results", [])
        formatted_events = []

        for event in events[:num_results]:
            # Process date information
            date_info = event.get("date", {})

            # Process venue information
            venue_info = event.get("venue", {})
            venue_data = {
                "name": venue_info.get("name", ""),
                "rating": venue_info.get("rating"),
                "reviews": venue_info.get("reviews"),
                "link": venue_info.get("link", "")
            } if venue_info else {}

            # Process event location map
            event_map = event.get("event_location_map", {})
            location_map = {
                "image": event_map.get("image", ""),
                "link": event_map.get("link", ""),
                "serpapi_link": event_map.get("serpapi_link", "")
            } if event_map else {}

            # Process ticket information
            ticket_info = []
            for ticket in event.get("ticket_info", []):
                ticket_data = {
                    "source": ticket.get("source", ""),
                    "link": ticket.get("link", ""),
                    "link_type": ticket.get("link_type", ""),
                    "price": ticket.get("price", "")
                }
                ticket_info.append(ticket_data)

            formatted_event = {
                "title": event.get("title", ""),
                "date": {
                    "start_date": date_info.get("start_date", ""),
                    "when": date_info.get("when", ""),
                    "end_date": date_info.get("end_date", "")
                },
                "venue": venue_data,
                "address": event.get("address", []),
                "link": event.get("link", ""),
                "description": event.get("description", ""),
                "thumbnail": event.get("thumbnail", ""),
                "event_location_map": location_map,
                "ticket_info": ticket_info,
                "event_id": event.get("event_id", ""),
                "price": event.get("price", ""),
                "event_type": event.get("event_type", "")
            }
            formatted_events.append(formatted_event)

        # Include search information if available
        search_info = results.get("search_information", {})

        response = {
            "search_metadata": search_metadata,
            "search_information": search_info,
            "events": formatted_events,
            "pagination": {
                "results_count": len(formatted_events),
                "has_more": len(events) > num_results
            }
        }

        return json.dumps(response, indent=2, ensure_ascii=False)

    except Exception as e:
        error_msg = f"Google Events search failed for query '{query}'"
        if formatted_location:
            error_msg += f" in {formatted_location}"
        if filters:
            error_msg += f" with filters '{filters}'"
        error_msg += f": {str(e)}"

        logger.error(error_msg)
        return json.dumps({
            "error": str(e),
            "search_parameters": {
                "query": query,
                "location": formatted_location or location,
                "filters": filters
            }
        }, indent=2)


@tool
@log_io
def google_flights_search(
    departure_id: str,
    arrival_id: str,
    outbound_date: str,
    return_date: Optional[str] = None,
    passengers: Optional[str] = None,
    filters: Optional[str] = None,
    max_price: Optional[int] = None,
    currency: str = "USD"
) -> str:
    """Search for flights using Google Flights API.
    
    Args:
        departure_id: Departure airport code (e.g., "JFK", "LAX", "CDG")
        arrival_id: Arrival airport code (e.g., "LAX", "NRT", "LHR")
        outbound_date: Outbound date in YYYY-MM-DD format
        return_date: Return date in YYYY-MM-DD format (optional for one-way trips)
        passengers: Passenger configuration in simple format:
                   - "2 adults" (default: "1 adult")
                   - "2 adults, 1 child" 
                   - "1 adult, 2 children, 1 infant"
        filters: Flight filters in simple format:
                - "nonstop" or "direct" for non-stop flights
                - "one_stop" for flights with one stop
                - "economy", "business", "first" for travel class
                - "morning", "afternoon", "evening" for departure time
                - "cheapest" for lowest price flights
                - Multiple filters: "nonstop,business,morning"
        max_price: Maximum price filter in specified currency
        currency: Currency code for prices (e.g., "USD", "EUR", "GBP") (default: "USD")
    
    Returns:
        JSON string containing comprehensive flight search results
    
    Examples:
        # Basic one-way search
        google_flights_search("JFK", "LAX", "2024-12-01")
        
        # Round trip with passengers
        google_flights_search("JFK", "LAX", "2024-12-01", "2024-12-08", 
                            passengers="2 adults, 1 child")
        
        # Business class non-stop flights
        google_flights_search("JFK", "LAX", "2024-12-01", filters="nonstop,business")
        
        # Cheap morning flights
        google_flights_search("JFK", "LAX", "2024-12-01", filters="cheapest,morning", 
                            max_price=500)
    """
    # Parse passenger configuration
    adults = 1
    children = 0
    infants_in_seat = 0
    infants_on_lap = 0
    
    if passengers:
        # Simple passenger parsing
        for part in passengers.split(","):
            part = part.strip().lower()
            if "adult" in part:
                adults = int(part.split()[0])
            elif "child" in part:
                children = int(part.split()[0])
            elif "infant" in part:
                infants_in_seat = int(part.split()[0])
    
    # Convert simple filters to SerpAPI parameters
    filter_params = _validate_flight_filters(filters)
    
    params = {
        "engine": "google_flights",
        "departure_id": departure_id,
        "arrival_id": arrival_id,
        "outbound_date": outbound_date,
        "adults": adults,
        "travel_class": filter_params.get("travel_class", "1"),  # Default economy
        "currency": currency,
        "hl": "en",  # Default language
        "gl": "us"   # Default country
    }

    # Add passenger details if non-zero
    if children > 0:
        params["children"] = children
    if infants_in_seat > 0:
        params["infants_in_seat"] = infants_in_seat
    if infants_on_lap > 0:
        params["infants_on_lap"] = infants_on_lap

    # Set trip type based on return date
    if return_date:
        params["return_date"] = return_date
        params["type"] = 1  # Round trip
    else:
        params["type"] = 2  # One way

    # Add filter parameters
    params.update(filter_params)
    
    # Add price filter
    if max_price is not None:
        params["max_price"] = max_price

    try:
        results = _make_serpapi_request(params)

        def format_flight(flight):
            """Format a single flight option with detailed information."""
            flights = flight.get("flights", [])

            formatted_flight = {
                "price": flight.get("price"),
                "type": flight.get("type", ""),
                "total_duration": flight.get("total_duration"),
                "flights": [],
                "layovers": [],
                "carbon_emissions": flight.get("carbon_emissions", {}),
                "price_insights": flight.get("price_insights", {}),
                "booking_token": flight.get("booking_token", ""),
                "departure_token": flight.get("departure_token", ""),
                "extensions": flight.get("extensions", [])
            }

            # Process individual flight segments
            for segment in flights:
                flight_segment = {
                    "airline": segment.get("airline"),
                    "airline_logo": segment.get("airline_logo"),
                    "flight_number": segment.get("flight_number"),
                    "departure_airport": {
                        "name": segment.get("departure_airport", {}).get("name"),
                        "id": segment.get("departure_airport", {}).get("id"),
                        "time": segment.get("departure_airport", {}).get("time")
                    },
                    "arrival_airport": {
                        "name": segment.get("arrival_airport", {}).get("name"),
                        "id": segment.get("arrival_airport", {}).get("id"),
                        "time": segment.get("arrival_airport", {}).get("time")
                    },
                    "duration": segment.get("duration"),
                    "airplane": segment.get("airplane"),
                    "often_delayed_by_over_30_min": segment.get("often_delayed_by_over_30_min"),
                    "legroom": segment.get("legroom"),
                    "extensions": segment.get("extensions", []),
                    "overnight": segment.get("overnight", False),
                    "travel_class": segment.get("travel_class")
                }
                formatted_flight["flights"].append(flight_segment)

            # Process layovers
            layovers = flight.get("layovers", [])
            for layover in layovers:
                layover_info = {
                    "duration": layover.get("duration"),
                    "name": layover.get("name"),
                    "id": layover.get("id"),
                    "overnight": layover.get("overnight", False)
                }
                formatted_flight["layovers"].append(layover_info)

            return formatted_flight

        # Process best flights
        best_flights = results.get("best_flights", [])
        formatted_best_flights = [format_flight(
            flight) for flight in best_flights]

        # Process other flights (limit to 10 for performance)
        other_flights = results.get("other_flights", [])
        formatted_other_flights = [format_flight(
            flight) for flight in other_flights[:10]]

        # Extract price insights if available
        price_insights = results.get("price_insights", {})

        # Extract booking options
        booking_options = results.get("booking_options", [])

        response = {
            "search_metadata": {
                "departure_id": departure_id,
                "arrival_id": arrival_id,
                "outbound_date": outbound_date,
                "return_date": return_date,
                "passengers": {
                    "adults": adults,
                    "children": children,
                    "infants_in_seat": infants_in_seat,
                    "infants_on_lap": infants_on_lap
                },
                "filters": filters,
                "currency": currency,
                "trip_type": "round_trip" if return_date else "one_way"
            },
            "price_insights": price_insights,
            "best_flights": formatted_best_flights,
            "other_flights": formatted_other_flights,
            "booking_options": booking_options[:5] if booking_options else [],
            "airports": results.get("airports", [])
        }

        return json.dumps(response, indent=2, ensure_ascii=False)
    except Exception as e:
        error_msg = f"Google Flights search failed for route {departure_id} -> {arrival_id} on {outbound_date}"
        if return_date:
            error_msg += f" (return: {return_date})"
        error_msg += f": {str(e)}"
        logger.error(error_msg)
        return json.dumps({
            "error": str(e),
            "search_parameters": {
                "departure_id": departure_id,
                "arrival_id": arrival_id,
                "outbound_date": outbound_date,
                "return_date": return_date,
                "passengers": passengers or "1 adult",
                "filters": filters
            }
        }, indent=2)


@tool
@log_io
def google_hotels_search(
    location: str,
    check_in_date: str,
    check_out_date: str,
    guests: Optional[str] = None,
    filters: Optional[str] = None,
    min_price: Optional[int] = None,
    max_price: Optional[int] = None,
    currency: str = "USD"
) -> str:
    """Search for hotels and accommodations using Google Hotels API.
    
    Args:
        location: Location or hotel name to search (e.g., "Paris, France", "Hilton Paris")
        check_in_date: Check-in date in YYYY-MM-DD format
        check_out_date: Check-out date in YYYY-MM-DD format
        guests: Guest configuration in simple format:
               - "2 adults" (default: "2 adults")
               - "2 adults, 2 children" 
               - "4 adults, 2 children, ages 8,12"
        filters: Hotel filters in simple format:
                - "luxury", "midrange", "budget" for hotel class
                - "5_star", "4_star", "3_star" for specific ratings
                - "highly_rated", "top_rated" for rating filters
                - "hotels" or "vacation_rentals" for property type
                - "pool", "gym", "wifi", "parking" for amenities
                - "free_cancellation", "special_offers", "eco_friendly" for special features
                - "cheapest", "highest_rated", "most_reviewed" for sorting
                - Multiple filters: "luxury,pool,free_cancellation"
        min_price: Minimum price per night in specified currency
        max_price: Maximum price per night in specified currency
        currency: Currency code for prices (e.g., "USD", "EUR", "GBP") (default: "USD")
    
    Returns:
        JSON string containing comprehensive hotel and accommodation search results
    
    Examples:
        # Basic hotel search
        google_hotels_search("Tokyo, Japan", "2024-12-01", "2024-12-03")
        
        # Luxury hotels with pool
        google_hotels_search("New York", "2024-12-01", "2024-12-03", 
                           filters="luxury,pool,free_cancellation")
        
        # Family vacation rental
        google_hotels_search("Bali", "2024-12-01", "2024-12-07", 
                           guests="4 adults, 2 children", filters="vacation_rentals")
        
        # Budget hotels with free cancellation
        google_hotels_search("Orlando", "2024-12-01", "2024-12-05",
                           filters="budget,free_cancellation", max_price=150)
    """
    # Format and validate location
    formatted_location = None
    if location:
        if not _validate_location(location):
            return json.dumps({
                "error": f"Invalid location format: {location}",
                "search_parameters": {
                    "query": location,
                    "check_in_date": check_in_date,
                    "check_out_date": check_out_date,
                    "guests": guests or "2 adults",
                    "filters": filters
                }
            }, indent=2)
        
        formatted_location = _format_location(location)
        logger.info(f"Original location: {location}, Formatted location: {formatted_location}")
    
    # Parse guest configuration
    adults = 2
    children = 0
    children_ages = None
    
    if guests:
        # Simple guest parsing
        for part in guests.split(","):
            part = part.strip().lower()
            if "adult" in part:
                adults = int(part.split()[0])
            elif "child" in part:
                children = int(part.split()[0])
            elif "age" in part:
                # Extract ages from "ages 8,12" format
                ages_part = part.replace("ages", "").strip()
                children_ages = ages_part
    
    # Convert simple filters to SerpAPI parameters
    filter_params = _validate_hotel_filters(filters)
    
    params = {
        "engine": "google_hotels",
        "q": formatted_location or location,
        "check_in_date": check_in_date,
        "check_out_date": check_out_date,
        "adults": adults,
        "currency": currency,
        "hl": "en",  # Default language
        "gl": "us"   # Default country
    }

    # Add optional parameters
    if children > 0:
        params["children"] = children
    if children_ages:
        params["children_ages"] = children_ages
    if min_price is not None:
        params["min_price"] = min_price
    if max_price is not None:
        params["max_price"] = max_price
    
    # Add filter parameters
    params.update(filter_params)

    try:
        results = _make_serpapi_request(params)

        # Extract search information
        search_info = results.get("search_information", {})
        search_metadata = {
            "query": formatted_location or location,
            "check_in_date": check_in_date,
            "check_out_date": check_out_date,
            "guests": {
                "adults": adults,
                "children": children
            },
            "filters": filters,
            "currency": currency,
            "result_type": search_info.get("hotels_results_state", "property_search")
        }

        # Check if this is a property details view (single hotel)
        if search_info.get("hotels_results_state") == "Showing results for property details":
            # Handle single property details response
            property_details = {
                "type": results.get("type", ""),
                "name": results.get("name", ""),
                "description": results.get("description", ""),
                "link": results.get("link", ""),
                "address": results.get("address", ""),
                "phone": results.get("phone", ""),
                "phone_link": results.get("phone_link", ""),
                "gps_coordinates": results.get("gps_coordinates", {}),
                "check_in_time": results.get("check_in_time", ""),
                "check_out_time": results.get("check_out_time", ""),
                "rate_per_night": results.get("rate_per_night", {}),
                "total_rate": results.get("total_rate", {}),
                "overall_rating": results.get("overall_rating", ""),
                "reviews": results.get("reviews", ""),
                "location_rating": results.get("location_rating", ""),
                "amenities": results.get("amenities", []),
                "excluded_amenities": results.get("excluded_amenities", []),
                "essential_info": results.get("essential_info", []),
                "images": results.get("images", []),
                "property_token": results.get("property_token", ""),
                "serpapi_property_details_link": results.get("serpapi_property_details_link", "")
            }

            response = {
                "search_metadata": search_metadata,
                "search_information": search_info,
                "property_details": property_details
            }
        else:
            # Handle properties search results
            properties = results.get("properties", [])
            formatted_properties = []

            for property_item in properties:
                # Process rate information
                rate_per_night = property_item.get("rate_per_night", {})
                total_rate = property_item.get("total_rate", {})

                # Process GPS coordinates
                gps_coords = property_item.get("gps_coordinates", {})

                # Process images (limit to 5 for performance)
                images = []
                for img in property_item.get("images", [])[:5]:
                    image_data = {
                        "thumbnail": img.get("thumbnail", ""),
                        "original_image": img.get("original_image", "")
                    }
                    images.append(image_data)

                formatted_property = {
                    "name": property_item.get("name", ""),
                    "description": property_item.get("description", ""),
                    "link": property_item.get("link", ""),
                    "rate_per_night": {
                        "lowest": rate_per_night.get("lowest", ""),
                        "extracted_lowest": rate_per_night.get("extracted_lowest"),
                        "before_taxes_fees": rate_per_night.get("before_taxes_fees", ""),
                        "extracted_before_taxes_fees": rate_per_night.get("extracted_before_taxes_fees")
                    },
                    "total_rate": {
                        "lowest": total_rate.get("lowest", ""),
                        "extracted_lowest": total_rate.get("extracted_lowest"),
                        "before_taxes_fees": total_rate.get("before_taxes_fees", ""),
                        "extracted_before_taxes_fees": total_rate.get("extracted_before_taxes_fees")
                    },
                    "overall_rating": property_item.get("overall_rating"),
                    "reviews": property_item.get("reviews"),
                    "location_rating": property_item.get("location_rating"),
                    "hotel_class": property_item.get("hotel_class"),
                    "amenities": property_item.get("amenities", []),
                    "excluded_amenities": property_item.get("excluded_amenities", []),
                    "essential_info": property_item.get("essential_info", []),
                    "nearby_places": property_item.get("nearby_places", []),
                    "images": images,
                    "gps_coordinates": gps_coords,
                    "property_token": property_item.get("property_token", ""),
                    "serpapi_property_details_link": property_item.get("serpapi_property_details_link", "")
                }
                formatted_properties.append(formatted_property)

            # Extract pagination information
            pagination = results.get("serpapi_pagination", {})

            response = {
                "search_metadata": search_metadata,
                "search_information": search_info,
                "properties": formatted_properties,
                "pagination": {
                    "current_from": pagination.get("current_from"),
                    "current_to": pagination.get("current_to"),
                    "next_page_token": pagination.get("next_page_token"),
                    "next_link": pagination.get("next"),
                    "has_next_page": bool(pagination.get("next_page_token"))
                },
                "ads": results.get("ads", [])
            }

        return json.dumps(response, indent=2, ensure_ascii=False)

    except Exception as e:
        error_msg = f"Google Hotels search failed for '{formatted_location or location}'"
        error_msg += f" from {check_in_date} to {check_out_date}"
        if filters:
            error_msg += f" with filters '{filters}'"
        error_msg += f": {str(e)}"

        logger.error(error_msg)
        return json.dumps({
            "error": str(e),
            "search_parameters": {
                "query": formatted_location or location,
                "check_in_date": check_in_date,
                "check_out_date": check_out_date,
                "guests": guests or "2 adults",
                "filters": filters
            }
        }, indent=2)


@tool
@log_io
def google_local_search(
    query: str,
    location: Optional[str] = None,
    num_results: int = 10
) -> str:
    """Search for local businesses and places using Google Local search API.
    
    Args:
        query: Search query for local businesses (e.g., "coffee shops", "restaurants near me", "pizza")
        location: Location to search in (e.g., "New York, NY", "San Francisco, CA", "London, UK")
                 If not provided, Google will use location from IP or query context
        num_results: Number of results to return (default: 10, max: 20)
    
    Returns:
        JSON string containing comprehensive local search results
    
    Examples:
        # Basic local search
        google_local_search("restaurants", "Chicago, IL")
        
        # Search without location (uses IP-based location)
        google_local_search("coffee shops near me")
        
        # Specific place lookup
        google_local_search("coffee")
        
        # More results
        google_local_search("hotels", "Paris, France", num_results=20)
    """
    # Format and validate location
    formatted_location = None
    if location:
        if not _validate_location(location):
            return json.dumps({
                "error": f"Invalid location format: {location}",
                "search_parameters": {
                    "query": query,
                    "location": location,
                    "num_results": num_results,
                }
            }, indent=2)
        
        formatted_location = _format_location(location)
        logger.info(f"Original location: {location}, Formatted location: {formatted_location}")
    
    params = {
        "engine": "google_local",
        "q": query,
        "hl": "en",  # Default language
        "gl": "us",  # Default country
        "num": min(num_results, 20)  # API maximum is 20
    }

    # Add optional parameters
    if formatted_location:
        params["location"] = formatted_location

    try:
        results = _make_serpapi_request(params)

        # Extract search metadata
        search_info = results.get("search_information", {})
        search_metadata = {
            "query": query,
            "location": formatted_location or location,
            "total_results": search_info.get("local_results_state", ""),
            "search_query": search_info.get("query_displayed", query)
        }

        # Process local results - according to API docs, it's an array directly under local_results
        local_results = results.get("local_results", [])
        formatted_places = []

        for place in local_results:
            # Process GPS coordinates
            gps_coords = place.get("gps_coordinates", {})

            # Process service options
            service_options = place.get("service_options", {})

            # Process reviews information
            reviews_original = place.get("reviews_original", "")
            reviews_count = place.get("reviews", 0)

            # Format comprehensive place information
            formatted_place = {
                "position": place.get("position"),
                "title": place.get("title", ""),
                "rating": place.get("rating"),
                "reviews": {
                    "count": reviews_count,
                    "original_text": reviews_original
                },
                "price_level": place.get("price", ""),
                "type": place.get("type", ""),
                "address": place.get("address", ""),
                "phone": place.get("phone", ""),
                "website": place.get("website", ""),
                "description": place.get("description", ""),
                "hours": place.get("hours", ""),
                "popular_times": place.get("popular_times", {}),
                "service_options": {
                    "dine_in": service_options.get("dine_in"),
                    "takeout": service_options.get("takeout"),
                    "delivery": service_options.get("delivery"),
                    "no_delivery": service_options.get("no_delivery"),
                    "curbside_pickup": service_options.get("curbside_pickup"),
                    "no_contact_delivery": service_options.get("no_contact_delivery"),
                    "in_store_shopping": service_options.get("in_store_shopping")
                },
                "gps_coordinates": {
                    "latitude": gps_coords.get("latitude"),
                    "longitude": gps_coords.get("longitude")
                },
                "place_id": place.get("place_id", ""),
                "place_id_search": place.get("place_id_search", ""),
                "lsig": place.get("lsig", ""),
                "thumbnail": place.get("thumbnail", ""),
                "menu_highlights": place.get("menu_highlights", []),
                # Limit to 3 images for performance
                "images": place.get("images", [])[:3]
            }
            formatted_places.append(formatted_place)

        # Process additional sections
        discover_more_places = results.get("discover_more_places", [])
        formatted_discover_more = []
        for item in discover_more_places:
            discover_item = {
                "title": item.get("title", ""),
                "link": item.get("link", ""),
                "serpapi_link": item.get("serpapi_link", ""),
                "thumbnail": item.get("thumbnail", "")
            }
            formatted_discover_more.append(discover_item)

        # Extract pagination information
        pagination = results.get("pagination", {})
        serpapi_pagination = results.get("serpapi_pagination", {})

        # Comprehensive response structure
        response = {
            "search_metadata": search_metadata,
            "search_information": search_info,
            "local_results": formatted_places,
            "discover_more_places": formatted_discover_more,
            "pagination": {
                "current": pagination.get("current", 1),
                "next": pagination.get("next", ""),
                "other_pages": pagination.get("other_pages", {}),
                "serpapi_next_link": serpapi_pagination.get("next_link", ""),
                "serpapi_next": serpapi_pagination.get("next", ""),
                "serpapi_other_pages": serpapi_pagination.get("other_pages", {})
            },
            "results_count": len(formatted_places),
            "has_more_results": bool(pagination.get("next")) or bool(serpapi_pagination.get("next"))
        }

        return json.dumps(response, indent=2, ensure_ascii=False)

    except Exception as e:
        error_msg = f"Google Local search failed for query '{query}'"
        if formatted_location:
            error_msg += f" in {formatted_location}"
        error_msg += f": {str(e)}"

        logger.error(error_msg)
        return json.dumps({
            "error": str(e),
            "search_parameters": {
                "query": query,
                "location": formatted_location or location,
                "num_results": num_results,
            }
        }, indent=2)


# Tools are already decorated with @tool and @log_io
# No need to create separate logged versions
