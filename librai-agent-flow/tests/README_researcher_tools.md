# Researcher工具测试指南

## 概述

本文档提供了测试researcher节点新添加工具的详细说明。我们创建了两个测试脚本：

1. `test_quick_tools.py` - 快速测试脚本（推荐）
2. `test_researcher_tools.py` - 完整测试脚本

## 环境准备

### 1. 安装依赖

确保已安装项目依赖：
```bash
uv sync
```

### 2. 配置API密钥

在 `.env` 文件中配置以下API密钥：

```bash
# Google API密钥（用于地理编码、时区、天气）
GOOGLE_API_KEY=your_google_api_key_here

# 汇率API密钥（用于货币转换）
EXCHANGE_RATE_API_KEY=your_exchange_rate_api_key_here

# Tavily搜索API密钥（用于网络搜索）
TAVILY_API_KEY=your_tavily_api_key_here
```

### 3. 配置LLM模型

确保 `conf.yaml` 文件已正确配置LLM模型。

## 运行测试

### 快速测试（推荐）

```bash
# 从项目根目录运行快速测试
python tests/integration/test_quick_tools.py
```

这个脚本会：
- 检查环境变量配置
- 单独测试每个工具的功能
- 简单测试researcher节点

### 完整测试

```bash
# 从项目根目录运行完整测试
python tests/integration/test_researcher_tools.py
```

这个脚本会：
- 执行更详细的测试用例
- 测试researcher节点在完整工作流中的表现
- 提供更详细的日志输出

### 使用pytest运行

```bash
# 运行所有集成测试
pytest tests/integration/

# 运行特定的测试文件
pytest tests/integration/test_quick_tools.py
pytest tests/integration/test_researcher_tools.py
```

## 测试内容

### 1. 工具功能测试

#### 地理编码工具 (`geocode_tool`)
- **功能**: 将地址转换为地理坐标
- **测试用例**: "北京市天安门广场"
- **期望结果**: 返回经纬度坐标

#### 时区转换工具 (`time_convert_tool`)
- **功能**: 在不同时区之间转换时间
- **测试用例**: 北京时间12:00转换为纽约时间
- **期望结果**: 返回转换后的时间

#### 天气查询工具 (`weather_by_address_tool`)
- **功能**: 根据地址查询天气信息
- **测试用例**: "上海市"的24小时天气预报
- **期望结果**: 返回天气数据

#### 货币转换工具 (`currency_convert_tool`)
- **功能**: 货币汇率转换
- **测试用例**: 100美元转换为人民币
- **期望结果**: 返回转换金额和汇率

### 2. Researcher节点测试

测试researcher节点是否能：
- 正确加载所有工具
- 根据查询内容选择合适的工具
- 生成有效的观察结果

## 测试用例示例

### 基础查询
```python
# 地理编码查询
"请查询北京市天安门广场的地理坐标信息"

# 时区转换查询
"请将北京时间2024年1月1日12:00转换为纽约时间"

# 天气查询
"请查询上海今天的天气情况"

# 货币转换
"请将100美元转换为人民币，并查询当前汇率"
```

### 综合查询
```python
# 多工具综合查询
"请查询东京的地理位置、当前时区、天气情况和日元对美元的汇率"
```

## 预期结果

### 成功情况
- ✅ 工具能够正常调用并返回结果
- ✅ researcher节点能够选择合适的工具
- ✅ 生成有效的观察结果
- ✅ 日志显示工具调用过程

### 可能的问题

#### 1. API密钥未配置
```
⚠️  GOOGLE_API_KEY: 未配置
❌ 地理编码工具测试失败: Google Maps API key not found
```

**解决方案**: 在 `.env` 文件中配置相应的API密钥

#### 2. 网络连接问题
```
❌ 工具测试失败: Network error
```

**解决方案**: 检查网络连接和API服务状态

#### 3. 工具导入错误
```
❌ 工具测试失败: ModuleNotFoundError
```

**解决方案**: 确保项目依赖已正确安装

#### 4. LLM配置问题
```
❌ researcher节点测试失败: LLM configuration error
```

**解决方案**: 检查 `conf.yaml` 中的LLM配置

## 调试建议

### 1. 查看详细日志
```bash
# 设置更详细的日志级别
export PYTHONPATH=.
python -u tests/integration/test_quick_tools.py 2>&1 | tee test_output.log
```

### 2. 单独测试工具
```python
# 在Python交互式环境中测试
from src.tools.geocode import geocode_tool
result = geocode_tool.invoke({"address": "北京市天安门广场"})
print(result)
```

### 3. 检查工具列表
```python
# 检查researcher节点加载的工具
from src.graph.nodes import researcher_node
# 查看日志输出中的工具列表
```

## 常见问题

### Q: 为什么某些工具测试失败？
A: 最常见的原因是API密钥未配置或配置错误。请检查 `.env` 文件中的配置。

### Q: 如何获取API密钥？
A: 
- Google API: https://console.cloud.google.com/
- Exchange Rate API: https://exchangerate-api.com/
- Tavily API: https://app.tavily.com/

### Q: 测试脚本运行很慢怎么办？
A: 这是正常的，因为需要调用外部API。可以只运行快速测试脚本。

### Q: 如何添加新的测试用例？
A: 在 `test_researcher_tools.py` 中的 `TEST_CASES` 列表中添加新的测试用例。

### Q: 如何集成到CI/CD流程？
A: 可以将这些测试添加到项目的测试套件中，在CI/CD流程中自动运行。

## 测试文件结构

```
tests/
├── integration/
│   ├── test_quick_tools.py          # 快速测试脚本
│   ├── test_researcher_tools.py     # 完整测试脚本
│   └── ...                          # 其他集成测试
├── unit/
│   └── ...                          # 单元测试
└── README_researcher_tools.md       # 本说明文档
```

## 下一步

测试通过后，你可以：

1. 在主项目中使用这些工具
2. 通过Web界面测试完整的工作流
3. 根据实际需求调整工具配置
4. 添加更多自定义工具
5. 将测试集成到项目的测试套件中

## 联系支持

如果遇到问题，请：
1. 检查日志输出
2. 确认环境配置
3. 查看项目文档
4. 提交Issue到项目仓库 