import dill
import json
from typing import Dict, Any, TypedDict, Type, Callable, Optional
from pathlib import Path
from langgraph.graph import StateGraph, START, END
from langgraph.graph.graph import Graph
from dataclasses import asdict
import inspect


class GraphMetadata(TypedDict):
    """Metadata structure for serialized graphs"""
    nodes: Dict[str, Dict[str, Any]]
    edges: list[Dict[str, Any]]
    conditional_edges: list[Dict[str, Any]]
    entry_points: list[str]
    finish_points: list[str]
    state_schema_name: str
    state_schema_module: str


def serialize_builder(
    builder: StateGraph,
    filepath: str,
    include_functions: bool = True,
    metadata_only: bool = False
) -> Dict[str, Any]:
    """
    Serialize a LangGraph StateGraph builder to file.
    
    Args:
        builder: The StateGraph builder to serialize
        filepath: Path to save the serialized data
        include_functions: Whether to include node functions (requires dill)
        metadata_only: If True, only save metadata structure
    
    Returns:
        Dictionary containing serialization info
    """
    filepath = Path(filepath)
    
    # Extract state schema information
    state_schema = builder._schema if hasattr(builder, '_schema') else builder.channels
    state_schema_info = {
        'name': getattr(state_schema, '__name__', 'State'),
        'module': getattr(state_schema, '__module__', '__main__')
    }
    
    # Extract graph structure
    metadata = {
        'nodes': {},
        'edges': [],
        'conditional_edges': [],
        'entry_points': [],
        'finish_points': [],
        'state_schema_name': state_schema_info['name'],
        'state_schema_module': state_schema_info['module']
    }
    
    # Extract nodes
    for node_name, node_spec in builder.nodes.items():
        node_info = {
            'name': node_name,
            'function_name': getattr(node_spec.runnable, '__name__', str(node_spec.runnable)),
            'metadata': node_spec.metadata
        }
        
        if include_functions and not metadata_only:
            # Store the actual function
            node_info['function'] = node_spec.runnable
            
        metadata['nodes'][node_name] = node_info
    
    # Extract edges
    for source, target in builder.edges:
        metadata['edges'].append({
            'source': source,
            'target': target
        })
    
    # Extract conditional edges
    for source, condition_info in builder.branches.items():
        for condition_name, branch in condition_info.items():
            metadata['conditional_edges'].append({
                'source': source,
                'condition_name': condition_name,
                'condition_function': branch.condition if include_functions and not metadata_only else None,
                'path_map': branch.ends if branch.ends else {}
            })
    
    # Create serialization package
    serialization_data = {
        'metadata': metadata,
        'state_schema': state_schema if include_functions and not metadata_only else None,
        'builder': builder if include_functions and not metadata_only else None,
        'version': '1.0'
    }
    
    # Save to file
    if metadata_only:
        # Save as JSON for metadata only
        with open(filepath.with_suffix('.json'), 'w') as f:
            # Remove non-serializable items for JSON
            json_metadata = {k: v for k, v in metadata.items() if k not in ['function', 'condition_function']}
            json.dump({'metadata': json_metadata, 'version': '1.0'}, f, indent=2)
    else:
        # Save with dill for full serialization
        with open(filepath.with_suffix('.pkl'), 'wb') as f:
            dill.dump(serialization_data, f)
    
    return serialization_data


def deserialize_builder(
    filepath: str,
    state_schema: Optional[Type] = None,
    node_functions: Optional[Dict[str, Callable]] = None
) -> StateGraph:
    """
    Deserialize a StateGraph builder from file.
    
    Args:
        filepath: Path to the serialized file
        state_schema: State schema class (required for metadata-only files)
        node_functions: Dictionary of node functions (required for metadata-only files)
    
    Returns:
        Reconstructed StateGraph builder
    """
    filepath = Path(filepath)
    
    if filepath.suffix == '.json':
        # Load metadata-only file
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        if state_schema is None or node_functions is None:
            raise ValueError("state_schema and node_functions are required for metadata-only deserialization")
        
        return _rebuild_from_metadata(data['metadata'], state_schema, node_functions)
    
    elif filepath.suffix == '.pkl':
        # Load full serialization
        with open(filepath, 'rb') as f:
            data = dill.load(f)
        
        if 'builder' in data and data['builder'] is not None:
            # Direct builder restoration
            return data['builder']
        else:
            # Reconstruct from metadata
            if state_schema is None or node_functions is None:
                raise ValueError("state_schema and node_functions are required when builder is not directly saved")
            
            return _rebuild_from_metadata(data['metadata'], state_schema, node_functions)
    
    else:
        raise ValueError(f"Unsupported file extension: {filepath.suffix}")


def _rebuild_from_metadata(
    metadata: Dict[str, Any],
    state_schema: Type,
    node_functions: Dict[str, Callable]
) -> StateGraph:
    """
    Rebuild StateGraph from metadata structure.
    
    Args:
        metadata: Graph metadata dictionary
        state_schema: State schema class
        node_functions: Dictionary mapping node names to functions
    
    Returns:
        Reconstructed StateGraph builder
    """
    # Create new builder
    builder = StateGraph(state_schema)
    
    # Add nodes
    for node_name, node_info in metadata['nodes'].items():
        if node_name in node_functions:
            builder.add_node(
                node_name, 
                node_functions[node_name],
                metadata=node_info.get('metadata')
            )
        else:
            raise ValueError(f"Function for node '{node_name}' not found in node_functions")
    
    # Add regular edges
    for edge_info in metadata['edges']:
        source = edge_info['source']
        target = edge_info['target']
        
        # Handle special START/END nodes
        if source == '__start__':
            source = START
        if target == '__end__':
            target = END
            
        builder.add_edge(source, target)
    
    # Add conditional edges
    for cond_edge in metadata['conditional_edges']:
        source = cond_edge['source']
        condition_func = cond_edge.get('condition_function')
        path_map = cond_edge.get('path_map', {})
        
        if condition_func is not None:
            # Use saved condition function
            builder.add_conditional_edges(source, condition_func, path_map)
        else:
            # This would require the condition function to be provided separately
            print(f"Warning: Conditional edge from {source} missing condition function")
    
    return builder


def extract_graph_structure(compiled_graph) -> Dict[str, Any]:
    """
    Extract structural information from a compiled LangGraph.
    
    Args:
        compiled_graph: A compiled LangGraph instance
    
    Returns:
        Dictionary containing graph structure information
    """
    try:
        # Get the graph representation
        graph_repr = compiled_graph.get_graph()
        
        structure = {
            'nodes': {},
            'edges': [],
            'node_count': len(graph_repr.nodes),
            'edge_count': len(graph_repr.edges)
        }
        
        # Extract node information
        for node_id, node in graph_repr.nodes.items():
            structure['nodes'][node_id] = {
                'id': node.id,
                'name': node.name,
                'data_type': str(type(node.data)),
                'metadata': node.metadata
            }
        
        # Extract edge information
        for edge in graph_repr.edges:
            structure['edges'].append({
                'source': edge.source,
                'target': edge.target,
                'conditional': edge.conditional,
                'data': edge.data
            })
        
        return structure
        
    except Exception as e:
        print(f"Error extracting graph structure: {e}")
        return {}


def compare_graph_structures(structure1: Dict[str, Any], structure2: Dict[str, Any]) -> Dict[str, Any]:
    """
    Compare two graph structures to identify differences.
    
    Args:
        structure1: First graph structure
        structure2: Second graph structure
    
    Returns:
        Dictionary containing comparison results
    """
    comparison = {
        'nodes_match': True,
        'edges_match': True,
        'differences': []
    }
    
    # Compare nodes
    nodes1 = set(structure1.get('nodes', {}).keys())
    nodes2 = set(structure2.get('nodes', {}).keys())
    
    if nodes1 != nodes2:
        comparison['nodes_match'] = False
        comparison['differences'].append({
            'type': 'nodes',
            'missing_in_first': list(nodes2 - nodes1),
            'missing_in_second': list(nodes1 - nodes2)
        })
    
    # Compare edges
    edges1 = [(e['source'], e['target']) for e in structure1.get('edges', [])]
    edges2 = [(e['source'], e['target']) for e in structure2.get('edges', [])]
    
    if set(edges1) != set(edges2):
        comparison['edges_match'] = False
        comparison['differences'].append({
            'type': 'edges',
            'edges_in_first_only': list(set(edges1) - set(edges2)),
            'edges_in_second_only': list(set(edges2) - set(edges1))
        })
    
    return comparison


# Example usage and test functions
def create_example_graph():
    """Create an example graph for testing"""
    from typing import Annotated
    from operator import add
    
    class State(TypedDict):
        foo: str
        bar: Annotated[list[str], add]
    
    def node_a(state: State):
        return {"foo": "a", "bar": ["a"]}
    
    def node_b(state: State):
        return {"foo": "b", "bar": ["b"]}
    
    # Build graph
    workflow = StateGraph(State)
    workflow.add_node("node_a", node_a)
    workflow.add_node("node_b", node_b)
    workflow.add_edge(START, "node_a")
    workflow.add_edge("node_a", "node_b")
    workflow.add_edge("node_b", END)
    
    return workflow, State, {"node_a": node_a, "node_b": node_b}


def test_serialization():
    """Test the serialization and deserialization functions"""
    # Create example graph
    builder, state_schema, node_functions = create_example_graph()
    
    print("Testing serialization...")
    
    # Test full serialization
    serialize_builder(builder, "test_graph_full", include_functions=True)
    print("✓ Full serialization completed")
    
    # Test metadata-only serialization
    serialize_builder(builder, "test_graph_metadata", metadata_only=True)
    print("✓ Metadata-only serialization completed")
    
    # Test deserialization
    restored_builder_full = deserialize_builder("test_graph_full.pkl")
    print("✓ Full deserialization completed")
    
    restored_builder_metadata = deserialize_builder(
        "test_graph_metadata.json", 
        state_schema=state_schema, 
        node_functions=node_functions
    )
    print("✓ Metadata-only deserialization completed")
    
    # Test compilation
    original_graph = builder.compile()
    restored_graph_full = restored_builder_full.compile()
    restored_graph_metadata = restored_builder_metadata.compile()
    
    print("✓ All graphs compiled successfully")
    
    # Compare structures
    orig_structure = extract_graph_structure(original_graph)
    restored_structure_full = extract_graph_structure(restored_graph_full)
    restored_structure_metadata = extract_graph_structure(restored_graph_metadata)
    
    comparison_full = compare_graph_structures(orig_structure, restored_structure_full)
    comparison_metadata = compare_graph_structures(orig_structure, restored_structure_metadata)
    
    print(f"Full restoration matches original: {comparison_full['nodes_match'] and comparison_full['edges_match']}")
    print(f"Metadata restoration matches original: {comparison_metadata['nodes_match'] and comparison_metadata['edges_match']}")
    
    return {
        'original': original_graph,
        'restored_full': restored_graph_full,
        'restored_metadata': restored_graph_metadata,
        'comparisons': {
            'full': comparison_full,
            'metadata': comparison_metadata
        }
    }


if __name__ == "__main__":
    # Run the test
    results = test_serialization()
    print(results)
    print("\nSerialization test completed!") 