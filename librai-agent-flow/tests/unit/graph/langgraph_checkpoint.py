from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import InMemorySaver
from typing import Annotated
from typing_extensions import TypedDict
from operator import add
from langgraph.graph.message import add_messages
import dill
from langchain_openai import ChatOpenAI
from typing import Dict, Any
from typing import Literal, Sequence, Annotated
import operator
from langchain_core.messages import HumanMessage, BaseMessage, SystemMessage

class GraphMetadata(TypedDict):
    """Metadata structure for serialized graphs"""
    nodes: Dict[str, Dict[str, Any]]
    edges: list[Dict[str, Any]]
    conditional_edges: list[Dict[str, Any]]
    entry_points: list[str]
    finish_points: list[str]
    state_schema_name: str
    state_schema_module: str

llm = ChatOpenAI(model="gpt-4-turbo-preview", api_key='***************************************************')

class AgentState(TypedDict):
    messages: Annotated[Sequence[BaseMessage], operator.add]
    user_message: str
    next: str
# Node: call the model with all messages so far
def call_model(state: AgentState):
    # always build a non-empty list for the API
    msgs: list[BaseMessage] = state["messages"] or [SystemMessage(content="You are a helpful assistant.")]
    msgs.append(HumanMessage(content=state["user_message"]))
    response = model.invoke(msgs)
    return {
        "messages": msgs + [response],
        "next": "__end__"
    }





graph = StateGraph(AgentState)
graph.add_node("agent", call_model)
graph.add_edge(START, "agent")
graph.add_edge("agent", END)


workflow = graph
checkpointer = InMemorySaver()
graph = workflow.compile(checkpointer=checkpointer)
graph.get_graph().draw_mermaid()


# with open("./agent_state.class", 'rb') as fr:
#     content = fr.read()
#     state = dill.loads(content)
#     print(f"state {state}")