# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import os
import pytest
import requests
from unittest.mock import patch, MagicMock
from src.rag.dify_knowledge import DifyKnowledgeProvider, parse_uri


# Dummy classes to mock dependencies
class DummyResource:
    def __init__(self, uri, title="", description=""):
        self.uri = uri
        self.title = title
        self.description = description


class DummyChunk:
    def __init__(self, content, similarity):
        self.content = content
        self.similarity = similarity


class DummyDocument:
    def __init__(self, id, title, chunks=None):
        self.id = id
        self.title = title
        self.chunks = chunks or []


# Patch imports in dify_knowledge.py to use dummy classes
@pytest.fixture(autouse=True)
def patch_imports(monkeypatch):
    import src.rag.dify_knowledge as dify_knowledge

    dify_knowledge.Resource = DummyResource
    dify_knowledge.Chunk = DummyChunk
    dify_knowledge.Document = DummyDocument
    yield


def test_parse_uri_valid():
    uri = "rag://dataset/5991f2f6-4727-44c6-a9ca-9542a22aac69"
    dataset_id, document_id = parse_uri(uri)
    assert dataset_id == "5991f2f6-4727-44c6-a9ca-9542a22aac69"
    assert document_id == ""


def test_parse_uri_invalid():
    with pytest.raises(ValueError):
        parse_uri("http://dataset/5991f2f6-4727-44c6-a9ca-9542a22aac69")


def test_init_env_vars(monkeypatch):
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_URL", "http://api")
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_KEY", "key")
    monkeypatch.delenv("DIFY_KNOWLEDGE_PAGE_SIZE", raising=False)
    provider = DifyKnowledgeProvider()
    assert provider.api_url == "http://api"
    assert provider.api_key == "key"
    assert provider.page_size == 10


def test_init_page_size(monkeypatch):
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_URL", "http://api")
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_KEY", "key")
    monkeypatch.setenv("DIFY_KNOWLEDGE_PAGE_SIZE", "5")
    provider = DifyKnowledgeProvider()
    assert provider.page_size == 5


def test_init_missing_env(monkeypatch):
    monkeypatch.delenv("DIFY_KNOWLEDGE_API_URL", raising=False)
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_KEY", "key")
    with pytest.raises(ValueError):
        DifyKnowledgeProvider()
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_URL", "http://api")
    monkeypatch.delenv("DIFY_KNOWLEDGE_API_KEY", raising=False)
    with pytest.raises(ValueError):
        DifyKnowledgeProvider()


@patch("src.rag.dify_knowledge.requests.post")
def test_query_dataset_success(mock_post, monkeypatch):
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_URL", "http://api")
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_KEY", "key")
    provider = DifyKnowledgeProvider()
    
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "records": [
            {
                "segment": {
                    "document": {
                        "id": "doc123",
                        "name": "Document Title"
                    },
                    "content": "chunk content 1"
                },
                "score": 0.9
            },
            {
                "segment": {
                    "document": {
                        "id": "doc123",
                        "name": "Document Title"
                    },
                    "content": "chunk content 2"
                },
                "score": 0.8
            }
        ]
    }
    mock_post.return_value = mock_response
    
    docs = provider._query_dataset("dataset123", "test query")
    
    assert len(docs) == 1
    assert "doc123" in docs
    assert docs["doc123"].id == "doc123"
    assert docs["doc123"].title == "Document Title"
    assert len(docs["doc123"].chunks) == 2
    assert docs["doc123"].chunks[0].content == "chunk content 1"
    assert docs["doc123"].chunks[0].similarity == 0.9
    assert docs["doc123"].chunks[1].content == "chunk content 2"
    assert docs["doc123"].chunks[1].similarity == 0.8


@patch("src.rag.dify_knowledge.requests.post")
def test_query_dataset_error(mock_post, monkeypatch):
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_URL", "http://api")
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_KEY", "key")
    provider = DifyKnowledgeProvider()
    
    mock_response = MagicMock()
    mock_response.status_code = 400
    mock_response.text = "error"
    mock_post.return_value = mock_response
    
    with pytest.raises(Exception):
        provider._query_dataset("dataset123", "test query")


@patch("src.rag.dify_knowledge.requests.post")
def test_query_relevant_documents_success(mock_post, monkeypatch):
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_URL", "http://api")
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_KEY", "key")
    provider = DifyKnowledgeProvider()
    
    resource = DummyResource("rag://dataset/123#doc456")
    
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "records": [
            {
                "segment": {
                    "document": {
                        "id": "doc456",
                        "name": "Doc Title"
                    },
                    "content": "chunk text"
                },
                "score": 0.9
            }
        ]
    }
    mock_post.return_value = mock_response
    
    docs = provider.query_relevant_documents("query", [resource])
    
    assert len(docs) == 1
    assert docs[0].id == "doc456"
    assert docs[0].title == "Doc Title"
    assert len(docs[0].chunks) == 1
    assert docs[0].chunks[0].content == "chunk text"
    assert docs[0].chunks[0].similarity == 0.9


@patch("src.rag.dify_knowledge.requests.post")
def test_query_relevant_documents_multiple_resources(mock_post, monkeypatch):
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_URL", "http://api")
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_KEY", "key")
    provider = DifyKnowledgeProvider()
    
    resource1 = DummyResource("rag://dataset/123#doc456")
    resource2 = DummyResource("rag://dataset/456#doc789")
    
    def mock_response_side_effect(*args, **kwargs):
        response = MagicMock()
        response.status_code = 200
        # Return different document IDs for each dataset call
        if "/datasets/123/" in args[0]:
            response.json.return_value = {
                "records": [
                    {
                        "segment": {
                            "document": {
                                "id": "doc456",
                                "name": "Doc Title 1"
                            },
                            "content": "chunk text 1"
                        },
                        "score": 0.9
                    }
                ]
            }
        else:
            response.json.return_value = {
                "records": [
                    {
                        "segment": {
                            "document": {
                                "id": "doc789",
                                "name": "Doc Title 2"
                            },
                            "content": "chunk text 2"
                        },
                        "score": 0.8
                    }
                ]
            }
        return response
    
    mock_post.side_effect = mock_response_side_effect
    
    docs = provider.query_relevant_documents("query", [resource1, resource2])
    
    # Should be called twice, once for each resource
    assert mock_post.call_count == 2
    assert len(docs) == 2


@patch("src.rag.dify_knowledge.requests.get")
def test_list_resources_success(mock_get, monkeypatch):
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_URL", "http://api")
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_KEY", "key")
    provider = DifyKnowledgeProvider()
    
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "data": [
            {"id": "123", "name": "Dataset1", "description": "desc1"},
            {"id": "456", "name": "Dataset2", "description": "desc2"},
        ]
    }
    mock_get.return_value = mock_response
    
    resources = provider.list_resources()
    
    assert len(resources) == 2
    assert resources[0].uri == "rag://dataset/123"
    assert resources[0].title == "Dataset1"
    assert resources[0].description == "desc1"
    assert resources[1].uri == "rag://dataset/456"
    assert resources[1].title == "Dataset2"
    assert resources[1].description == "desc2"


@patch("src.rag.dify_knowledge.requests.get")
def test_list_resources_with_query(mock_get, monkeypatch):
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_URL", "http://api")
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_KEY", "key")
    provider = DifyKnowledgeProvider()
    
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "data": [
            {"id": "123", "name": "Dataset1", "description": "desc1"},
        ]
    }
    mock_get.return_value = mock_response
    
    resources = provider.list_resources("search query")
    
    # Check that the query parameter was passed correctly
    mock_get.assert_called_once()
    args, kwargs = mock_get.call_args
    assert "keywords" in kwargs["params"]
    assert kwargs["params"]["keywords"] == "search query"
    assert len(resources) == 1


@patch("src.rag.dify_knowledge.requests.get")
def test_list_resources_error(mock_get, monkeypatch):
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_URL", "http://api")
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_KEY", "key")
    provider = DifyKnowledgeProvider()
    
    mock_response = MagicMock()
    mock_response.status_code = 500
    mock_response.text = "fail"
    mock_get.return_value = mock_response
    
    with pytest.raises(Exception):
        provider.list_resources()


@patch("src.rag.dify_knowledge.requests.get")
def test_list_resources_empty_data(mock_get, monkeypatch):
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_URL", "http://api")
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_KEY", "key")
    provider = DifyKnowledgeProvider()
    
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {"data": []}
    mock_get.return_value = mock_response
    
    resources = provider.list_resources()
    
    assert len(resources) == 0


def test_query_relevant_documents_empty_resources(monkeypatch):
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_URL", "http://api")
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_KEY", "key")
    provider = DifyKnowledgeProvider()
    
    docs = provider.query_relevant_documents("query", [])
    
    assert len(docs) == 0


@patch("src.rag.dify_knowledge.requests.post")
def test_query_dataset_missing_score(mock_post, monkeypatch):
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_URL", "http://api")
    monkeypatch.setenv("DIFY_KNOWLEDGE_API_KEY", "key")
    provider = DifyKnowledgeProvider()
    
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "records": [
            {
                "segment": {
                    "document": {
                        "id": "doc123",
                        "name": "Document Title"
                    },
                    "content": "chunk content"
                }
                # No score field
            }
        ]
    }
    mock_post.return_value = mock_response
    
    docs = provider._query_dataset("dataset123", "test query")
    
    assert len(docs) == 1
    assert docs["doc123"].chunks[0].similarity == 0  # Default score
