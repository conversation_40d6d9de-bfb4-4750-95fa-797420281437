# Author: <PERSON><PERSON>

import pytest
from src.utils.step_quality_checker import check_step_errors

def test_error_detection():
    """测试错误检测功能"""
    checker = check_step_errors
    
    # 测试正常情况
    assert checker("This is a normal execution result with good content.") == False
    
    # 测试错误关键词
    assert checker("An error occurred while processing the request") == True
    assert checker("The operation failed to complete") == True
    assert checker("Connection timeout occurred") == True
    assert checker("抱歉，无法完成请求") == True
    assert checker("执行失败，请重试") == True
    
    # 测试内容过短
    assert checker("Short") == True
    assert checker("") == True
    assert checker(None) == True
    
    # 测试正常长度但包含错误信息
    assert checker("This is a long execution result but it contains an error message that should be detected") == True
    
    # 测试正常长度且无错误
    assert checker("This is a properly formatted execution result with sufficient content length and no error indicators") == False

def test_case_insensitive():
    """测试大小写不敏感"""
    assert check_step_errors("ERROR in the system") == True
    assert check_step_errors("error in the system") == True
    assert check_step_errors("Error in the system") == True

def test_multiple_errors():
    """测试多个错误关键词"""
    result = "Multiple issues: failed to connect, timeout occurred, and permission denied"
    assert check_step_errors(result) == True

def test_edge_cases():
    """测试边界情况"""
    # 刚好30个字符（边界值）
    short_result = "This is exactly thirty chars!"
    assert check_step_errors(short_result) == False
    
    # 29个字符（应该检测为过短）
    very_short_result = "This is twenty-nine chars"
    assert check_step_errors(very_short_result) == True 