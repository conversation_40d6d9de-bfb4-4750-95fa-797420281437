# Author: <PERSON><PERSON>
# Quick test script for researcher tools

import asyncio
import os
import sys
# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

def test_individual_tools():
    """快速测试各个工具"""
    print("🔧 快速测试工具功能...")
    print("=" * 50)
    
    # 测试地理编码工具
    print("\n📍 测试地理编码工具:")
    try:
        from src.tools.geocode import geocode_tool
        result = geocode_tool.invoke({"address": "北京市天安门广场"})
        if "Coordinates:" in result:
            print("✅ 地理编码工具工作正常")
            print(f"   结果: {result.split('Coordinates:')[1].split('\n')[0].strip()}")
        else:
            print("⚠️  地理编码工具返回异常结果")
            print(f"   结果: {result[:100]}...")
    except Exception as e:
        print(f"❌ 地理编码工具测试失败: {e}")
    
    # 测试时区转换工具
    print("\n⏰ 测试时区转换工具:")
    try:
        from src.tools.timeconvert import time_convert_tool
        result = time_convert_tool.invoke({
            "from_timezone": "Asia/Shanghai",
            "to_timezone": "America/New_York", 
            "time_str": "12:00:00",
            "date_str": "2024-01-01"
        })
        if "Time Conversion:" in result:
            print("✅ 时区转换工具工作正常")
            print(f"   结果: {result.split('To:')[1].split('\n')[0].strip()}")
        else:
            print("⚠️  时区转换工具返回异常结果")
            print(f"   结果: {result[:100]}...")
    except Exception as e:
        print(f"❌ 时区转换工具测试失败: {e}")
    
    # 测试天气查询工具
    print("\n🌤️ 测试天气查询工具:")
    try:
        from src.tools.weather import weather_by_address_tool
        result = weather_by_address_tool.invoke({"address": "上海市", "hours": 24})
        if "Weather Forecast" in result:
            print("✅ 天气查询工具工作正常")
            print(f"   结果: {result.split('Temperature:')[1].split('\n')[0].strip()}")
        else:
            print("⚠️  天气查询工具返回异常结果")
            print(f"   结果: {result[:100]}...")
    except Exception as e:
        print(f"❌ 天气查询工具测试失败: {e}")
    
    # 测试货币转换工具
    print("\n💰 测试货币转换工具:")
    try:
        from src.tools.currency import currency_convert_tool
        result = currency_convert_tool.invoke({
            "amount": 100,
            "from_currency": "USD",
            "to_currency": "CNY"
        })
        if "Currency Conversion:" in result:
            print("✅ 货币转换工具工作正常")
            print(f"   结果: {result.split('Converted Amount:')[1].split('\n')[0].strip()}")
        else:
            print("⚠️  货币转换工具返回异常结果")
            print(f"   结果: {result[:100]}...")
    except Exception as e:
        print(f"❌ 货币转换工具测试失败: {e}")
    
    # 测试时区信息工具
    print("\n🌍 测试时区信息工具:")
    try:
        from src.tools.timeconvert import timezone_info_tool
        result = timezone_info_tool.invoke({
            "latitude": 39.9042, 
            "longitude": 116.4074
        })
        if "Timezone Information:" in result:
            print("✅ 时区信息工具工作正常")
            print(f"   结果: {result.split('Timezone Information:')[1].split('\n')[0].strip()}")
        else:
            print("⚠️  时区信息工具返回异常结果")
            print(f"   结果: {result[:100]}...")
    except Exception as e:
        print(f"❌ 时区信息工具测试失败: {e}")
    
    # 测试天气预报工具
    print("\n🌦️ 测试天气预报工具:")
    try:
        from src.tools.weather import weather_forecast_tool
        result = weather_forecast_tool.invoke({
            "latitude": 39.9042, 
            "longitude": 116.4074, 
            "hours": 24
        })
        if "Weather Forecast" in result:
            print("✅ 天气预报工具工作正常")
            print(f"   结果: {result.split('Temperature:')[1].split('\n')[0].strip()}")
        else:
            print("⚠️  天气预报工具返回异常结果")
            print(f"   结果: {result[:100]}...")
    except Exception as e:
        print(f"❌ 天气预报工具测试失败: {e}")
    
    # 测试支持的货币列表工具
    print("\n💵 测试支持的货币列表工具:")
    try:
        from src.tools.currency import supported_currencies_tool
        result = supported_currencies_tool.invoke({})
        if "Supported Currencies:" in result:
            print("✅ 支持的货币列表工具工作正常")
            print(f"   结果: {result.split('Supported Currencies:')[1].split('\n')[0].strip()}")
        else:
            print("⚠️  支持的货币列表工具返回异常结果")
            print(f"   结果: {result[:100]}...")
    except Exception as e:
        print(f"❌ 支持的货币列表工具测试失败: {e}")
    
    # 测试SerpAPI工具
    print("\n🎫 测试SerpAPI工具:")
    
    # 测试谷歌活动搜索工具
    print("\n   📅 测试谷歌活动搜索工具:")
    try:
        from src.tools.serpapi_tools import google_events_search
        result = google_events_search.invoke({
            "query": "events",
            "location": "New York",
            "num_results": 3
        })
        if '"search_metadata"' in result:
            print("✅ 谷歌活动搜索工具工作正常")
            print(f"   结果: 找到活动数据")
        else:
            print("⚠️  谷歌活动搜索工具返回异常结果")
            print(f"   结果: {result[:100]}...")
    except Exception as e:
        print(f"❌ 谷歌活动搜索工具测试失败: {e}")
    
    # 测试谷歌航班搜索工具
    print("\n   ✈️ 测试谷歌航班搜索工具:")
    try:
        from src.tools.serpapi_tools import google_flights_search
        # 使用未来的日期
        from datetime import datetime, timedelta
        future_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
        
        result = google_flights_search.invoke({
            "departure_id": "JFK",
            "arrival_id": "LAX",
            "outbound_date": future_date,
            "adults": 1
        })
        if '"search_metadata"' in result:
            print("✅ 谷歌航班搜索工具工作正常")
            print(f"   结果: 找到航班数据")
        else:
            print("⚠️  谷歌航班搜索工具返回异常结果")
            print(f"   结果: {result[:100]}...")
    except Exception as e:
        print(f"❌ 谷歌航班搜索工具测试失败: {e}")
    
    # 测试谷歌酒店搜索工具
    print("\n   🏨 测试谷歌酒店搜索工具:")
    try:
        from src.tools.serpapi_tools import google_hotels_search
        # 使用未来的日期
        from datetime import datetime, timedelta
        check_in_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
        check_out_date = (datetime.now() + timedelta(days=33)).strftime("%Y-%m-%d")
        
        result = google_hotels_search.invoke({
            "q": "Tokyo, Japan",
            "check_in_date": check_in_date,
            "check_out_date": check_out_date,
            "adults": 2
        })
        if '"search_metadata"' in result:
            print("✅ 谷歌酒店搜索工具工作正常")
            print(f"   结果: 找到酒店数据")
        else:
            print("⚠️  谷歌酒店搜索工具返回异常结果")
            print(f"   结果: {result[:100]}...")
    except Exception as e:
        print(f"❌ 谷歌酒店搜索工具测试失败: {e}")
    
    # 测试谷歌本地搜索工具
    print("\n   🏪 测试谷歌本地搜索工具:")
    try:
        from src.tools.serpapi_tools import google_local_search
        result = google_local_search.invoke({
            "query": "coffee shops",
            "location": "San Francisco, CA",
            "num": 3
        })
        if '"search_metadata"' in result:
            print("✅ 谷歌本地搜索工具工作正常")
            print(f"   结果: 找到本地商家数据")
        else:
            print("⚠️  谷歌本地搜索工具返回异常结果")
            print(f"   结果: {result[:100]}...")
    except Exception as e:
        print(f"❌ 谷歌本地搜索工具测试失败: {e}")

def test_researcher_node_simple():
    """简单测试researcher节点"""
    print("\n🧪 测试researcher节点...")
    print("=" * 50)
    
    try:
        from src.graph.nodes import researcher_node
        from src.graph.types import State
        from src.prompts.planner_model import Plan, Step, StepType
        
        # 创建一个简单的测试计划 - 使用SerpAPI工具
        test_step = Step(
            need_search=True,
            title="查询纽约的音乐活动",
            description="使用SerpAPI工具查询纽约地区的音乐活动信息",
            step_type=StepType.RESEARCH,
            execution_res=None
        )
        
        test_plan = Plan(
            locale="zh-CN",
            has_enough_context=False,
            thought="需要查询纽约地区的音乐活动信息",
            title="音乐活动查询计划",
            steps=[test_step]
        )
        
        # 创建简单的测试状态
        state = State(
            research_topic="请查询纽约地区的音乐活动信息",
            locale="zh-CN",
            messages=[],
            observations=[],
            current_plan=test_plan,
            plan_iterations=0,
            auto_accepted_plan=True,
            enable_background_investigation=False,
            resources=[],
            background_investigation_results=None,
            final_report=None
        )
        
        # 创建模拟配置 - 使用字典格式而不是类实例
        config = {
            "configurable": {
                "max_search_results": 5,
                "max_plan_iterations": 1,
                "max_step_num": 3,
                "enable_background_investigation": False,
                "resources": [],
                "mcp_settings": None
            }
        }
        
        print("🔄 正在执行researcher节点...")
        result = asyncio.run(researcher_node(state, config))
        
        if result and hasattr(result, 'update'):
            observations = result.update.get('observations', [])
            if observations:
                print("✅ researcher节点执行成功!")
                print(f"📝 生成了 {len(observations)} 个观察结果")
                print(f"📄 结果预览: {observations[-1][:700]}...")
            else:
                print("⚠️  researcher节点执行完成但未生成观察结果")
        else:
            print("❌ researcher节点执行失败")
            
    except Exception as e:
        print(f"❌ researcher节点测试失败: {e}")
        import traceback
        traceback.print_exc()

def check_environment():
    """检查环境配置"""
    print("📋 检查环境变量配置:")
    print("=" * 50)
    
    env_vars = {
        "GOOGLE_API_KEY": "Google API密钥（地理编码、时区、天气）",
        "EXCHANGE_RATE_API_KEY": "汇率API密钥（货币转换）",
        "TAVILY_API_KEY": "Tavily搜索API密钥（网络搜索）",
        "SERPAPI_API_KEY": "SerpAPI密钥（活动、航班、酒店、本地搜索）"
    }
    
    all_configured = True
    for var, description in env_vars.items():
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: 已配置")
        else:
            print(f"⚠️  {var}: 未配置 - {description}")
            all_configured = False
    
    if not all_configured:
        print("\n💡 提示: 某些工具可能需要API密钥才能正常工作")
        print("   请检查.env文件中的配置")
    
    return all_configured

def main():
    """主函数"""
    print("🚀 Researcher工具快速测试")
    print("=" * 50)
    
    # 检查环境
    check_environment()
    
    # 测试单独工具
    test_individual_tools()
    
    # 测试researcher节点
    test_researcher_node_simple()
    
    print("\n" + "=" * 50)
    print("🎉 快速测试完成!")

if __name__ == "__main__":
    main() 