# Author: <PERSON><PERSON>
"""
Planner-Researcher工作流测试
验证planner节点能输出current_plan，researcher节点能消费current_plan并生成observations，
以及整个工作流的执行流程。
"""

import asyncio
import json
from unittest.mock import patch, MagicMock, AsyncMock
from langgraph.graph import StateGraph, START, END
from langgraph.types import Command
from src.graph.nodes import planner_node, researcher_node
from src.config.configuration import Configuration
from src.prompts.planner_model import Plan, Step, StepType

# 常量定义
DEFAULT_LOCALE = "zh-CN"
RESEARCH_TOPIC = "查询北京天气"
PLAN_STEPS = [
    {"title": "查天气", "description": "查北京天气", "step_type": "research", "need_search": True}
]

class SimpleMockLLM:
    """简单的Mock LLM类"""
    def invoke(self, messages):
        mock_response = MagicMock()
        # 返回一个固定的plan
        plan_data = {
            "has_enough_context": False,
            "title": "北京天气计划",
            "thought": "查北京天气",
            "steps": PLAN_STEPS,
            "locale": DEFAULT_LOCALE
        }
        mock_response.content = json.dumps(plan_data, ensure_ascii=False)
        mock_response.model_dump_json = lambda *a, **k: mock_response.content
        return mock_response
    
    def bind_tools(self, tools):
        return self
    
    def with_structured_output(self, *a, **k):
        return self

def create_state_dict():
    """创建初始状态字典"""
    return {
        "research_topic": RESEARCH_TOPIC,
        "locale": DEFAULT_LOCALE,
        "messages": [],
        "observations": [],
        "current_plan": None,
        "plan_iterations": 0,
        "auto_accepted_plan": True,
        "enable_background_investigation": False,
        "resources": [],
        "background_investigation_results": None,
        "final_report": None
    }

def create_plan():
    """创建Plan对象"""
    return Plan(
        locale=DEFAULT_LOCALE,
        has_enough_context=False,
        thought="查北京天气",
        title="北京天气计划",
        steps=[Step(**step) for step in PLAN_STEPS]
    )

def build_direct_workflow():
    """构建直接的planner-researcher工作流"""
    # 创建状态图
    workflow = StateGraph(dict)
    
    # 添加节点
    workflow.add_node("planner", planner_node)
    workflow.add_node("researcher", researcher_node)
    
    # 添加边 - 直接连接planner到researcher
    workflow.add_edge(START, "planner")
    workflow.add_edge("planner", "researcher")
    workflow.add_edge("researcher", END)
    
    # 编译工作流
    return workflow.compile()

def build_conditional_workflow():
    """构建条件分支的planner-researcher工作流"""
    # 创建状态图
    workflow = StateGraph(dict)
    
    # 添加节点
    workflow.add_node("planner", planner_node)
    workflow.add_node("researcher", researcher_node)
    
    # 添加边
    workflow.add_edge(START, "planner")
    
    # 添加条件边 - 根据planner的结果决定下一步
    def planner_decision(state):
        """根据planner结果决定下一步"""
        current_plan = state.get("current_plan")
        if current_plan and hasattr(current_plan, 'has_enough_context') and current_plan.has_enough_context:
            return "researcher"
        else:
            return "researcher"  # 简化逻辑，总是去researcher
    
    workflow.add_conditional_edges(
        "planner",
        planner_decision,
        ["researcher"]
    )
    workflow.add_edge("researcher", END)
    
    # 编译工作流
    return workflow.compile()

async def test_direct_workflow():
    """测试直接连接的工作流"""
    print("\n🚀 测试直接Planner-Researcher工作流\n" + "="*50)
    
    # 创建初始状态
    initial_state = create_state_dict()
    
    # 构建工作流
    workflow = build_direct_workflow()
    
    # Mock所有必要的依赖
    with patch('src.graph.nodes.get_llm_by_type') as mock_llm, \
         patch('src.graph.nodes.Configuration.from_runnable_config') as mock_config, \
         patch('src.graph.nodes.apply_prompt_template') as mock_template, \
         patch('src.graph.nodes.AGENT_LLM_MAP', {"planner": "basic"}), \
         patch('src.graph.nodes.repair_json_output') as mock_repair, \
         patch('src.prompts.planner_model.Plan.model_validate', side_effect=lambda x: create_plan()), \
         patch('src.graph.nodes.create_agent') as mock_create_agent:
        
        # 设置Mock返回值
        mock_llm.return_value = SimpleMockLLM()
        mock_config.return_value = Configuration(
            max_search_results=5, 
            max_plan_iterations=2, 
            max_step_num=3, 
            resources=[], 
            mcp_settings=None, 
            enable_deep_thinking=False
        )
        mock_template.return_value = [{"role": "user", "content": "plan this"}]
        mock_repair.return_value = SimpleMockLLM().invoke([]).content
        
        # Mock researcher agent
        mock_agent = AsyncMock()
        mock_agent.ainvoke.return_value = {"messages": [MagicMock(content="北京天气晴朗，温度25度")]}
        mock_create_agent.return_value = mock_agent
        
        # 执行工作流
        try:
            result = await workflow.ainvoke(initial_state)
            print("✅ 直接工作流执行成功")
            print(f"📊 最终状态: {result}")
            
            # 验证结果
            assert "current_plan" in result, "工作流应该生成current_plan"
            assert "observations" in result, "工作流应该生成observations"
            assert len(result["observations"]) > 0, "应该有关键词观察结果"
            
            print("✅ 直接工作流验证通过")
            return True
            
        except Exception as e:
            print(f"❌ 直接工作流执行失败: {e}")
            return False

async def test_conditional_workflow():
    """测试条件分支工作流"""
    print("\n🚀 测试条件分支Planner-Researcher工作流\n" + "="*50)
    
    # 创建初始状态
    initial_state = create_state_dict()
    
    # 构建条件工作流
    workflow = build_conditional_workflow()
    
    # Mock所有必要的依赖
    with patch('src.graph.nodes.get_llm_by_type') as mock_llm, \
         patch('src.graph.nodes.Configuration.from_runnable_config') as mock_config, \
         patch('src.graph.nodes.apply_prompt_template') as mock_template, \
         patch('src.graph.nodes.AGENT_LLM_MAP', {"planner": "basic"}), \
         patch('src.graph.nodes.repair_json_output') as mock_repair, \
         patch('src.prompts.planner_model.Plan.model_validate', side_effect=lambda x: create_plan()), \
         patch('src.graph.nodes.create_agent') as mock_create_agent:
        
        # 设置Mock返回值
        mock_llm.return_value = SimpleMockLLM()
        mock_config.return_value = Configuration(
            max_search_results=5, 
            max_plan_iterations=2, 
            max_step_num=3, 
            resources=[], 
            mcp_settings=None, 
            enable_deep_thinking=False
        )
        mock_template.return_value = [{"role": "user", "content": "plan this"}]
        mock_repair.return_value = SimpleMockLLM().invoke([]).content
        
        # Mock researcher agent
        mock_agent = AsyncMock()
        mock_agent.ainvoke.return_value = {"messages": [MagicMock(content="北京天气晴朗，温度25度")]}
        mock_create_agent.return_value = mock_agent
        
        # 执行工作流
        try:
            result = await workflow.ainvoke(initial_state)
            print("✅ 条件分支工作流执行成功")
            print(f"📊 最终状态: {result}")
            
            # 验证结果
            assert "current_plan" in result, "工作流应该生成current_plan"
            assert "observations" in result, "工作流应该生成observations"
            
            print("✅ 条件分支工作流验证通过")
            return True
            
        except Exception as e:
            print(f"❌ 条件分支工作流执行失败: {e}")
            return False

async def test_workflow_with_multiple_steps():
    """测试包含多个步骤的工作流"""
    print("\n🚀 测试多步骤Planner-Researcher工作流\n" + "="*50)
    
    # 创建包含多个步骤的计划
    multi_step_plan = Plan(
        locale=DEFAULT_LOCALE,
        has_enough_context=False,
        thought="查北京天气和交通",
        title="北京天气和交通计划",
        steps=[
            Step(title="查天气", description="查北京天气", step_type=StepType.RESEARCH, need_search=True),
            Step(title="查交通", description="查北京交通状况", step_type=StepType.RESEARCH, need_search=True)
        ]
    )
    
    # 创建初始状态
    initial_state = create_state_dict()
    initial_state["current_plan"] = multi_step_plan
    
    # 构建工作流
    workflow = build_direct_workflow()
    
    # Mock所有必要的依赖
    with patch('src.graph.nodes.get_llm_by_type') as mock_llm, \
         patch('src.graph.nodes.Configuration.from_runnable_config') as mock_config, \
         patch('src.graph.nodes.apply_prompt_template') as mock_template, \
         patch('src.graph.nodes.AGENT_LLM_MAP', {"planner": "basic"}), \
         patch('src.graph.nodes.repair_json_output') as mock_repair, \
         patch('src.prompts.planner_model.Plan.model_validate', side_effect=lambda x: multi_step_plan), \
         patch('src.graph.nodes.create_agent') as mock_create_agent:
        
        # 设置Mock返回值
        mock_llm.return_value = SimpleMockLLM()
        mock_config.return_value = Configuration(
            max_search_results=5, 
            max_plan_iterations=2, 
            max_step_num=3, 
            resources=[], 
            mcp_settings=None, 
            enable_deep_thinking=False
        )
        mock_template.return_value = [{"role": "user", "content": "plan this"}]
        mock_repair.return_value = SimpleMockLLM().invoke([]).content
        
        # Mock researcher agent - 返回不同的结果
        mock_agent = AsyncMock()
        mock_agent.ainvoke.side_effect = [
            {"messages": [MagicMock(content="北京天气晴朗，温度25度")]},
            {"messages": [MagicMock(content="北京交通状况良好，地铁运行正常")]}
        ]
        mock_create_agent.return_value = mock_agent
        
        # 执行工作流
        try:
            result = await workflow.ainvoke(initial_state)
            print("✅ 多步骤工作流执行成功")
            print(f"📊 最终状态: {result}")
            
            # 验证结果
            assert "current_plan" in result, "工作流应该生成current_plan"
            assert "observations" in result, "工作流应该生成observations"
            assert len(result["observations"]) >= 1, "应该有关键词观察结果"
            
            print("✅ 多步骤工作流验证通过")
            return True
            
        except Exception as e:
            print(f"❌ 多步骤工作流执行失败: {e}")
            return False

async def test_workflow_state_transition():
    """测试工作流状态转换"""
    print("\n🚀 测试工作流状态转换\n" + "="*50)
    
    # 创建初始状态
    initial_state = create_state_dict()
    
    # 构建工作流
    workflow = build_direct_workflow()
    
    # Mock所有必要的依赖
    with patch('src.graph.nodes.get_llm_by_type') as mock_llm, \
         patch('src.graph.nodes.Configuration.from_runnable_config') as mock_config, \
         patch('src.graph.nodes.apply_prompt_template') as mock_template, \
         patch('src.graph.nodes.AGENT_LLM_MAP', {"planner": "basic"}), \
         patch('src.graph.nodes.repair_json_output') as mock_repair, \
         patch('src.prompts.planner_model.Plan.model_validate', side_effect=lambda x: create_plan()), \
         patch('src.graph.nodes.create_agent') as mock_create_agent:
        
        # 设置Mock返回值
        mock_llm.return_value = SimpleMockLLM()
        mock_config.return_value = Configuration(
            max_search_results=5, 
            max_plan_iterations=2, 
            max_step_num=3, 
            resources=[], 
            mcp_settings=None, 
            enable_deep_thinking=False
        )
        mock_template.return_value = [{"role": "user", "content": "plan this"}]
        mock_repair.return_value = SimpleMockLLM().invoke([]).content
        
        # Mock researcher agent
        mock_agent = AsyncMock()
        mock_agent.ainvoke.return_value = {"messages": [MagicMock(content="北京天气晴朗，温度25度")]}
        mock_create_agent.return_value = mock_agent
        
        # 执行工作流并跟踪状态变化
        try:
            # 使用stream来跟踪状态变化
            async for event in workflow.astream(initial_state):
                if "planner" in event:
                    print(f"📝 Planner节点执行完成: {event['planner']}")
                if "researcher" in event:
                    print(f"🔍 Researcher节点执行完成: {event['researcher']}")
            
            print("✅ 工作流状态转换测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 工作流状态转换测试失败: {e}")
            return False

async def main():
    """主测试函数"""
    print("🎯 Planner-Researcher工作流测试开始\n" + "="*60)
    
    # 运行所有测试
    test_results = []
    
    # 测试1: 直接工作流
    result1 = await test_direct_workflow()
    test_results.append(("直接工作流", result1))
    
    # 测试2: 条件分支工作流
    result2 = await test_conditional_workflow()
    test_results.append(("条件分支工作流", result2))
    
    # 测试3: 多步骤工作流
    result3 = await test_workflow_with_multiple_steps()
    test_results.append(("多步骤工作流", result3))
    
    # 测试4: 状态转换测试
    result4 = await test_workflow_state_transition()
    test_results.append(("状态转换测试", result4))
    
    # 输出测试结果
    print("\n📋 测试结果汇总\n" + "="*40)
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    # 计算成功率
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过 ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("🎉 所有工作流测试通过！")
    else:
        print("⚠️  部分测试失败，请检查相关代码")

if __name__ == "__main__":
    asyncio.run(main()) 