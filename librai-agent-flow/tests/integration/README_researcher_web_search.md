# Researcher Web Search 测试

## 概述

这个测试文件 `test_researcher_web_search.py` 用于测试researcher节点在调用web_search工具时的输入输出行为，复现特定的测试日志场景。

## 测试场景

测试复现以下日志场景：

```
2025-06-30 07:52:38,795 - src.graph.nodes - INFO - Researcher tools: [LoggedTavilySearchResultsWithImages(name='web_search', max_results=3, include_raw_content=True, include_images=True, api_wrapper=EnhancedTavilySearchAPIWrapper(tavily_api_key=SecretStr('**********')), include_image_descriptions=True), StructuredTool(name='crawl_tool', description='Use this to crawl a url and get a readable content in markdown format.', args_schema=<class 'langchain_core.utils.pydantic.crawl_tool'>, func=<function crawl_tool at 0x7f08ed6c7a60>)]

2025-06-30 07:52:38,803 - src.graph.nodes - INFO - Executing step: Research Average Lifespan of Tesla Batteries, agent: researcher

2025-06-30 07:52:38,803 - src.graph.nodes - INFO - Recursion limit set to: 25

2025-06-30 07:52:38,803 - src.graph.nodes - INFO - Agent input: {'messages': [HumanMessage(content='# Current Task\n\n## Title\n\nResearch Average Lifespan of Tesla Batteries\n\n## Description\n\nCollect data on the average lifespan of Tesla batteries. This includes warranty information, average mileage before significant degradation, user testimonials, and any variations based on model or usage. Obtain data from Tesla's official resources, industry reports, and credible automotive reviews.\n\n## Locale\n\nen-US', additional_kwargs={}, response_metadata={}), HumanMessage(content='IMPORTANT: DO NOT include inline citations in the text. Instead, track all sources and include a References section at the end using link reference format. Include an empty line between each citation for better readability. Use this format for each reference:\n- [Source Title](URL)\n\n- [Another Source](URL)', additional_kwargs={}, response_metadata={}, name='system')]}
```

## 测试内容

### 1. 工具设置测试 (`test_researcher_node_tools_setup`)
- 验证researcher节点正确创建web_search工具
- 检查工具参数配置（max_results=3, include_raw_content=True等）
- 验证工具列表包含预期的工具

### 2. Agent输入构建测试 (`test_agent_input_construction`)
- 验证agent输入消息的正确构建
- 检查任务标题、描述和locale信息
- 验证递归限制设置

### 3. Web Search工具调用测试 (`test_web_search_tool_invocation`)
- 测试web_search工具的异步调用
- 验证搜索结果的格式和内容
- 检查页面和图片结果的正确处理

### 4. 完整工作流程测试 (`test_full_researcher_workflow`)
- 测试完整的researcher节点执行流程
- 验证工具链的集成
- 检查返回结果的格式

## 运行测试

### 使用pytest运行
```bash
# 运行所有测试
pytest tests/integration/test_researcher_web_search.py -v

# 运行特定测试
pytest tests/integration/test_researcher_web_search.py::test_researcher_node_tools_setup -v
```

### 独立运行
```bash
# 直接运行脚本
python tests/integration/test_researcher_web_search.py
```

## 环境要求

- Python 3.8+
- pytest
- pytest-asyncio
- 项目依赖包

## 常量配置

测试中使用的关键常量：

```python
DEFAULT_RECURSION_LIMIT = 25
DEFAULT_MAX_SEARCH_RESULTS = 3
TEST_QUERY = "Research Average Lifespan of Tesla Batteries"
```

## 模拟策略

测试使用以下模拟策略：

1. **工具模拟**：模拟web_search、crawl_tool等工具的行为
2. **Agent模拟**：模拟researcher agent的响应
3. **API模拟**：模拟Tavily API的响应数据
4. **状态模拟**：创建模拟的State和Configuration对象

## 预期输出

测试成功时会输出：

- ✓ 工具创建和配置验证
- ✓ Agent输入构建验证
- ✓ Web search工具调用验证
- ✓ 完整工作流程验证
- 期望的日志格式展示

## 故障排除

如果测试失败，请检查：

1. 项目依赖是否正确安装
2. 环境变量是否正确设置
3. 导入路径是否正确
4. 模拟对象是否正确配置

## 扩展测试

可以通过修改以下部分来扩展测试：

- `TEST_QUERY`：更改测试查询
- `DEFAULT_MAX_SEARCH_RESULTS`：调整搜索结果数量
- `mock_search_results`：修改模拟的搜索结果
- 添加新的测试场景和验证逻辑 