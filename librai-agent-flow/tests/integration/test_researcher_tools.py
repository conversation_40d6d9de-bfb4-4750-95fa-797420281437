# Author: <PERSON><PERSON>
# Test script for researcher node tools

import asyncio
import logging
import os
from typing import Dict, Any

from langchain_core.messages import HumanMessage
from langgraph.graph import StateGraph, START, END

from src.graph.nodes import researcher_node
from src.graph.types import State
from src.config.configuration import Configuration
from src.prompts.planner_model import Plan, Step, StepType

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 测试配置
TEST_CONFIG = {
    "configurable": {
        "max_search_results": 5,
        "max_plan_iterations": 1,
        "max_step_num": 3,
        "enable_background_investigation": False,
        "resources": [],
        "mcp_settings": None
    }
}

# 测试用例
TEST_CASES = [
    # {
    #     "name": "地理编码测试",
    #     "query": "请查询北京市天安门广场的地理坐标信息",
    #     "expected_tools": ["geocode_tool"]
    # },
    # {
    #     "name": "时区转换测试", 
    #     "query": "请将北京时间2024年1月1日12:00转换为纽约时间",
    #     "expected_tools": ["time_convert_tool", "geocode_tool"]
    # },
    # {
    #     "name": "天气查询测试",
    #     "query": "请查询纽约今天的天气情况",
    #     "expected_tools": ["weather_by_address_tool", "geocode_tool"]
    # },
    # {
    #     "name": "货币转换测试",
    #     "query": "请将100美元转换为人民币，并查询当前汇率",
    #     "expected_tools": ["currency_convert_tool", "currency_rates_tool"]
    # },
    {
        "name": "综合查询测试",
        "query": "请查询迪拜的地理位置、当前时区、天气情况和日元对美元的汇率",
        "expected_tools": ["geocode_tool", "timezone_by_address_tool", "weather_by_address_tool", "currency_rates_tool"]
    },
    {
        "name": "SerpAPI工具测试",
        "query": "请查询纽约地区的活动信息，并搜索从纽约到洛杉矶的航班，以及纽约的酒店信息",
        "expected_tools": ["google_events_search", "google_flights_search"]
    }
]

def create_test_plan(query: str) -> Plan:
    """为测试创建计划"""
    test_step = Step(
        need_search=True,
        title=f"执行查询: {query}",
        description=query,
        step_type=StepType.RESEARCH,
        execution_res=None
    )
    
    return Plan(
        locale="zh-CN",
        has_enough_context=False,
        thought=f"需要执行查询: {query}",
        title="测试查询计划",
        steps=[test_step]
    )

async def create_simple_researcher_workflow():
    """创建一个简单的researcher工作流"""
    
    def create_researcher_state(query: str) -> State:
        """创建测试状态"""
        return State(
            research_topic=query,
            locale="zh-CN",
            messages=[],
            observations=[],
            current_plan=create_test_plan(query),
            plan_iterations=0,
            auto_accepted_plan=True,
            enable_background_investigation=False,
            resources=[],
            background_investigation_results=None,
            final_report=None
        )
    
    def researcher_wrapper(state: State, config: Dict[str, Any]):
        """包装researcher节点调用"""
        return asyncio.run(researcher_node(state, config))
    
    # 构建简单的工作流图
    builder = StateGraph(State)
    builder.add_edge(START, "researcher")
    builder.add_edge("researcher", END)
    builder.add_node("researcher", researcher_wrapper)
    
    return builder.compile()

async def test_researcher_tools():
    """测试researcher节点的工具调用"""
    
    print("🧪 开始测试researcher节点工具调用...")
    print("=" * 60)
    
    # 检查环境变量
    print("📋 检查环境变量配置:")
    required_env_vars = {
        "GOOGLE_API_KEY": "Google API密钥（用于地理编码、时区、天气）",
        "EXCHANGE_RATE_API_KEY": "汇率API密钥（用于货币转换）",
        "TAVILY_API_KEY": "Tavily搜索API密钥（用于网络搜索）",
        "SERPAPI_API_KEY": "SerpAPI密钥（用于活动、航班、酒店、本地搜索）"
    }
    
    for var, description in required_env_vars.items():
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: 已配置 ({value[:10]}...)")
        else:
            print(f"⚠️  {var}: 未配置 - {description}")
    
    print("\n" + "=" * 60)
    
    # 测试每个用例
    for i, test_case in enumerate(TEST_CASES, 1):
        print(f"\n🔍 测试用例 {i}: {test_case['name']}")
        print(f"查询: {test_case['query']}")
        print(f"期望使用的工具: {', '.join(test_case['expected_tools'])}")
        print("-" * 40)
        
        try:
            # 创建状态
            state = State(
                research_topic=test_case['query'],
                locale="zh-CN",
                messages=[],
                observations=[],
                current_plan=create_test_plan(test_case['query']),
                plan_iterations=0,
                auto_accepted_plan=True,
                enable_background_investigation=False,
                resources=[],
                background_investigation_results=None,
                final_report=None
            )
            
            # 调用researcher节点
            print("🔄 正在执行researcher节点...")
            result = await researcher_node(state, TEST_CONFIG)
            
            # 分析结果
            if result and hasattr(result, 'update'):
                observations = result.update.get('observations', [])
                if observations:
                    print("✅ 节点执行成功!")
                    print(f"📝 生成了 {len(observations)} 个观察结果")
                    
                    # 显示最后一个观察结果的前200个字符
                    last_observation = observations[-1]
                    preview = last_observation[:700] + "..." if len(last_observation) > 700 else last_observation
                    print(f"📄 结果预览: {preview}")
                else:
                    print("⚠️  节点执行完成但未生成观察结果")
            else:
                print("❌ 节点执行失败或返回异常结果")
                
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            logger.exception(f"测试用例 {i} 执行异常")
        
        print("-" * 40)

async def test_individual_tools():
    """单独测试每个工具的功能"""
    
    print("\n🔧 单独测试工具功能...")
    print("=" * 60)
    
    # 测试地理编码工具
    print("\n📍 测试地理编码工具:")
    try:
        from src.tools.geocode import geocode_tool
        result = geocode_tool.invoke({"address": "北京市天安门广场"})
        print(f"✅ 地理编码工具测试成功: {result[:100]}...")
    except Exception as e:
        print(f"❌ 地理编码工具测试失败: {e}")
    
    # 测试时区转换工具
    print("\n⏰ 测试时区转换工具:")
    try:
        from src.tools.timeconvert import time_convert_tool
        result = time_convert_tool.invoke({
            "from_timezone": "Asia/Shanghai",
            "to_timezone": "America/New_York", 
            "time_str": "12:00:00",
            "date_str": "2024-01-01"
        })
        print(f"✅ 时区转换工具测试成功: {result[:100]}...")
    except Exception as e:
        print(f"❌ 时区转换工具测试失败: {e}")
    
    # 测试天气查询工具
    print("\n🌤️ 测试天气查询工具:")
    try:
        from src.tools.weather import weather_by_address_tool
        result = weather_by_address_tool.invoke({"address": "上海市", "hours": 24})
        print(f"✅ 天气查询工具测试成功: {result[:100]}...")
    except Exception as e:
        print(f"❌ 天气查询工具测试失败: {e}")
    
    # 测试货币转换工具
    print("\n💰 测试货币转换工具:")
    try:
        from src.tools.currency import currency_convert_tool
        result = currency_convert_tool.invoke({
            "amount": 100,
            "from_currency": "USD",
            "to_currency": "CNY"
        })
        print(f"✅ 货币转换工具测试成功: {result[:100]}...")
    except Exception as e:
        print(f"❌ 货币转换工具测试失败: {e}")
    
    # 测试时区信息工具
    print("\n🌍 测试时区信息工具:")
    try:
        from src.tools.timeconvert import timezone_info_tool
        # 使用北京的坐标而不是时区名称
        result = timezone_info_tool.invoke({
            "latitude": 39.9042, 
            "longitude": 116.4074
        })
        print(f"✅ 时区信息工具测试成功: {result[:100]}...")
    except Exception as e:
        print(f"❌ 时区信息工具测试失败: {e}")
    
    # 测试天气预报工具
    print("\n🌦️ 测试天气预报工具:")
    try:
        from src.tools.weather import weather_forecast_tool
        # 使用北京的坐标而不是位置名称
        result = weather_forecast_tool.invoke({
            "latitude": 39.9042, 
            "longitude": 116.4074, 
            "hours": 24
        })
        print(f"✅ 天气预报工具测试成功: {result[:100]}...")
    except Exception as e:
        print(f"❌ 天气预报工具测试失败: {e}")
    
    # 测试支持的货币列表工具
    print("\n 测试支持的货币列表工具:")
    try:
        from src.tools.currency import supported_currencies_tool
        result = supported_currencies_tool.invoke({})
        print(f"✅ 支持的货币列表工具测试成功: {result[:100]}...")
    except Exception as e:
        print(f"❌ 支持的货币列表工具测试失败: {e}")
    
    # 测试SerpAPI工具
    print("\n🎫 测试SerpAPI工具:")
    
    # 测试谷歌活动搜索工具
    print("\n   📅 测试谷歌活动搜索工具:")
    try:
        from src.tools.serpapi_tools import google_events_search
        result = google_events_search.invoke({
            "query": "events",
            "location": "New York",
            "num_results": 3
        })
        print(f"✅ 谷歌活动搜索工具测试成功: {result[:100]}...")
    except Exception as e:
        print(f"❌ 谷歌活动搜索工具测试失败: {e}")
    
    # 测试谷歌航班搜索工具
    print("\n   ✈️ 测试谷歌航班搜索工具:")
    try:
        from src.tools.serpapi_tools import google_flights_search
        # 使用未来的日期
        from datetime import datetime, timedelta
        future_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
        
        result = google_flights_search.invoke({
            "departure_id": "JFK",
            "arrival_id": "LAX",
            "outbound_date": future_date,
            "adults": 1
        })
        print(f"✅ 谷歌航班搜索工具测试成功: {result[:100]}...")
    except Exception as e:
        print(f"❌ 谷歌航班搜索工具测试失败: {e}")
    
    # 测试谷歌酒店搜索工具
    print("\n   🏨 测试谷歌酒店搜索工具:")
    try:
        from src.tools.serpapi_tools import google_hotels_search
        # 使用未来的日期
        from datetime import datetime, timedelta
        check_in_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
        check_out_date = (datetime.now() + timedelta(days=33)).strftime("%Y-%m-%d")
        
        result = google_hotels_search.invoke({
            "q": "Tokyo, Japan",
            "check_in_date": check_in_date,
            "check_out_date": check_out_date,
            "adults": 2
        })
        print(f"✅ 谷歌酒店搜索工具测试成功: {result[:100]}...")
    except Exception as e:
        print(f"❌ 谷歌酒店搜索工具测试失败: {e}")
    
    # 测试谷歌本地搜索工具
    print("\n   🏪 测试谷歌本地搜索工具:")
    try:
        from src.tools.serpapi_tools import google_local_search
        result = google_local_search.invoke({
            "query": "coffee shops",
            "location": "San Francisco, CA",
            "num": 3
        })
        print(f"✅ 谷歌本地搜索工具测试成功: {result[:100]}...")
    except Exception as e:
        print(f"❌ 谷歌本地搜索工具测试失败: {e}")

async def main():
    """主测试函数"""
    print("🚀 Researcher节点工具测试脚本")
    print("=" * 60)
    
    # 测试单独的工具
    await test_individual_tools()
    
    # 测试researcher节点
    await test_researcher_tools()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成!")

if __name__ == "__main__":
    asyncio.run(main()) 