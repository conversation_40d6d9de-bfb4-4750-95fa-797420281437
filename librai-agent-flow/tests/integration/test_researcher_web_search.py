# Author: <PERSON><PERSON>
"""
测试researcher节点在调用web_search时的输入输出
简化版：直接运行输出结果，无需pytest、无类、无logger
"""

import asyncio
import os
from unittest.mock import Mock, patch, AsyncMock
from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig
from src.graph.nodes import researcher_node, _execute_agent_step
from src.graph.types import State
from src.config.configuration import Configuration
from src.tools.search import get_web_search_tool

# 常量
DEFAULT_RECURSION_LIMIT = 25
DEFAULT_MAX_SEARCH_RESULTS = 3
TEST_QUERY = "Research Average Lifespan of Tesla Batteries"

def create_mock_state():
    state = State()
    
    # 兼容dict类型的赋值方式
    if isinstance(state, dict):
        state['locale'] = "en-US"
        state['research_topic'] = TEST_QUERY
        state['observations'] = []
        state['resources'] = []
        state['plan_iterations'] = 0
        state['current_plan'] = None
        state['final_report'] = ""
        state['auto_accepted_plan'] = False
        state['enable_background_investigation'] = True
        state['background_investigation_results'] = None
        
        # 添加当前步骤信息
        from src.prompts.planner_model import Plan, Step, StepType
        current_step = Step(
            need_search=True,
            title="Research Average Lifespan of Tesla Batteries",
            description="Collect data on the average lifespan of Tesla batteries. This includes warranty information, average mileage before significant degradation, user testimonials, and any variations based on model or usage. Obtain data from Tesla's official resources, industry reports, and credible automotive reviews.",
            step_type=StepType.RESEARCH,
            execution_res=""
        )
        state['current_plan'] = Plan(
            locale="en-US",
            has_enough_context=False,
            thought="Need to research Tesla battery lifespan information",
            title="Tesla Battery Research Plan",
            steps=[current_step]
        )
    else:
        # 保持原有的属性赋值方式作为备选
        state.locale = "en-US"
        state.research_topic = TEST_QUERY
        state.observations = []
        state.resources = []
        state.plan_iterations = 0
        state.current_plan = None
        state.final_report = ""
        state.auto_accepted_plan = False
        state.enable_background_investigation = True
        state.background_investigation_results = None
        
        # 添加当前步骤信息
        from src.prompts.planner_model import Plan, Step, StepType
        current_step = Step(
            need_search=True,
            title="Research Average Lifespan of Tesla Batteries",
            description="Collect data on the average lifespan of Tesla batteries. This includes warranty information, average mileage before significant degradation, user testimonials, and any variations based on model or usage. Obtain data from Tesla's official resources, industry reports, and credible automotive reviews.",
            step_type=StepType.RESEARCH,
            execution_res=""
        )
        state.current_plan = Plan(
            locale="en-US",
            has_enough_context=False,
            thought="Need to research Tesla battery lifespan information",
            title="Tesla Battery Research Plan",
            steps=[current_step]
        )
    
    return state

def create_mock_config():
    configurable = Configuration(
        max_search_results=DEFAULT_MAX_SEARCH_RESULTS,
        max_plan_iterations=1,
        max_step_num=3,
        report_style="academic",
        enable_deep_thinking=False
    )
    return RunnableConfig(
        configurable=configurable.__dict__,
        recursion_limit=DEFAULT_RECURSION_LIMIT
    )

def create_mock_agent():
    agent = Mock()
    agent.ainvoke = AsyncMock()
    mock_response = {
        "messages": [
            HumanMessage(
                content="# Problem Statement\n\nResearch the average lifespan of Tesla batteries...\n\n# Research Findings\n\nBased on my research...\n\n# References\n\n- [Tesla Battery Warranty](https://www.tesla.com/support/vehicle-warranty)\n\n- [Battery Degradation Study](https://example.com/study)"
            )
        ]
    }
    agent.ainvoke.return_value = mock_response
    return agent

async def test_researcher_node_tools_setup():
    print("=== 测试researcher节点的工具设置 ===")
    state = create_mock_state()
    config = create_mock_config()
    with patch('src.graph.nodes.get_web_search_tool') as mock_web_search, \
         patch('src.graph.nodes.crawl_tool') as mock_crawl_tool, \
         patch('src.graph.nodes.get_retriever_tool') as mock_retriever_tool, \
         patch('src.graph.nodes._setup_and_execute_agent_step') as mock_setup:
        mock_web_search.return_value = Mock(name='web_search')
        mock_crawl_tool.name = 'crawl_tool'
        mock_retriever_tool.return_value = None
        await researcher_node(state, config)
        mock_web_search.assert_called_once_with(DEFAULT_MAX_SEARCH_RESULTS)
        print(f"✓ web_search工具创建成功，max_results={DEFAULT_MAX_SEARCH_RESULTS}")
        mock_setup.assert_called_once()
        args, kwargs = mock_setup.call_args
        tools = args[3]
        tool_names = [tool.name if hasattr(tool, 'name') else getattr(tool, '__name__', str(tool)) for tool in tools]
        print(f"✓ Researcher工具列表: {tool_names}")

async def test_agent_input_construction():
    print("\n=== 测试agent输入构建 ===")
    state = create_mock_state()
    agent = create_mock_agent()
    with patch('src.graph.nodes._execute_agent_step') as mock_execute:
        mock_execute.return_value = Mock()
        await _execute_agent_step(state, agent, "researcher")
        call_args = agent.ainvoke.call_args
        agent_input = call_args[1]['input']
        print(f"✓ Agent输入构建成功")
        print(f"✓ 输入消息数量: {len(agent_input['messages'])}")
        for i, message in enumerate(agent_input['messages']):
            print(f"✓ 消息 {i+1}: {message.content[:100]}...")
        config = call_args[1]['config']
        print(f"✓ 递归限制设置: {config.get('recursion_limit', '未设置')}")

async def test_web_search_tool_invocation():
    print("\n=== 测试web_search工具调用 ===")
    web_search_tool = get_web_search_tool(DEFAULT_MAX_SEARCH_RESULTS)
    mock_search_results = [
        {
            "type": "page",
            "title": "Tesla Battery Warranty Information",
            "url": "https://www.tesla.com/support/vehicle-warranty",
            "content": "Tesla provides comprehensive warranty coverage for its battery systems...",
            "score": 0.95
        },
        {
            "type": "page", 
            "title": "Tesla Battery Degradation Study",
            "url": "https://example.com/tesla-battery-study",
            "content": "Recent studies show that Tesla batteries typically retain 90% capacity after 200,000 miles...",
            "score": 0.92
        },
        {
            "type": "image",
            "image_url": "https://example.com/tesla-battery-diagram.jpg",
            "image_description": "Tesla battery pack diagram showing cell arrangement"
        }
    ]
    with patch.object(web_search_tool, '_arun') as mock_arun:
        mock_arun.return_value = (mock_search_results, {})
        result = await web_search_tool.ainvoke({"query": TEST_QUERY})
        print(f"✓ web_search工具调用成功")
        print(f"✓ 查询: {TEST_QUERY}")
        print(f"✓ 返回结果数量: {len(result)}")
        if isinstance(result, list):
            for i, item in enumerate(result):
                print(f"✓ 结果 {i+1}: {item.get('type', 'unknown')} - {item.get('title', item.get('image_description', 'N/A'))}")

async def test_full_researcher_workflow():
    print("\n=== 测试完整的researcher工作流程 ===")
    state = create_mock_state()
    config = create_mock_config()
    with patch('src.graph.nodes.get_web_search_tool') as mock_web_search, \
         patch('src.graph.nodes.crawl_tool') as mock_crawl_tool, \
         patch('src.graph.nodes.get_retriever_tool') as mock_retriever_tool, \
         patch('src.graph.nodes.create_agent') as mock_create_agent, \
         patch('src.graph.nodes._execute_agent_step') as mock_execute:
        mock_web_search.return_value = Mock(name='web_search')
        mock_crawl_tool.name = 'crawl_tool'
        mock_retriever_tool.return_value = None
        mock_agent = Mock()
        mock_agent.ainvoke = AsyncMock()
        mock_agent.ainvoke.return_value = {
            "messages": [HumanMessage(content="Research completed successfully")]
        }
        mock_create_agent.return_value = mock_agent
        mock_execute.return_value = Mock()
        result = await researcher_node(state, config)
        print(f"✓ 完整工作流程执行成功")
        print(f"✓ 返回结果类型: {type(result)}")
        print("✓ 工具配置日志已输出（模拟）:")
        print("  - LoggedTavilySearchResultsWithImages(name='web_search', max_results=3, include_raw_content=True, include_images=True)")
        print("  - StructuredTool(name='crawl_tool', description='Use this to crawl a url...')")

def log_expected_output():
    print("\n=== 期望的日志输出格式 ===")
    print("2025-06-30 07:52:38,795 - src.graph.nodes - INFO - Researcher tools: [LoggedTavilySearchResultsWithImages(name='web_search', max_results=3, include_raw_content=True, include_images=True, api_wrapper=EnhancedTavilySearchAPIWrapper(tavily_api_key=SecretStr('**********')), include_image_descriptions=True), StructuredTool(name='crawl_tool', description='Use this to crawl a url and get a readable content in markdown format.', args_schema=<class 'langchain_core.utils.pydantic.crawl_tool'>, func=<function crawl_tool at 0x7f08ed6c7a60>)]")
    print("2025-06-30 07:52:38,803 - src.graph.nodes - INFO - Executing step: Research Average Lifespan of Tesla Batteries, agent: researcher")
    print("2025-06-30 07:52:38,803 - src.graph.nodes - INFO - Recursion limit set to: 25")
    print("2025-06-30 07:52:38,803 - src.graph.nodes - INFO - Agent input: {'messages': [HumanMessage(content='# Current Task\\n\\n## Title\\n\\nResearch Average Lifespan of Tesla Batteries\\n\\n## Description\\n\\nCollect data on the average lifespan of Tesla batteries...')]}")

async def main():
    await test_researcher_node_tools_setup()
    await test_agent_input_construction()
    await test_web_search_tool_invocation()
    await test_full_researcher_workflow()
    log_expected_output()
    print("\n🎉 所有测试完成！")
    print("这个脚本成功复现了researcher节点在调用web_search时的输入输出测试场景。")

if __name__ == "__main__":
    os.environ.setdefault("AGENT_RECURSION_LIMIT", str(DEFAULT_RECURSION_LIMIT))
    asyncio.run(main()) 