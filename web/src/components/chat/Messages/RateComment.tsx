'use client';

import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface RateCommentProps {
  messageId: string;
  initialRating?: 'up' | 'down' | null;
  onSave: (data: {
    rating: number;
    reason: string;
    comment: string;
    tags: string[];
  }) => void;
  onClose: () => void;
}

const LIKE_REASONS = [
  'accurate_information',
  'followed_instructions_perfectly',
  'showcased_creativity', 
  'positive_attitude',
  'attention_to_detail',
  'thorough_explanation',
  'other'
];

const DISLIKE_REASONS = [
  'dont_like_the_style',
  'too_verbose',
  'not_helpful',
  'not_factually_correct',
  'didnt_fully_follow_instructions',
  'refused_when_it_shouldnt_have',
  'being_lazy',
  'other'
];

const REASON_LABELS = {
  'accurate_information': 'Accurate information',
  'followed_instructions_perfectly': 'Followed instructions perfectly',
  'showcased_creativity': 'Showcased creativity',
  'positive_attitude': 'Positive attitude',
  'attention_to_detail': 'Attention to detail',
  'thorough_explanation': 'Thorough explanation',
  'dont_like_the_style': "Don't like the style",
  'too_verbose': 'Too verbose',
  'not_helpful': 'Not helpful',
  'not_factually_correct': 'Not factually correct',
  'didnt_fully_follow_instructions': "Didn't fully follow instructions",
  'refused_when_it_shouldnt_have': "Refused when it shouldn't have",
  'being_lazy': 'Being lazy',
  'other': 'Other'
};

export const RateComment: React.FC<RateCommentProps> = ({
  messageId,
  initialRating,
  onSave,
  onClose
}) => {
  const [detailedRating, setDetailedRating] = useState<number | null>(null);
  const [selectedReason, setSelectedReason] = useState<string>('');
  const [comment, setComment] = useState<string>('');
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState<string>('');

  const isLike = initialRating === 'up';
  const reasons = isLike ? LIKE_REASONS : DISLIKE_REASONS;
  
  // Set default rating based on thumbs up/down
  useEffect(() => {
    if (initialRating === 'up' && !detailedRating) {
      setDetailedRating(8); // Default high rating for thumbs up
    } else if (initialRating === 'down' && !detailedRating) {
      setDetailedRating(3); // Default low rating for thumbs down
    }
  }, [initialRating, detailedRating]);

  const handleSave = () => {
    if (!detailedRating) {
      toast.error('Please select a rating');
      return;
    }

    onSave({
      rating: detailedRating,
      reason: selectedReason,
      comment: comment,
      tags: tags
    });

    toast.success('Thanks for your feedback!');
    onClose();
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && newTag.trim()) {
      addTag();
    }
  };

  return (
    <div className="my-2.5 rounded-xl px-4 py-3 border border-gray-100 dark:border-gray-800 bg-white dark:bg-gray-900">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="text-sm font-medium">How would you rate this response?</div>
        <button onClick={onClose} className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded">
          <X className="w-4 h-4" />
        </button>
      </div>

      {/* Rating Scale */}
      <div className="w-full flex justify-center">
        <div className="relative w-fit">
          <div className="mt-1.5 w-fit flex gap-1 pb-5">
            {Array.from({ length: 10 }, (_, i) => i + 1).map(rating => (
              <button
                key={rating}
                className={cn(
                  "size-7 text-sm border border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800 transition rounded-full",
                  detailedRating === rating ? "bg-gray-100 dark:bg-gray-700" : "",
                  // Disable ratings based on thumbs up/down
                  (!isLike && rating > 5) || (isLike && rating < 6) 
                    ? "cursor-not-allowed text-gray-400 bg-gray-50 dark:bg-gray-800" 
                    : ""
                )}
                onClick={() => setDetailedRating(rating)}
                disabled={(!isLike && rating > 5) || (isLike && rating < 6)}
              >
                {rating}
              </button>
            ))}
          </div>
          
          <div className="absolute bottom-0 left-0 right-0 flex justify-between text-xs text-gray-500">
            <div>1 - Awful</div>
            <div>10 - Amazing</div>
          </div>
        </div>
      </div>

      {/* Reasons */}
      {reasons.length > 0 && (
        <div>
          <div className="text-sm mt-1.5 font-medium">Why?</div>
          <div className="flex flex-wrap gap-1.5 text-sm mt-1.5">
            {reasons.map(reason => (
              <button
                key={reason}
                className={cn(
                  "px-3 py-0.5 border border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800 transition rounded-xl",
                  selectedReason === reason ? "bg-gray-100 dark:bg-gray-700" : ""
                )}
                onClick={() => setSelectedReason(reason)}
              >
                {REASON_LABELS[reason as keyof typeof REASON_LABELS] || reason}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Comment */}
      <div className="mt-2">
        <textarea
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          className="w-full text-sm px-1 py-2 bg-transparent outline-none resize-none rounded-xl border border-transparent focus:border-gray-200 dark:focus:border-gray-700"
          placeholder="Feel free to add specific details"
          rows={3}
        />
      </div>

      {/* Tags */}
      <div className="mt-2 gap-1.5 flex justify-between items-end">
        <div className="flex items-center flex-wrap gap-1">
          {tags.map(tag => (
            <span
              key={tag}
              className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded-full"
            >
              {tag}
              <button onClick={() => removeTag(tag)} className="hover:text-red-500">
                <X className="w-3 h-3" />
              </button>
            </span>
          ))}
          <input
            type="text"
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            onKeyPress={handleKeyPress}
            onBlur={addTag}
            placeholder="Add tag..."
            className="text-xs bg-transparent border-none outline-none min-w-16 max-w-24"
          />
        </div>

        <Button
          onClick={handleSave}
          className="px-3.5 py-1.5 text-sm font-medium transition rounded-full"
          style={{
            color:'white'
          }}
        >
          Save
        </Button>
      </div>
    </div>
  );
};