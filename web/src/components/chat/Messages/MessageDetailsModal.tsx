'use client';

import React from 'react';
import { X, Clock, Cpu, Hash, FileText } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { Message } from '@/lib/types';

interface MessageDetailsModalProps {
  message: Message;
  isOpen: boolean;
  onClose: () => void;
}

export const MessageDetailsModal: React.FC<MessageDetailsModalProps> = ({
  message,
  isOpen,
  onClose
}) => {
  if (!isOpen) return null;

  const formatTimestamp = (timestamp?: number) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp * 1000).toLocaleString();
  };

  const calculateContentLength = (content?: string) => {
    if (!content) return 0;
    return content.length;
  };

  const getWordCount = (content?: string) => {
    if (!content) return 0;
    return content.split(/\s+/).filter(word => word.length > 0).length;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-600">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Message Details
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
          >
            <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4 overflow-y-auto">
          {/* Message ID */}
          <div className="flex items-start space-x-3">
            <Hash className="w-5 h-5 text-gray-400 mt-0.5" />
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Message ID</dt>
              <dd className="text-sm text-gray-900 dark:text-white font-mono">
                {message.id || 'N/A'}
              </dd>
            </div>
          </div>

          {/* Role */}
          <div className="flex items-start space-x-3">
            <FileText className="w-5 h-5 text-gray-400 mt-0.5" />
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Role</dt>
              <dd className={cn(
                "text-sm font-medium",
                message.role === 'user' ? "text-blue-600" : "text-green-600"
              )}>
                {message.role === 'user' ? 'User' : 'Assistant'}
              </dd>
            </div>
          </div>

          {/* Model */}
          {message.model && (
            <div className="flex items-start space-x-3">
              <Cpu className="w-5 h-5 text-gray-400 mt-0.5" />
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Model</dt>
                <dd className="text-sm text-gray-900 dark:text-white">
                  {message.model}
                </dd>
              </div>
            </div>
          )}

          {/* Timestamp */}
          <div className="flex items-start space-x-3">
            <Clock className="w-5 h-5 text-gray-400 mt-0.5" />
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Timestamp</dt>
              <dd className="text-sm text-gray-900 dark:text-white">
                {formatTimestamp(message.timestamp)}
              </dd>
            </div>
          </div>

          {/* Content Stats */}
          <div className="flex items-start space-x-3">
            <FileText className="w-5 h-5 text-gray-400 mt-0.5" />
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Content Statistics</dt>
              <dd className="text-sm text-gray-900 dark:text-white space-y-1">
                <div>Characters: {calculateContentLength(message.content)}</div>
                <div>Words: {getWordCount(message.content)}</div>
              </dd>
            </div>
          </div>

          {/* Status */}
          <div className="flex items-start space-x-3">
            <div className={cn(
              "w-5 h-5 rounded-full mt-1",
              message.done ? "bg-green-500" : "bg-yellow-500"
            )} />
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
              <dd className="text-sm text-gray-900 dark:text-white">
                {message.done ? 'Completed' : 'Processing'}
              </dd>
            </div>
          </div>

          {/* Files */}
          {message.files && message.files.length > 0 && (
            <div className="flex items-start space-x-3">
              <FileText className="w-5 h-5 text-gray-400 mt-0.5" />
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Attachments</dt>
                <dd className="text-sm text-gray-900 dark:text-white space-y-1">
                  {message.files.map((file, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <span>📎</span>
                      <span>{file.name || `File ${index + 1}`}</span>
                    </div>
                  ))}
                </dd>
              </div>
            </div>
          )}

          {/* Content Preview */}
          <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Content Preview</dt>
            <dd className="text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 p-4 rounded-lg max-h-40 overflow-y-auto">
              <pre className="whitespace-pre-wrap font-sans">
                {message.content || 'No content'}
              </pre>
            </dd>
          </div>
        </div>
      </div>
    </div>
  );
};