'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Clock, Cpu, Hash, FileText } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { Message } from '@/lib/types';

interface MessageDetailsTooltipProps {
  message: Message;
  children: React.ReactNode;
  className?: string;
}

export const MessageDetailsTooltip: React.FC<MessageDetailsTooltipProps> = ({
  message,
  children,
  className
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Format usage information like the original project
  const formatUsage = (usage: any) => {
    if (!usage) return '';
    
    try {
      return JSON.stringify(usage, null, 2)
        .replace(/"([^(")"]+)":/g, '$1:')
        .slice(1, -1)
        .split('\n')
        .map((line) => line.slice(2))
        .map((line) => (line.endsWith(',') ? line.slice(0, -1) : line))
        .join('\n');
    } catch {
      return String(usage);
    }
  };

  const updatePosition = () => {
    if (!triggerRef.current || !tooltipRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;
    let y = triggerRect.top - tooltipRect.height - 8;

    // Adjust horizontal position to stay within viewport
    if (x < 8) {
      x = 8;
    } else if (x + tooltipRect.width > viewportWidth - 8) {
      x = viewportWidth - tooltipRect.width - 8;
    }

    // Adjust vertical position if tooltip would go above viewport
    if (y < 8) {
      y = triggerRect.bottom + 8;
    }

    setPosition({ x, y });
  };

  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      // Update position after showing to get accurate measurements
      setTimeout(updatePosition, 0);
    }, 300); // 300ms delay before showing
  };

  const handleMouseLeave = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      setIsVisible(false);
    }, 150); // Small delay before hiding to allow moving to tooltip
  };

  const handleTooltipMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  const handleTooltipMouseLeave = () => {
    setIsVisible(false);
  };

  useEffect(() => {
    if (isVisible) {
      updatePosition();
      
      const handleResize = () => updatePosition();
      const handleScroll = () => updatePosition();
      
      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleScroll, true);
      
      return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('scroll', handleScroll, true);
      };
    }
  }, [isVisible]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <>
      {/* Trigger element */}
      <div
        ref={triggerRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className={cn("inline-block", className)}
      >
        {children}
      </div>

      {/* Tooltip */}
      {isVisible && (
        <div
          ref={tooltipRef}
          className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-xl p-3 max-w-sm"
          style={{
            left: position.x,
            top: position.y,
          }}
          onMouseEnter={handleTooltipMouseEnter}
          onMouseLeave={handleTooltipMouseLeave}
        >
          {/* Arrow */}
          <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white border-r border-b border-gray-200 rotate-45" />

          <div className="text-xs text-gray-900 space-y-2">
            {/* Show usage info if available */}
            {(message as any).info && (
              <div>
                <div className="font-semibold text-gray-700 mb-1">Usage:</div>
                <pre className="whitespace-pre-wrap break-words font-mono">
                  {formatUsage((message as any).info)}
                </pre>
              </div>
            )}
            
            {/* Show basic message info */}
            <div>
              <div className="font-semibold text-gray-700 mb-1">Details:</div>
              <div className="space-y-1">
                <div><span className="font-medium">ID:</span> {message.id}</div>
                <div><span className="font-medium">Role:</span> {message.role}</div>
                {message.model && <div><span className="font-medium">Model:</span> {message.model}</div>}
                {message.timestamp && (
                  <div><span className="font-medium">Time:</span> {new Date(message.timestamp * 1000).toLocaleString()}</div>
                )}
                <div><span className="font-medium">Status:</span> {message.done ? 'Complete' : 'Generating'}</div>
                {message.content && (
                  <div><span className="font-medium">Length:</span> {message.content.length} chars</div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};