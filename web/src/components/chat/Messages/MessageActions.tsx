'use client';

import React, { useState, useEffect, useRef } from 'react';
import { toast } from 'sonner';
import { Co<PERSON>, RotateCcw, ThumbsUp, ThumbsDown, Edit3, Volume2, Info, Play } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuthStore, useChatStore } from '@/lib/stores';
import { useChats } from '@/hooks/useChats';
import { MessageDetailsTooltip } from './MessageDetailsTooltip';
import { RateComment } from './RateComment';
import { createFeedback, updateFeedbackById } from '@/lib/api/evaluations';
import { getChatById } from '@/lib/api/chats';
import type { Message } from '@/lib/types';
import type { Feedback } from '@/lib/api/evaluations';

interface MessageActionsProps {
  message: Message;
  onCopy?: () => void;
  onRegenerate?: () => void;
  onEdit?: () => void;
  onReadAloud?: () => void;
  onContinue?: () => void;
  className?: string;
}

export const MessageActions: React.FC<MessageActionsProps> = ({
  message,
  onCopy,
  onRegenerate,
  onEdit,
  onReadAloud,
  onContinue,
  className
}) => {
  const [rating, setRating] = useState<'up' | 'down' | null>(null);
  const [showActions, setShowActions] = useState(false);
  const [showRating, setShowRating] = useState(false);
  const [currentFeedback, setCurrentFeedback] = useState<Feedback | null>(null);
  const [isRatingLoading, setIsRatingLoading] = useState(false);
  const actionsRef = useRef<HTMLDivElement>(null);
  const { token } = useAuthStore();
  const { currentChat } = useChatStore();
  const { loadChats } = useChats();

  const isAssistant = message.role === 'assistant';
  const isUser = message.role === 'user';

  // Note: Removed automatic feedback loading to avoid unnecessary API calls
  // Feedback will be loaded only when needed (on first rating attempt)

  // For user messages, show actions only on hover
  useEffect(() => {
    if (!isUser) return; // Only apply hover logic to user messages

    const messageElement = actionsRef.current?.parentElement;
    if (!messageElement) return;

    const handleMouseEnter = () => setShowActions(true);
    const handleMouseLeave = () => setShowActions(false);

    messageElement.addEventListener('mouseenter', handleMouseEnter);
    messageElement.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      messageElement.removeEventListener('mouseenter', handleMouseEnter);
      messageElement.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [isUser]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content || '');
      toast.success('Copied to clipboard');
      onCopy?.();
    } catch (error) {
      toast.error('Failed to copy');
    }
  };

  // Handle initial rating click (thumbs up/down) - Start API call sequence immediately
  const handleRate = async (newRating: 'up' | 'down') => {
    if (!token) {
      toast.error('Please sign in to rate messages');
      return;
    }

    if (!currentChat?.id) {
      toast.error('Unable to rate message - no active chat');
      return;
    }

    setRating(newRating);
    setShowRating(true);
    setIsRatingLoading(true);

    try {
      // Start the API call sequence immediately when thumbs up/down is clicked
      // Step 1: Get chat details
      console.log('🚀 Step 1: GET /api/v1/chats/' + currentChat.id);
      const chatDetails = await getChatById(token, currentChat.id);
      console.log('✅ Step 1 completed - Chat details fetched');
      
      // Step 2: Create feedback (POST /api/v1/evaluations/feedback)
      const feedbackPayload = {
        type: 'rating',
        data: {
          rating: newRating === 'up' ? 1 : -1,
          model_id: message.model || 'unknown',
          comment: '',
          reason: newRating === 'up' ? 'helpful' : 'not_helpful'
        },
        meta: {
          message_id: message.id || '',
          chat_id: currentChat.id,
          arena: false
        }
      };
      
      console.log('🚀 Step 2: POST /api/v1/evaluations/feedback');
      console.log('Payload:', feedbackPayload);

      // Always create new feedback, backend will return feedId
      const initialFeedback = await createFeedback(token, feedbackPayload);
      console.log('✅ Step 2 completed - Created feedback with ID:', initialFeedback.id);
      
      setCurrentFeedback(initialFeedback);
      
      // Step 3: Update chat (re-fetch chat with new feedback)
      console.log('🚀 Step 3: GET /api/v1/chats/' + currentChat.id + ' (re-fetch)');
      const updatedChatDetails = await getChatById(token, currentChat.id);
      console.log('✅ Step 3 completed - Updated chat details fetched');
      
      // Step 4: Refresh chat list
      console.log('🚀 Step 4: GET /api/v1/chats/?page=1');
      await loadChats(1); // Explicitly pass page=1
      console.log('✅ Step 4 completed - Chat list refreshed');
      
      console.log('✅ Complete API sequence finished. FeedbackId:', initialFeedback.id);
      
    } catch (error) {
      console.error('Failed to process initial rating:', error);
      toast.error('Failed to submit initial rating');
      // Keep the rating panel open so user can try again
    } finally {
      setIsRatingLoading(false);
    }
  };

  // Handle saving detailed rating from the rating panel
  const handleSaveRating = async (ratingData: {
    rating: number;
    reason: string;
    comment: string;
    tags: string[];
  }) => {
    if (!token || !currentChat?.id || !currentFeedback) {
      toast.error('Unable to save detailed rating');
      return;
    }

    setIsRatingLoading(true);
    
    try {
      // Update the existing feedback with detailed information
      console.log('Updating feedback with detailed rating...');
      const detailedFeedbackPayload = {
        type: 'rating',
        data: {
          rating: rating === 'up' ? ratingData.rating : -Math.abs(ratingData.rating),
          model_id: message.model || 'unknown',
          comment: ratingData.comment,
          reason: ratingData.reason,
          tags: ratingData.tags
        },
        meta: {
          message_id: message.id || '',
          chat_id: currentChat.id,
          arena: false
        }
      };

      // Update the existing feedback with detailed information
      const updatedFeedback = await updateFeedbackById(token, currentFeedback.id, detailedFeedbackPayload);
      
      // Refresh chat and chat list again after detailed update
      console.log('Refreshing after detailed feedback update...');
      await getChatById(token, currentChat.id);
      await loadChats(1); // Explicitly pass page=1
      
      setCurrentFeedback(updatedFeedback);
      setShowRating(false);
      toast.success('Detailed rating saved successfully!');
      
    } catch (error) {
      console.error('Failed to save detailed rating:', error);
      toast.error('Failed to submit detailed rating');
    } finally {
      setIsRatingLoading(false);
    }
  };

  if (!message.content || (!message.done && isAssistant)) {
    return null;
  }

  const shouldShow = (isAssistant || showActions) && !showRating; // Hide when rating panel is shown

  return (
    <>
      <div
        ref={actionsRef}
        className={cn(
          "flex items-center gap-1",
          shouldShow ? "opacity-100" : "opacity-0",
          showRating ? "!opacity-0 pointer-events-none" : "transition-opacity duration-200", // Instant hide when rating panel is open, smooth transition otherwise
          className
        )}
      >
      {/* Copy button */}
      <button
        onClick={handleCopy}
        className="p-1.5 rounded-md hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
        title="Copy message"
      >
        <Copy className="w-4 h-4" />
      </button>

      {/* Assistant-specific actions */}
      {isAssistant && (
        <>
          {/* Edit button for assistant messages */}
          {onEdit && (
            <button
              onClick={onEdit}
              className="p-1.5 rounded-md hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
              title="Edit message"
            >
              <Edit3 className="w-4 h-4" />
            </button>
          )}

          {/* Read aloud button */}
          {onReadAloud && (
            <button
              onClick={onReadAloud}
              className="p-1.5 rounded-md hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
              title="Read aloud"
            >
              <Volume2 className="w-4 h-4" />
            </button>
          )}

          {/* Show details button with tooltip */}
          <MessageDetailsTooltip message={message}>
            <button
              className="p-1.5 rounded-md hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
              title="Show message details"
              onClick={() => {
                console.log(message);
              }}
            >
              <Info className="w-4 h-4" />
            </button>
          </MessageDetailsTooltip>

          {/* Continue generation button */}
          {onContinue && (
            <button
              onClick={onContinue}
              className="p-1.5 rounded-md hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
              title="Continue generation"
            >
              <Play className="w-4 h-4" />
            </button>
          )}

          {/* Regenerate button */}
          {onRegenerate && (
            <button
              onClick={onRegenerate}
              className="p-1.5 rounded-md hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
              title="Regenerate response"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
          )}

          {/* Rating buttons */}
          <button
            onClick={() => handleRate('up')}
            disabled={isRatingLoading}
            className={cn(
              "p-1.5 rounded-md transition-colors",
              rating === 'up'
                ? "bg-green-100 text-green-600"
                : "hover:bg-gray-100 text-gray-500 hover:text-gray-700",
              isRatingLoading && "opacity-50 cursor-not-allowed"
            )}
            title={rating === 'up' ? "Remove like" : "Like message"}
          >
            <ThumbsUp className={cn(
              "w-4 h-4",
              rating === 'up' && "fill-current"
            )} />
          </button>

          <button
            onClick={() => handleRate('down')}
            disabled={isRatingLoading}
            className={cn(
              "p-1.5 rounded-md transition-colors",
              rating === 'down'
                ? "bg-red-100 text-red-600"
                : "hover:bg-gray-100 text-gray-500 hover:text-gray-700",
              isRatingLoading && "opacity-50 cursor-not-allowed"
            )}
            title={rating === 'down' ? "Remove dislike" : "Dislike message"}
          >
            <ThumbsDown className={cn(
              "w-4 h-4",
              rating === 'down' && "fill-current"
            )} />
          </button>
        </>
      )}

      {/* User message actions */}
      {isUser && onEdit && (
        <button
          onClick={onEdit}
          className="p-1.5 rounded-md hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
          title="Edit message"
        >
          <Edit3 className="w-4 h-4" />
        </button>
      )}
      </div>
      
      {/* Rating panel */}
      {showRating && (
        <RateComment
          messageId={message.id || ''}
          initialRating={rating}
          onSave={handleSaveRating}
          onClose={() => setShowRating(false)}
        />
      )}
    </>
  );
};