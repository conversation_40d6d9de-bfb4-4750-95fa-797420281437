'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
// import Fuse from 'fuse.js'; // Temporarily removed to avoid dependency
import { Zap, Sparkles } from 'lucide-react';

interface SuggestionPrompt {
  id?: string;
  title?: string;
  content: string;
  category?: string;
}

interface SuggestionsProps {
  suggestions: SuggestionPrompt[] | any[];
  inputValue?: string;
  onSelect: (content: string) => void;
  className?: string;
  showHeader?: boolean;
  maxHeight?: string;
}

export const Suggestions: React.FC<SuggestionsProps> = ({
  suggestions = [],
  inputValue = '',
  onSelect,
  className,
  showHeader = true,
  maxHeight = 'h-40'
}) => {
  const [sortedPrompts, setSortedPrompts] = useState<SuggestionPrompt[]>([]);

  // Initialize and shuffle prompts
  useEffect(() => {
    if (suggestions.length > 0) {
      const shuffled = [...suggestions].sort(() => Math.random() - 0.5);
      setSortedPrompts(shuffled);
    }
  }, [suggestions]);

  // Always show all suggestions regardless of input
  const filteredPrompts = useMemo(() => {
    return sortedPrompts;
  }, [sortedPrompts]);

  const handleSelect = (prompt: SuggestionPrompt | any) => {
    const content = prompt.content || prompt;
    onSelect(content);
  };

  const getPromptTitle = (prompt: SuggestionPrompt | any) => {
    if (prompt.title) {
      if (Array.isArray(prompt.title)) {
        return prompt.title.join(' ');
      }
      return prompt.title;
    }
    return prompt.content?.slice(0, 50) + (prompt.content?.length > 50 ? '...' : '');
  };

  const getPromptContent = (prompt: SuggestionPrompt | any) => {
    return prompt.content || prompt;
  };

  return (
    <div className={cn('w-full', className)}>
      {/* Header */}
      {showHeader && (
        <div className="mb-1 flex gap-1 text-xs font-medium items-center text-gray-400 ">
          {filteredPrompts.length > 0 ? (
            <>
              <Zap className="w-3 h-3" />
              <span>Suggested</span>
            </>
          ) : (
            <div className="flex w-full text-center items-center justify-center self-start text-gray-400 ">
              <span>Open WebUI ‧ v0.3.32</span>
            </div>
          )}
        </div>
      )}

      {/* Suggestions List */}
      <ScrollArea className={cn(maxHeight, 'overflow-auto scrollbar-none items-start')}>
        {filteredPrompts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {filteredPrompts.map((prompt, idx) => (
              <Button
                key={prompt.id || prompt.content || idx}
                variant="ghost"
                className={cn(
                  'waterfall flex flex-col flex-1 w-full justify-start items-start',
                  'px-4 py-3 h-auto text-left bg-white/60 hover:bg-white/80 border border-gray-100',
                  'rounded-lg shadow-sm hover:shadow-md transition-all duration-200 group'
                )}
                style={{ animationDelay: `${idx * 60}ms` }}
                onClick={() => handleSelect(prompt)}
              >
                <div className="flex flex-col text-left w-full">
                  {/* Title */}
                  {prompt.title && Array.isArray(prompt.title) ? (
                    <>
                      {/* Main title - first element */}
                      <div className="font-medium text-gray-800 transition truncate mb-1">
                        {prompt.title[0]}
                      </div>
                      {/* Subtitle - second element */}
                      {prompt.title[1] && (
                        <div className="text-sm text-gray-500 transition truncate mb-2">
                          {prompt.title[1]}
                        </div>
                      )}
                    </>
                  ) : prompt.title ? (
                    <div className="font-medium text-gray-800 transition truncate mb-1">
                      {prompt.title}
                    </div>
                  ) : null}
                  
                  {/* Content - now hidden since we're showing title properly */}
                </div>
              </Button>
            ))}
          </div>
        ) : inputValue.trim() ? (
          <div className="flex items-center justify-center h-20 text-sm text-gray-500 ">
            No suggestions found
          </div>
        ) : null}
      </ScrollArea>

      {/* CSS for waterfall animation */}
      <style jsx>{`
        .waterfall {
          opacity: 0;
          transform: translateY(10px);
          animation: waterfall 0.3s ease-out forwards;
        }

        @keyframes waterfall {
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
};
