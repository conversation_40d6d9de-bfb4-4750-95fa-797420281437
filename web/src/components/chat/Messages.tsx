'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useAuthStore } from '@/lib/stores';
import { Spinner } from '@/components/common';
import { toast } from 'sonner';
import { ChatPlaceholder } from './ChatPlaceholder';
import { LoadingSkeleton } from './LoadingSkeleton';
import { ThinkingSkeleton } from './Messages/Skeleton';
import { MarkdownRenderer } from './Messages/MarkdownRenderer';
import { MessageActions } from './Messages/MessageActions';
import { cn } from '@/lib/utils';
import type { Message, Chat } from '@/lib/types';

interface MessagesProps {
  chatId?: string;
  chat?: Chat | null;
  messages?: Message[];
  selectedModels: string[];
  prompt: string;
  onPromptChange: (prompt: string) => void;
  onSubmit: (data: any) => void;
  onUpdateMessage?: (messageId: string, newContent: string) => void;
  onRegenerateMessage?: (messageId: string) => void;
  onContinueGeneration?: (messageId: string) => void;
  readOnly?: boolean;
  autoScroll?: boolean;
  bottomPadding?: boolean;
  className?: string;
  temporaryChatEnabled?: boolean;
  isHistoryLoading?: boolean;
  isCreatingNewChat?: boolean;
  isSubmittingMessage?: boolean;
}

export const Messages: React.FC<MessagesProps> = ({
  chatId,
  chat,
  messages = [],
  selectedModels,
  prompt,
  onPromptChange,
  onSubmit,
  onUpdateMessage,
  onRegenerateMessage,
  onContinueGeneration,
  readOnly = false,
  autoScroll = true,
  bottomPadding = false,
  className = 'h-full flex flex-col',
  temporaryChatEnabled = false,
  isHistoryLoading = false,
  isCreatingNewChat = false,
  isSubmittingMessage = false
}) => {
  const { user } = useAuthStore();
  const [messagesCount, setMessagesCount] = useState(20);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const bottomRef = useRef<HTMLDivElement>(null);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const previousMessagesLength = useRef(messages.length);

  // Track if user is manually scrolling
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const element = e.currentTarget;
    const { scrollTop, scrollHeight, clientHeight } = element;
    
    // Check if user scrolled away from bottom (with some tolerance)
    const isAtBottom = scrollHeight - scrollTop - clientHeight < 100;
    
    // Update auto-scroll behavior based on scroll position
    if (!isAtBottom && !isUserScrolling) {
      setIsUserScrolling(true);
      setShouldAutoScroll(false);
    } else if (isAtBottom && isUserScrolling) {
      setIsUserScrolling(false);
      setShouldAutoScroll(true);
    }
    
    // Load more messages when scrolled to top
    if (element.scrollTop === 0 && messages.length >= messagesCount && !messagesLoading) {
      loadMoreMessages();
    }
  }, [messages.length, messagesCount, messagesLoading, isUserScrolling]);

  // Auto scroll to bottom only when appropriate
  useEffect(() => {
    // Only auto-scroll if:
    // 1. autoScroll is enabled
    // 2. shouldAutoScroll is true (user hasn't manually scrolled up)
    // 3. bottomPadding is true (indicating content is present)
    // 4. We have new messages (length increased)
    const hasNewMessages = messages.length > previousMessagesLength.current;
    
    if (autoScroll && shouldAutoScroll && bottomPadding && bottomRef.current && hasNewMessages) {
      bottomRef.current.scrollIntoView({ behavior: 'smooth' });
    }
    
    // Update previous messages length
    previousMessagesLength.current = messages.length;
  }, [messages.length, autoScroll, shouldAutoScroll, bottomPadding]);

  // Reset scroll behavior when loading new chat
  useEffect(() => {
    if (isHistoryLoading || isCreatingNewChat) {
      setIsUserScrolling(false);
      setShouldAutoScroll(true);
    }
  }, [isHistoryLoading, isCreatingNewChat]);

  const scrollToBottom = useCallback(() => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  }, []);

  const loadMoreMessages = useCallback(async () => {
    if (messagesLoading) return;

    // Scroll slightly down to disable continuous loading
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop += 100;
    }

    setMessagesLoading(true);
    setMessagesCount(prev => prev + 20);

    // Simulate loading delay
    setTimeout(() => {
      setMessagesLoading(false);
    }, 500);
  }, [messagesLoading]);

  // Display messages (limited by messagesCount)
  const displayMessages = messages.slice(-messagesCount);
  

  // Show placeholder when no messages AND not in any loading state
  if (!messages.length && !isHistoryLoading && !isCreatingNewChat && !isSubmittingMessage) {
    // Show ChatPlaceholder only for true new chats, not when loading or creating
    // console.log('📄 [Messages] Showing ChatPlaceholder - no messages, not in any loading state');
    return (
      <div className={cn(className)}>
        <ChatPlaceholder
          selectedModels={selectedModels}
          prompt={prompt}
          onPromptChange={onPromptChange}
          onSubmit={onSubmit}
          temporaryChatEnabled={temporaryChatEnabled}
        />
      </div>
    );
  }

  // Show loading state when in any loading state and no messages
  if ((isHistoryLoading || isCreatingNewChat || isSubmittingMessage) && !messages.length) {
    console.log('⏳ [Messages] Showing loading spinner - isHistoryLoading:', isHistoryLoading, 'isCreatingNewChat:', isCreatingNewChat, 'isSubmittingMessage:', isSubmittingMessage);
    return (
      <div className={cn(className, "flex items-center justify-center")}>
        <Spinner />
      </div>
    );
  }

  return (
    <div className={cn(className)}>
      <div
        ref={messagesContainerRef}
        id="messages-container"
        className="flex-1 overflow-y-auto"
        onScroll={handleScroll}
      >
        {/* Loading indicator for more messages */}
        {messagesLoading && (
          <div className="flex justify-center py-4">
            <Spinner className="size-6" />
          </div>
        )}

        {/* Messages list */}
        <div className="space-y-4 pb-4">
          {displayMessages.map((message, index) => (
            <MessageComponent
              key={message.id || index}
              message={message}
              isLast={index === displayMessages.length - 1}
              readOnly={readOnly}
              onUpdateMessage={onUpdateMessage}
              onRegenerateMessage={onRegenerateMessage}
              onContinueGeneration={onContinueGeneration}
            />
          ))}
        </div>

        {/* Bottom padding and scroll anchor */}
        {bottomPadding && <div ref={bottomRef} className="h-32" />}
      </div>
    </div>
  );
};

// Check if model supports thinking functionality
const supportsThinking = (modelId?: string): boolean => {
  if (!modelId) return false;
  
  const modelIdLower = modelId.toLowerCase();
  
  // List of models that support thinking
  const thinkingModels = [
    'o1-preview',
    'o1-mini',
    'claude-3.5-sonnet',
    'claude-3-5-sonnet',
    'deepseek-r1',
    'deepseek-reasoning',
    'qwen-qvq',
    'marco-o1'
  ];
  
  // Check if model ID contains any of the thinking model names
  return thinkingModels.some(thinkingModel => modelIdLower.includes(thinkingModel));
};

// Detect whether message is in thinking state
const isThinking = (message: Message): boolean => {
  if (message.done || message.error) return false;
  
  // Only models that support thinking show thinking state
  if (!supportsThinking(message.model)) return false;
  
  // If no content at all, don't show thinking skeleton (wait for model to start returning content)
  if (!message.content) return false;
  
  // If only has reasoning content (details tags) and no other text content, also show skeleton
  const hasOnlyReasoning = message.content.trim() && 
    /^<details[^>]*type="reasoning"[\s\S]*<\/details>\s*$/.test(message.content.trim());
  
  return hasOnlyReasoning;
};

// Message Typing Indicator Component
interface MessageTypingIndicatorProps {
  message: Message;
  isTyping: boolean;
}

const MessageTypingIndicator: React.FC<MessageTypingIndicatorProps> = ({ message, isTyping }) => {
  // Show typing indicator when:
  // 1. Message is not done AND has content (streaming in progress)
  // 2. OR message is done but content is still being typed out by typewriter effect
  const shouldShowIndicator = !message.done && message.content;

  if (!shouldShowIndicator) {
    return null;
  }

  return (
    <span className="inline-block w-1 h-4 bg-current ml-1 animate-pulse" />
  );
};

// Individual Message Component
interface MessageComponentProps {
  message: Message;
  isLast: boolean;
  readOnly: boolean;
  onUpdateMessage?: (messageId: string, newContent: string) => void;
  onRegenerateMessage?: (messageId: string) => void;
  onContinueGeneration?: (messageId: string) => void;
}

const MessageComponent: React.FC<MessageComponentProps> = ({
  message,
  isLast,
  readOnly,
  onUpdateMessage,
  onRegenerateMessage,
  onContinueGeneration
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content || '');
  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';

  // Handle edit save
  const handleEditSave = () => {
    if (editContent.trim() && onUpdateMessage) {
      onUpdateMessage(message.id || '', editContent.trim());
      setIsEditing(false);
    }
  };

  // Handle edit cancel
  const handleEditCancel = () => {
    setEditContent(message.content || '');
    setIsEditing(false);
  };


  // Update editContent when message content changes
  useEffect(() => {
    setEditContent(message.content || '');
  }, [message.content]);
  
  // Check if message is loading (assistant message that's not done and has no content)
  const isInitialLoading = isAssistant && !message.done && !message.content;
  
  // Check if message is in thinking state
  const isMessageThinking = isThinking(message);
  
  // FIXED: More conservative approach - only skip truly empty streaming artifacts
  // Always render historical messages and messages with any meaningful content
  const hasAnyContent = message.content && message.content.trim().length > 0;
  const isHistoricalMessage = message.timestamp && message.timestamp > 0;
  const hasModelInfo = message.model && message.model.length > 0;
  
  // Only skip assistant messages that are:
  // 1. Completely empty (no content)
  // 2. Done processing
  // 3. Not currently loading or thinking
  // 4. Appear to be streaming artifacts (no timestamp, no model info)
  const shouldSkipMessage = isAssistant && 
    !hasAnyContent &&
    !isInitialLoading && 
    !isMessageThinking &&
    message.done &&
    !isHistoricalMessage &&
    !hasModelInfo;
    
  if (shouldSkipMessage) {
    return null;
  }

  return (
    <div className={cn(
      "flex w-full group",
      isUser ? "justify-end" : "justify-start"
    )}>
      {/* Show different skeletons based on message state */}
      {isInitialLoading ? (
        <LoadingSkeleton message={message} />
      ) : isMessageThinking ? (
        <ThinkingSkeleton message={message} />
      ) : (
        <div className="relative flex flex-col max-w-[80%]">
          <div className={cn(
            "rounded-2xl px-4 py-3",
            isUser 
              ? "bg-blue-600 text-white ml-auto" 
              : "bg-gray-100 text-gray-900 mr-auto"
          )}>
            {/* Message content or edit interface */}
            {isEditing ? (
              <div className="space-y-3">
                <textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  className={cn(
                    "w-full min-h-[80px] p-0 border-none bg-transparent resize-none focus:outline-none",
                    isUser 
                      ? "text-white placeholder-blue-200" 
                      : "text-gray-900 placeholder-gray-500"
                  )}
                  placeholder="Edit message..."
                  autoFocus
                  onInput={(e) => {
                    const target = e.target as HTMLTextAreaElement;
                    target.style.height = '';
                    target.style.height = `${target.scrollHeight}px`;
                  }}
                />
                <div className="flex gap-2 justify-end">
                  <button
                    onClick={handleEditCancel}
                    className={cn(
                      "px-3 py-1.5 text-sm rounded-full transition-colors",
                      isUser
                        ? "bg-blue-500 hover:bg-blue-400 text-white"
                        : "bg-gray-200 hover:bg-gray-300 text-gray-700"
                    )}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleEditSave}
                    className={cn(
                      "px-3 py-1.5 text-sm rounded-full transition-colors",
                      isUser
                        ? "bg-gray-400 text-black"
                        : "bg-blue-600 hover:bg-gray-800 text-white"
                    )}
                    style={{
                      color:'white'
                    }}
                  >
                    Save
                  </button>
                </div>
              </div>
            ) : (
              <div className="whitespace-pre-wrap break-words">
                <MessageContent 
                  content={message.content} 
                  isAssistant={isAssistant}
                  isDone={message.done || false}
                  isStreaming={(message as any).isStreaming || false}
                />
                {/* Show typing indicator for assistant messages that are typing */}
                {isAssistant && (
                  <MessageTypingIndicator 
                    message={message}
                    isTyping={true} // We'll manage this in the MessageTypingIndicator component
                  />
                )}
              </div>
            )}

            {/* Message metadata */}
            <div className={cn(
              "text-xs mt-2 opacity-70",
              isUser ? "text-blue-100" : "text-gray-500"
            )}>
              {message.timestamp && new Date(message.timestamp * 1000).toLocaleTimeString()}
              {message.model && ` • ${message.model}`}
              {isAssistant && !message.done && " • Generating..."}
            </div>
            {/* File attachments */}
            {message.files && message.files.length > 0 && (
              <div className="mt-2 space-y-1">
                {message.files.map((file, index) => (
                  <div
                    key={index}
                    className={cn(
                      "text-xs px-2 py-1 rounded",
                      isUser 
                        ? "bg-blue-500 text-blue-100" 
                        : "bg-gray-200 text-gray-600"
                    )}
                  >
                    📎 {file.name}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Message Actions */}
          <div className={cn(
            "flex mt-2",
            isUser ? "justify-end" : "justify-start"
          )}>
            <MessageActions
              message={message}
              onCopy={() => {}}
              onEdit={() => {
                setIsEditing(true);
              }}
              onRegenerate={isAssistant && message.done && onRegenerateMessage ? () => {
                onRegenerateMessage(message.id || '');
              } : undefined}
              onReadAloud={isAssistant ? () => {
                // Use Web Speech API to read message content
                if (message.content) {
                  const utterance = new SpeechSynthesisUtterance(message.content);
                  utterance.rate = 0.8;
                  utterance.pitch = 1;
                  utterance.volume = 1;
                  
                  // Stop any ongoing speech
                  window.speechSynthesis.cancel();
                  
                  // Start speaking
                  window.speechSynthesis.speak(utterance);
                  
                  toast.success('Reading message aloud...');
                }
              } : undefined}
              onContinue={isAssistant && onContinueGeneration && message.content && !message.done ? () => {
                onContinueGeneration(message.id || '');
              } : undefined}
            />
          </div>
        </div>
      )}

    </div>
  );
};

// Message Content Component with Markdown rendering
interface MessageContentProps {
  content: string;
  isAssistant: boolean;
  isDone: boolean;
  isStreaming?: boolean;
}

const MessageContent: React.FC<MessageContentProps> = ({ content, isAssistant, isDone, isStreaming = false }) => {
  const [displayedContent, setDisplayedContent] = useState('');
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const lastContentRef = useRef('');
  const targetLengthRef = useRef(0);
  const isTypingRef = useRef(false);

  // Create a stable typeNextChar function using useCallback
  const typeNextChar = useCallback(() => {
    if (!isTypingRef.current) return;
    
    setDisplayedContent(prevDisplayed => {
      const currentDisplayed = prevDisplayed.length;
      const currentTarget = targetLengthRef.current;
      const currentContent = lastContentRef.current;
      
      if (currentDisplayed < currentTarget && currentDisplayed < currentContent.length) {
        const newDisplayed = currentContent.slice(0, currentDisplayed + 1);
        
        // Schedule next character
        let speed;
        if (currentContent.length > 500) {
          speed = 80;
        } else if (currentContent.length > 200) {
          speed = 100;
        } else {
          speed = 120;
        }
        
        // Continue typing if there's more to type
        if (currentDisplayed + 1 < currentTarget && currentDisplayed + 1 < currentContent.length && isTypingRef.current) {
          timerRef.current = setTimeout(typeNextChar, speed);
        } else {
          isTypingRef.current = false;
        }
        
        return newDisplayed;
      }
      
      isTypingRef.current = false;
      return prevDisplayed;
    });
  }, []);

  // Smart typewriter effect that adapts to streaming
  useEffect(() => {
    // Clear any existing timer
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    isTypingRef.current = false;

    // Only apply typewriter effect to assistant messages
    if (!isAssistant) {
      setDisplayedContent(content || '');
      lastContentRef.current = content || '';
      targetLengthRef.current = content?.length || 0;
      return;
    }

    // CRITICAL: For historical messages (completed and not currently streaming), show immediately
    if (isDone && !isStreaming) {
      setDisplayedContent(content || '');
      lastContentRef.current = content || '';
      targetLengthRef.current = content?.length || 0;
      return;
    }

    // If no content, show empty
    if (!content) {
      setDisplayedContent('');
      lastContentRef.current = '';
      targetLengthRef.current = 0;
      return;
    }

    // Only proceed if content has actually changed
    if (content === lastContentRef.current) {
      return;
    }

    lastContentRef.current = content;

    // Determine target length based on streaming state
    let newTargetLength;
    if (isStreaming && !isDone) {
      if (content.length <= 20) {
        newTargetLength = Math.max(1, content.length - 2);
      } else {
        newTargetLength = Math.max(5, content.length - 8);
      }
    } else {
      newTargetLength = content.length;
    }

    targetLengthRef.current = newTargetLength;

    // Start typing if we need to catch up
    const currentDisplayed = displayedContent.length;
    if (currentDisplayed < newTargetLength && currentDisplayed < content.length && !isTypingRef.current) {
      isTypingRef.current = true;
      typeNextChar();
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
      isTypingRef.current = false;
    };
  }, [content, isAssistant, isDone, isStreaming, typeNextChar]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  // Show typewriter content for assistant messages, full content for user messages
  const contentToShow = isAssistant ? displayedContent : content;


  // For user messages, always show content as plain text
  if (!isAssistant) {
    return <>{contentToShow || ''}</>;
  }
  
  // For assistant messages with no content, show a placeholder
  if (!contentToShow || contentToShow.trim() === '') {
    return <span className="text-gray-400 italic">No response recorded</span>;
  }

  // For assistant messages, render as Markdown using the enhanced MarkdownRenderer
  return (
    <MarkdownRenderer 
      content={contentToShow}
      className="text-gray-900"
    />
  );
};
