'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { Square, Paperclip, ChevronDown, Globe } from 'lucide-react';
import { Select } from 'antd';
import { useAppStore, useAuthStore } from '@/lib/stores';
import { uploadFile } from '@/lib/api';
import Image from 'next/image';
import { LanguageMenu } from './LanguageMenu';








interface MessageInputProps {
  prompt: string;
  onPromptChange: (prompt: string) => void;
  onSubmit: (data: any) => void;
  placeholder?: string;
  disabled?: boolean;
  transparentBackground?: boolean;
  selectedModels?: string[];
  onModelsChange?: (models: string[]) => void;
  files?: any[];
  onFilesChange?: (files: any[]) => void;
  className?: string;
  isGenerating?: boolean;
  onStop?: () => void;
}

export const MessageInput: React.FC<MessageInputProps> = ({
  prompt,
  onPromptChange,
  onSubmit,
  placeholder = "Ask anything",
  disabled = false,
  transparentBackground = false,
  selectedModels = [],
  onModelsChange,
  files = [],
  onFilesChange,
  className,
  isGenerating = false,
  onStop
}) => {
  const { models } = useAppStore();
  const { token } = useAuthStore();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLanguageMenuOpen, setIsLanguageMenuOpen] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const languageButtonRef = useRef<HTMLButtonElement>(null);

  // Handle model selection
  const handleModelSelect = (modelId: string) => {
    if (onModelsChange) {
      onModelsChange([modelId]);
    }
  };

  // Handle language menu
  const handleLanguageButtonClick = () => {
    setIsLanguageMenuOpen(!isLanguageMenuOpen);
  };

  const handleLanguageSelect = (languageCode: string) => {
    setSelectedLanguage(languageCode);
    // You can add additional logic here to handle language change
    console.log('Selected language:', languageCode);
  };

  const handleLanguageMenuClose = () => {
    setIsLanguageMenuOpen(false);
  };



  const handleSubmit = useCallback(async () => {
    // Prevent multiple simultaneous submissions
    if (isSubmitting || disabled || isGenerating) {
      return;
    }

    if (!prompt.trim() && files.length === 0) {
      toast.error('Please enter a prompt');
      return;
    }

    // Check if models are selected and not empty
    if (!selectedModels.length || selectedModels.includes('')) {
      toast.error('Model not selected');
      return;
    }

    setIsSubmitting(true);

    try {
      await onSubmit({
        prompt: prompt.trim(),
        files,
        selectedModels
      });

      // Note: Input clearing is now handled by parent Chat component for immediate UX
    } catch (error) {
      console.error('Failed to submit message:', error);
      // Input is already cleared by parent, so user needs to retype on error
    } finally {
      // Reset submitting state after a short delay
      setTimeout(() => {
        setIsSubmitting(false);
      }, 500);
    }
  }, [prompt, files, selectedModels, onSubmit, onPromptChange, onFilesChange, isSubmitting, disabled, isGenerating]);

  // Process image file to Base64 URL (like original project)
  const processImageFile = useCallback((file: File): Promise<any> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = async (event) => {
        const imageUrl = event.target?.result as string;
        
        // TODO: Add image compression logic if needed
        // if (settings?.imageCompression) {
        //     imageUrl = await compressImage(imageUrl, width, height);
        // }
        
        resolve({
          id: Math.random().toString(36).substring(2, 11),
          type: 'image',
          url: imageUrl, // Base64 data URL for AI model
          name: file.name,
          size: file.size
        });
      };
      reader.readAsDataURL(file);
    });
  }, []);

  const handleFileUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFiles = Array.from(e.target.files || []);
    
    if (uploadedFiles.length === 0) return;

    for (const file of uploadedFiles) {
      try {
        // Handle image files specially (like original project)
        if (['image/gif', 'image/webp', 'image/jpeg', 'image/png', 'image/avif'].includes(file.type)) {
          // TODO: Check vision model support
          // if (visionCapableModels.length === 0) {
          //     toast.error('Selected model(s) do not support image inputs');
          //     return;
          // }
          
          const imageFile = await processImageFile(file);
          if (onFilesChange) {
            onFilesChange([...files, imageFile]);
          }
        } else {
          // Handle non-image files: upload to server first
          if (!token) {
            toast.error('Please sign in to upload files');
            continue;
          }

          // Create loading file and add to state immediately
          const tempFileId = Math.random().toString(36).substring(2, 11);
          const loadingFile = {
            id: tempFileId,
            name: file.name,
            type: 'file',
            size: file.size,
            status: 'uploading' as const
          };

          if (onFilesChange) {
            onFilesChange([...files, loadingFile]);
          }

          try {
            // Upload file to server
            const uploadedFile = await uploadFile(token, file);
            
            // Create file object matching the expected format
            const serverFile = {
              id: uploadedFile.id,
              name: uploadedFile.filename,
              type: 'file', // Server file type
              size: uploadedFile.size,
              url: `/api/v1/files/${uploadedFile.id}`, // Server URL for the file
              collection_name: uploadedFile.filename,
              meta: uploadedFile.meta,
              status: 'uploaded' as const
            };

            // Replace loading file with successful upload
            if (onFilesChange) {
              onFilesChange(prevFiles => prevFiles.map(f => 
                f.id === tempFileId ? serverFile : f
              ));
            }

            toast.success(`Successfully uploaded ${file.name}`);
          } catch (uploadError) {
            console.error('File upload failed:', uploadError);
            toast.error(`Failed to upload ${file.name}: ${uploadError instanceof Error ? uploadError.message : String(uploadError)}`);
            
            // Remove loading file on error
            if (onFilesChange) {
              onFilesChange(prevFiles => prevFiles.filter(f => f.id !== tempFileId));
            }
          }
        }
      } catch (error) {
        console.error('File processing failed:', error);
        toast.error(`Failed to process ${file.name}`);
      }
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [files, onFilesChange, processImageFile, token]);

  const removeFile = useCallback((fileId: string) => {
    if (onFilesChange) {
      onFilesChange(files.filter(file => file.id !== fileId));
    }
  }, [files, onFilesChange]);





  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  }, [handleSubmit]);

  return (
    <div className={cn(
      "w-full relative",
      transparentBackground ? "bg-transparent" : "bg-white",
      className
    )}>

      {/* Input container */}
      <div
        className="relative bg-white flex h-full w-full flex-col min-w-0 overflow-hidden rounded-[12px] border"
        style={{
          borderColor: '#E9E9EA',
          boxShadow: '0px 10px 10px -5px rgba(0, 0, 0, 0.02), 0px 20px 25px -5px rgba(0, 0, 0, 0.02)'
        }}
      >
        {/* File attachments display - inside dialog */}
        {files.length > 0 && (
          <div className="flex flex-wrap gap-3 p-4 pb-2 bg-gray-50 border-b border-gray-100">
            {files.map((file) => (
              <div
                key={file.id}
                className="relative flex items-center gap-2 bg-white p-2 rounded-lg shadow-sm border"
              >
                {file.type === 'image' && file.url ? (
                  // Image preview for image files
                  <div className="flex items-center gap-2">
                    <img 
                      src={file.url} 
                      alt={file.name}
                      className="w-12 h-12 object-cover rounded-md"
                    />
                    <div className="flex flex-col">
                      <span className="text-sm font-medium truncate max-w-24">{file.name}</span>
                      <span className="text-xs text-gray-500">{(file.size / 1024 / 1024).toFixed(1)}MB</span>
                    </div>
                  </div>
                ) : (
                  // Generic file display for non-image files
                  <div className="flex items-center gap-2">
                    <div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center">
                      {file.status === 'uploading' ? (
                        <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-500 border-t-transparent"></div>
                      ) : (
                        <Paperclip className="h-5 w-5 text-gray-500" />
                      )}
                    </div>
                    <div className="flex flex-col">
                      <span className="text-sm font-medium truncate max-w-24">{file.name}</span>
                      <div className="text-xs text-gray-500">
                        {file.status === 'uploading' ? (
                          <span className="text-blue-500">Uploading...</span>
                        ) : file.status === 'error' ? (
                          <span className="text-red-500">Upload failed</span>
                        ) : (
                          <span>{(file.size / 1024 / 1024).toFixed(1)}MB</span>
                        )}
                      </div>
                    </div>
                  </div>
                )}
                <button
                  onClick={() => removeFile(file.id)}
                  className="absolute -top-1 -right-1 w-5 h-5 bg-gray-500 hover:bg-gray-600 text-white rounded-full flex items-center justify-center text-xs transition-colors"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}

        <div className="w-full">
          {/* Text input */}
          <textarea
            value={prompt}
            onChange={(e) => onPromptChange(e.target.value)}
            placeholder={placeholder}
            className="h-24 p-[16px] pb-0 w-full min-w-0 resize-none border-none outline-none bg-transparent overflow-hidden"
            onKeyDown={handleKeyDown}
            style={{ resize: 'none' }}
          />
        </div>
        <div className="flex items-center justify-between p-[16px] pt-[8px] min-w-0 overflow-hidden">
          {/* Left group */}
          <div className="flex items-center gap-[6px]">
            {/* File upload button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => fileInputRef.current?.click()}
              className="w-[32px] h-[32px] rounded-[8px] bg-[#F7F8FA] hover:bg-[#EAEAF2]"
              title="Upload files"
            >
              <Paperclip className="h-4 w-4 text-[#2E2F41]" />
            </Button>

            {/* Hidden file input */}
            <input
              ref={fileInputRef}
              type="file"
              multiple
              onChange={handleFileUpload}
              className="hidden"
              accept="image/*,text/*,.pdf,.doc,.docx"
            />

            {/* Multi-language search button */}
            <Button
              ref={languageButtonRef}
              variant="ghost"
              className="h-[32px] px-[6px] rounded-[8px] bg-[#F7F8FA] hover:bg-[#EAEAF2] flex items-center gap-[4px]"
              title="Multi-language search"
              onClick={handleLanguageButtonClick}
              style={{display:'none'}}
            >
              <Globe className="h-4 w-4 text-[#2E2F41]" />
              <span className="text-sm text-[#2E2F41]">Multi-language search</span>
            </Button>
          </div>

          {/* Right group */}
          <div className="flex items-center gap-[8px]">
            {/* Model dropdown */}
            <div className="flex items-center">
              <Image
                src="/modelIcon.svg"
                alt="Model Icon"
                width={14}
                height={15}
                className="shrink-0"
              />
              <Select
                value={selectedModels[0] || undefined}
                onChange={handleModelSelect}
                placeholder="ChatGPT 4o"
                disabled={disabled}
                style={{ width: 'auto', minWidth: 'fit-content' }}
                className="model-selector"
                suffixIcon={<ChevronDown className="h-4 w-4" />}
                styles={{
                  popup: {
                    root: { width: '300px'}
                  }
                }}
              >
              {models && models.length > 0 ? (
                models.map((model) => (
                  <Select.Option key={model.id} value={model.id}>
                    <span className="truncate">{model.name}</span>
                  </Select.Option>
                ))
              ) : (
                <Select.Option disabled value="">
                  No models available
                </Select.Option>
              )}
            </Select>
            </div>

            {/* Send/Stop button */}
            <Button
              onClick={isGenerating ? onStop : handleSubmit}
              disabled={!isGenerating && (disabled || isSubmitting)}
              variant="ghost"
              size="icon"
              className={cn(
                "w-[32px] h-[32px] rounded-[8px] transition-colors",
                (!isGenerating && (disabled || isSubmitting))
                  ? "bg-gray-200" // 真正禁用时的灰色
                  : (isGenerating || isSubmitting)
                    ? "bg-[#625DEC] hover:bg-[#5A55E0]" // 生成中或提交中的深紫色（暂停按钮）
                    : (prompt.trim() || files.length > 0)
                      ? "bg-[#625DEC] hover:bg-[#5A55E0]" // 有输入时的深紫色
                      : "bg-[#B6BAFB] hover:bg-[#A5AAFA]"  // 无输入时的浅紫色
              )}
              title={isGenerating ? "Stop generation" : isSubmitting ? "Sending..." : "Send message"}
            >
              {isGenerating ? (
                <Square className="w-4 h-4 text-white fill-current" />
              ) : (
                <Image
                  src="/Send.svg"
                  alt="Send"
                  width={16}
                  height={16}
                  className="text-white"
                />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Language Menu - positioned using fixed positioning */}
      <LanguageMenu
        isOpen={isLanguageMenuOpen}
        onClose={handleLanguageMenuClose}
        onLanguageSelect={handleLanguageSelect}
        selectedLanguage={selectedLanguage}
        buttonRef={languageButtonRef}
      />
    </div>
  );
};
