import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAuthStore } from "@/lib/stores";

interface AgentWelcomeProps {
  className?: string;
  onSend?: (message: string, options?: any) => void;
  onFillInput?: (content: string) => void;
}

export function AgentWelcome({ className, onSend, onFillInput }: AgentWelcomeProps) {
  const user = useAuthStore((state) => state.user);
  return (
    <motion.div
      className={cn("flex flex-col", className)}
      style={{ transition: "all 0.2s ease-out" }}
      initial={{ opacity: 0, scale: 0.85 }}
      animate={{ opacity: 1, scale: 1 }}
    >
      <div className="text-left">
        <div className="bg-[#E8E9FF]">
        <h1 className="text-3xl font-medium text-[#5C62FF] mb-1 with-[fit-content] ">
          Hey, {user?.name || 'there'}! 👋
        </h1>
        </div>
        <h2 className="text-xl font-normal text-[#8B93B8]">
          What can I help you with?
        </h2>
      </div>
    </motion.div>
  );
}
