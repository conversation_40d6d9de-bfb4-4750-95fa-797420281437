import { X, ChevronLeft, ChevronRight } from "lucide-react";
import { useCallback, forwardRef, useImperativeHandle, useRef, useState, useEffect, useMemo } from "react";
// Resource type definition - keeping it local for now
interface Resource {
  uri: string;
  title: string;
}

export interface UploadedFile {
  id: string;
  name: string;
  size: string;
  type: string;
  file: File;
}

export interface KnowledgeFile {
  id: string;
  title: string;
  size: string;
  type: 'knowledge';
  uri: string;
}

export interface UploadedFilesRef {
  addFiles: (files: FileList) => void;
  addKnowledgeFiles: (knowledgeFiles: Array<KnowledgeFile>) => void;
  clearFiles: () => void;
  clearKnowledgeFiles: () => void;
  getFileResources: () => Array<Resource>;
}

interface UploadedFilesProps {
  files: Array<UploadedFile>;
  knowledgeFiles: Array<KnowledgeFile>;
  onFilesChange: (files: Array<UploadedFile>) => void;
  onKnowledgeFilesChange: (knowledgeFiles: Array<KnowledgeFile>) => void;
  className?: string;
}

export const UploadedFiles = forwardRef<UploadedFilesRef, UploadedFilesProps>(({
  files,
  knowledgeFiles,
  onFilesChange,
  onKnowledgeFilesChange,
  className = ""
}, ref) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const scrollbarRef = useRef<HTMLDivElement>(null);
  const thumbRef = useRef<HTMLDivElement>(null);
  const scrollState = useRef({ isDragging: false, startX: 0, scrollStartLeft: 0 });

  const [thumbWidth, setThumbWidth] = useState(0);
  const [thumbLeft, setThumbLeft] = useState(0);
  const [showScrollbar, setShowScrollbar] = useState(false);

  // 合并所有文件用于展示
  const allItems = useMemo(() => {
    const regularFiles = files.map(file => ({ ...file, itemType: 'file' as const }));
    const knowledge = knowledgeFiles.map(kf => ({ ...kf, itemType: 'knowledge' as const }));
    return [...regularFiles, ...knowledge] as Array<(UploadedFile & { itemType: 'file' }) | (KnowledgeFile & { itemType: 'knowledge' })>;
  }, [files, knowledgeFiles]);

  const handleScroll = useCallback(() => {
    const el = scrollContainerRef.current;
    if (el) {
      const { scrollLeft, scrollWidth, clientWidth } = el;
      if (scrollWidth <= clientWidth) {
        setShowScrollbar(false);
        return;
      }
      setShowScrollbar(true);
      const scrollbarWidth = scrollbarRef.current?.clientWidth || 0;
      const newThumbWidth = (clientWidth / scrollWidth) * scrollbarWidth;
      const maxThumbLeft = scrollbarWidth - newThumbWidth;
      const newThumbLeft = (scrollLeft / (scrollWidth - clientWidth)) * maxThumbLeft;
      
      setThumbWidth(newThumbWidth);
      setThumbLeft(newThumbLeft);
    }
  }, []);

  useEffect(() => {
    const el = scrollContainerRef.current;
    if (el) {
      handleScroll();
      el.addEventListener('scroll', handleScroll);
      const resizeObserver = new ResizeObserver(handleScroll);
      resizeObserver.observe(el);
      resizeObserver.observe(document.body);

      return () => {
        el.removeEventListener('scroll', handleScroll);
        resizeObserver.disconnect();
      };
    }
  }, [allItems, handleScroll]);

  const handleThumbMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    const el = scrollContainerRef.current;
    if (el) {
      scrollState.current = {
        isDragging: true,
        startX: e.clientX,
        scrollStartLeft: el.scrollLeft,
      };
      document.addEventListener('mousemove', handleThumbMouseMove);
      document.addEventListener('mouseup', handleThumbMouseUp);
    }
  };

  const handleThumbMouseMove = (e: MouseEvent) => {
    if (!scrollState.current.isDragging) return;
    const el = scrollContainerRef.current;
    const scrollbarEl = scrollbarRef.current;
    if (el && scrollbarEl) {
      const deltaX = e.clientX - scrollState.current.startX;
      const scrollbarWidth = scrollbarEl.clientWidth;
      const currentThumbWidth = (el.clientWidth / el.scrollWidth) * scrollbarWidth;
      const scrollableDist = scrollbarWidth - currentThumbWidth;
      
      const scrollPercent = deltaX / scrollableDist;
      const scrollDist = (el.scrollWidth - el.clientWidth) * scrollPercent;
      
      el.scrollLeft = scrollState.current.scrollStartLeft + scrollDist;
    }
  };

  const handleThumbMouseUp = () => {
    scrollState.current.isDragging = false;
    document.removeEventListener('mousemove', handleThumbMouseMove);
    document.removeEventListener('mouseup', handleThumbMouseUp);
  };
  
  const scrollLeft = () => {
    scrollContainerRef.current?.scrollBy({ left: -200, behavior: 'smooth' });
  };

  const scrollRight = () => {
    scrollContainerRef.current?.scrollBy({ left: 200, behavior: 'smooth' });
  };

  const getFileTypeInfo = useCallback((fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase() || '';
    const iconPath = '/icon/files/';
    switch (extension) {
      case 'pdf': return { icon: `${iconPath}files-pdf.svg` };
      case 'doc': return { icon: `${iconPath}files-doc.svg` };
      case 'docx': return { icon: `${iconPath}files-docx.svg` };
      case 'xls': return { icon: `${iconPath}files-xls.svg` };
      case 'xlsx': return { icon: `${iconPath}files-xlsx.svg` };
      case 'csv': return { icon: `${iconPath}files-csv.svg` };
      case 'ppt': return { icon: `${iconPath}files-ppt.svg` };
      case 'pptx': return { icon: `${iconPath}files-pptx.svg` };
      case 'zip': return { icon: `${iconPath}files-zip.svg` };
      case 'rar': return { icon: `${iconPath}files-rar.svg` };
      case 'txt': return { icon: `${iconPath}files-txt.svg` };
      case 'png': return { icon: `${iconPath}files-png.svg` };
      case 'jpg': return { icon: `${iconPath}files-jpg.svg` };
      case 'jpeg': return { icon: `${iconPath}files-jpg.svg` };
      case 'svg': return { icon: `${iconPath}files-svg.svg` };
      case 'mp3': return { icon: `${iconPath}files-mp3.svg` };
      case 'mp4': return { icon: `${iconPath}files-mp4.svg` };
      case 'wav': return { icon: `${iconPath}files-wav.svg` };
      case 'avi': return { icon: `${iconPath}files-avi.svg` };
      case 'mkv': return { icon: `${iconPath}files-mkv.svg` };
      case 'mpeg': return { icon: `${iconPath}files-mpeg.svg` };
      case 'fig': return { icon: `${iconPath}files-fig.svg` };
      case 'ai': return { icon: `${iconPath}files-ai.svg` };
      case 'psd': return { icon: `${iconPath}files-psd.svg` };
      case 'aep': return { icon: `${iconPath}files-aep.svg` };
      case 'indd': return { icon: `${iconPath}files-indd.svg` };
      default: return { icon: `${iconPath}files-doc.svg` }; // Default icon
    }
  }, []);

  // 知识文件图标组件
  const KnowledgeFileIcon = () => (
    <img src="/icon/files/knowledge.svg" alt="knowledge file icon" className="w-8 h-8" />
  );

  const addFiles = useCallback((fileList: FileList) => {
    const newFiles = Array.from(fileList).map(file => {
      const sizeInKB = Math.round(file.size / 1024);
      const sizeText = sizeInKB < 1024 ? `${sizeInKB}k` : `${Math.round(sizeInKB / 1024)}M`;
      return { id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`, name: file.name, size: sizeText, type: file.type, file };
    });
    onFilesChange([...files, ...newFiles]);
  }, [files, onFilesChange]);

  const addKnowledgeFiles = useCallback((newKnowledgeFiles: Array<KnowledgeFile>) => {
    onKnowledgeFilesChange([...knowledgeFiles, ...newKnowledgeFiles]);
  }, [knowledgeFiles, onKnowledgeFilesChange]);

  const clearFiles = useCallback(() => { onFilesChange([]); }, [onFilesChange]);

  const clearKnowledgeFiles = useCallback(() => { onKnowledgeFilesChange([]); }, [onKnowledgeFilesChange]);

  const getFileResources = useCallback((): Array<Resource> => {
    const fileResources = files.map(file => ({ uri: `file://${file.id}`, title: `${file.name} (${file.size})` }));
    const knowledgeResources = knowledgeFiles.map(kf => ({ uri: kf.uri, title: kf.title }));
    return [...fileResources, ...knowledgeResources];
  }, [files, knowledgeFiles]);

  const handleRemoveFile = useCallback((itemId: string, itemType: 'file' | 'knowledge') => {
    if (itemType === 'file') {
      onFilesChange(files.filter(f => f.id !== itemId));
    } else {
      onKnowledgeFilesChange(knowledgeFiles.filter(kf => kf.id !== itemId));
    }
  }, [files, knowledgeFiles, onFilesChange, onKnowledgeFilesChange]);

  useImperativeHandle(ref, () => ({ 
    addFiles, 
    addKnowledgeFiles,
    clearFiles, 
    clearKnowledgeFiles,
    getFileResources 
  }), [addFiles, addKnowledgeFiles, clearFiles, clearKnowledgeFiles, getFileResources]);

  if (allItems.length === 0) return null;

  return (
    <div 
      className={`flex flex-col rounded-b-xl border-x border-b ${className}`}
      style={{ backgroundColor: '#F7F7FC', borderColor: '#DCDFE6' }}
    >
      <div 
        ref={scrollContainerRef}
        className="flex items-end gap-2 overflow-x-auto no-scrollbar p-3"
      >
        {allItems.map((item) => {
          const isImage = item.itemType === 'file' && (item as UploadedFile).type.startsWith('image/');
          const isKnowledge = item.itemType === 'knowledge';
          
          if (isImage) {
            return (
              <div key={item.id} className="relative group cursor-pointer" style={{ width: '60px', height: '60px', flexShrink: 0 }}>
                <img src={URL.createObjectURL((item as UploadedFile).file)} alt={(item as UploadedFile).name} className="w-full h-full object-cover rounded-lg" />
                <button 
                  onClick={() => handleRemoveFile(item.id, item.itemType)} 
                  className="absolute top-[-4px] right-[-4px] w-5 h-5 bg-black rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X className="w-3 h-3 text-white" />
                </button>
              </div>
            );
          }

          return (
            <div 
              key={item.id} 
              className="relative flex items-center gap-2 p-2 bg-white rounded-lg border group cursor-pointer" 
              style={{ 
                borderColor: '#E5E5EF', 
                width: '180px', 
                height: '55px', 
                flexShrink: 0 
              }}
            >
              <>
                {isKnowledge ? (
                  <KnowledgeFileIcon />
                ) : (
                  <img src={getFileTypeInfo((item as UploadedFile).name).icon} alt={`${(item as UploadedFile).name} type icon`} className="w-8 h-8" />
                )}
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-normal truncate" style={{ color: '#343A3E' }}>
                    {isKnowledge ? (item as KnowledgeFile).title : (item as UploadedFile).name}
                  </div>
                  <div className="text-xs" style={{ color: '#9099B1' }}>
                    {item.size}
                  </div>
                </div>
                <button 
                  onClick={() => handleRemoveFile(item.id, item.itemType)} 
                  className="absolute top-[-4px] right-[-4px] w-5 h-5 bg-black rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X className="w-3 h-3 text-white" />
                </button>
              </>
            </div>
          );
        })}
      </div>
      {showScrollbar && (
        <div className="flex items-center gap-2 px-4 pb-2">
          <button onClick={scrollLeft} className="p-1 rounded hover:bg-black/10 disabled:opacity-50" disabled={thumbLeft <= 0}>
            <ChevronLeft className="h-4 w-4 text-gray-600" />
          </button>
          <div ref={scrollbarRef} className="flex-1 h-2 rounded-full">
            <div
              ref={thumbRef}
              className="h-full rounded-full cursor-pointer"
              style={{
                width: `${thumbWidth}px`,
                marginLeft: `${thumbLeft}px`,
                backgroundColor: '#E5E5EF',
              }}
              onMouseDown={handleThumbMouseDown}
            />
          </div>
          <button onClick={scrollRight} className="p-1 rounded hover:bg-black/10 disabled:opacity-50" disabled={thumbLeft >= (scrollbarRef.current?.clientWidth || 0) - thumbWidth}>
            <ChevronRight className="h-4 w-4 text-gray-600" />
          </button>
        </div>
      )}
    </div>
  );
});

UploadedFiles.displayName = "UploadedFiles";