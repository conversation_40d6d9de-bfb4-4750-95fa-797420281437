import { MagicWandIcon } from "@radix-ui/react-icons";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowUp, Lightbulb, Plus, X, Paperclip, BookOpen, ChevronRight } from "lucide-react";
import { useCallback, useMemo, useRef, useState, useEffect, forwardRef, useImperativeHandle } from "react";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import { UploadedFiles, type UploadedFile, type KnowledgeFile, type UploadedFilesRef } from "./UploadedFiles";

// Add tools 面板组件，直接定义在本文件
const tools = [
  {
    key: "hotel",
    label: "Hotel",
    icon: <img src="/images/hotel-icon.svg" className="w-[16px] h-[16px]" alt="Hotel" />,
    tool_name: "google_hotels_search"
  },
  {
    key: "event",
    label: "Event",
    icon: <img src="/images/event-icon.svg" className="w-[16px] h-[16px]" alt="Event" />,
    tool_name: "google_events_search"
  },
  {
    key: "weather",
    label: "Weather",
    icon: <img src="/images/weather-icon.svg" className="w-[16px] h-[16px]" alt="Weather" />,
    tool_name: "weather_tool"
  },
  {
    key: "timezone",
    label: "Timezone",
    icon: <img src="/images/timezone-icon.svg" className="w-[16px] h-[16px]" alt="Timezone" />,
    tool_name: "time_convert_tool"
  },
];

export interface InputBoxRef {
  fillContent: (content: string) => void;
  clearAllFiles: () => void;
}

export const AgentInputBox = forwardRef<InputBoxRef, {
  className?: string;
  size?: "large" | "normal";
  responding?: boolean;
  feedback?: { option: any } | null;
  onSend?: (
    message: string,
    options?: {
      interruptFeedback?: string;
      resources?: Array<any>;
      tools?: Array<string>;
    },
  ) => void;
  onCancel?: () => void;
  onRemoveFeedback?: () => void;
  onFileUpload?: (files: FileList) => void;
  onKnowledgeFileSelect?: (knowledgeFile: { id: string; title: string; size: string }) => void;
  hasFilesBelow?: boolean;
  uploadedFiles?: Array<UploadedFile>;
  knowledgeFiles?: Array<KnowledgeFile>;
  onUploadedFilesChange?: (files: Array<UploadedFile>) => void;
  onKnowledgeFilesChange?: (files: Array<KnowledgeFile>) => void;
}>(({ 
  className,
  responding,
  feedback,
  onSend,
  onCancel,
  onRemoveFeedback,
  onFileUpload,
  onKnowledgeFileSelect,
  hasFilesBelow,
  uploadedFiles = [],
  knowledgeFiles = [],
  onUploadedFilesChange,
  onKnowledgeFilesChange,
}, ref) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const feedbackRef = useRef<HTMLDivElement>(null);
  const addKnowledgeButtonRef = useRef<HTMLButtonElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const addToolsButtonRef = useRef<HTMLButtonElement>(null);
  const uploadedFilesRef = useRef<UploadedFilesRef>(null);
  
  const [selectedTools, setSelectedTools] = useState<Array<string>>([]);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [isEnhanceAnimating, setIsEnhanceAnimating] = useState(false);
  const [currentPrompt, setCurrentPrompt] = useState("");
  
  // Knowledge panel state
  const [showKnowledgePanel, setShowKnowledgePanel] = useState(false);
  const [showToolsPanel, setShowToolsPanel] = useState(false);
  const [knowledgeResources, setKnowledgeResources] = useState<Array<any>>([]);
  const [loadingResources, setLoadingResources] = useState(false);
  const [selectedKnowledgeIndex, setSelectedKnowledgeIndex] = useState(0);
  const [showResourcesList, setShowResourcesList] = useState(false);
  
  // Model Selector State
  const [selectedModel, setSelectedModel] = useState<"4o" | "nano">("4o");
  const [modelMenuOpen, setModelMenuOpen] = useState(false);
  const [hoveredModel, setHoveredModel] = useState<"4o" | "nano" | null>(null);

  // Expose methods through ref
  useImperativeHandle(ref, () => ({
    fillContent: (content: string) => {
      if (inputRef.current) {
        inputRef.current.value = content;
        setCurrentPrompt(content);
        inputRef.current.focus();
      }
    },
    clearAllFiles: () => {
      uploadedFilesRef.current?.clearFiles();
      uploadedFilesRef.current?.clearKnowledgeFiles();
    }
  }), []);

  const handleSendMessage = useCallback(
    (message: string, resources: Array<any> = []) => {
      if (responding) {
        onCancel?.();
      } else {
        if (message.trim() === "") {
          return;
        }
        if (onSend) {
          // 获取文件资源
          const fileResources = uploadedFilesRef.current?.getFileResources() || [];
          const allResources = [...resources, ...fileResources];
          
          onSend(message, {
            interruptFeedback: feedback?.option.value,
            resources: allResources,
            tools: selectedTools.map(tool => tools.find(t => t.key === tool)?.tool_name ?? ""),
          });
          onRemoveFeedback?.();
          setIsEnhanceAnimating(false);
          // 清空输入和文件
          setCurrentPrompt("");
          if (inputRef.current) {
            inputRef.current.value = "";
          }
        }
      }
    },
    [responding, onCancel, onSend, feedback, onRemoveFeedback, selectedTools],
  );

  const handleEnhancePrompt = useCallback(async () => {
    if (currentPrompt.trim() === "" || isEnhancing) {
      return;
    }

    setIsEnhancing(true);
    setIsEnhanceAnimating(true);

    try {
      // Mock enhance prompt - replace with actual API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      const enhancedPrompt = currentPrompt + " [Enhanced]";
      
      if (inputRef.current) {
        inputRef.current.value = enhancedPrompt;
        setCurrentPrompt(enhancedPrompt);
      }

      setTimeout(() => {
        setIsEnhanceAnimating(false);
      }, 1000);
    } catch (error) {
      console.error("Failed to enhance prompt:", error);
      setIsEnhanceAnimating(false);
    } finally {
      setIsEnhancing(false);
    }
  }, [currentPrompt, isEnhancing]);

  // Handle Add Knowledge button click
  const handleAddKnowledge = useCallback(() => {
    if (showKnowledgePanel) {
      setShowKnowledgePanel(false);
      setShowResourcesList(false);
    } else {
      setShowKnowledgePanel(true);
      setShowResourcesList(false);
    }
    setSelectedKnowledgeIndex(0);
  }, [showKnowledgePanel]);

  // Handle upload files
  const handleUploadFiles = useCallback(() => {
    setShowKnowledgePanel(false);
    fileInputRef.current?.click();
  }, []);

  // Handle file selection
  const handleFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      // 使用新的 UploadedFiles 组件
      uploadedFilesRef.current?.addFiles(files);
      // 保持兼容性
      onFileUpload?.(files);
    }
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [onFileUpload]);

  // Handle choose knowledge
  const handleChooseKnowledge = useCallback(async () => {
    setLoadingResources(true);
    setShowResourcesList(true);
    
    try {
      // Mock load resources - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      const mockResources = [
        { uri: "doc1", title: "Sample Document 1" },
        { uri: "doc2", title: "Sample Document 2" },
      ];
      setKnowledgeResources(mockResources);
    } catch (error) {
      console.error("Failed to load knowledge resources:", error);
      setKnowledgeResources([]);
    } finally {
      setLoadingResources(false);
    }
  }, []);

  const handleAddTools = useCallback(() => {
    if (showToolsPanel) {
      setShowToolsPanel(false);
      return;
    }
    setShowToolsPanel(true);
  }, [showToolsPanel]);

  // Handle resource selection
  const handleSelectKnowledge = useCallback((item: { id: string; label: string }) => {
    const knowledgeFile: KnowledgeFile = {
      id: item.id,
      title: item.label,
      size: "32k",
      type: 'knowledge',
      uri: item.id
    };
    
    // 使用新的 UploadedFiles 组件
    uploadedFilesRef.current?.addKnowledgeFiles([knowledgeFile]);
    
    // 保持兼容性
    if (onKnowledgeFileSelect) {
      onKnowledgeFileSelect({
        id: item.id,
        title: item.label,
        size: "32k"
      });
    }
    
    setShowKnowledgePanel(false);
    setShowResourcesList(false);
  }, [onKnowledgeFileSelect]);

  // Close panel when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        showKnowledgePanel &&
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        if (showResourcesList) {
          setShowResourcesList(false);
          setSelectedKnowledgeIndex(1);
        } else {
          setShowKnowledgePanel(false);
        }
      }
      
      if (
        showToolsPanel &&
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setShowToolsPanel(false);
      }
    }

    if (showKnowledgePanel || showToolsPanel) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }
  }, [showKnowledgePanel, showToolsPanel, showResourcesList]);

  const handleInputClick = useCallback(() => {
    if (showKnowledgePanel) {
      setShowKnowledgePanel(false);
      setShowResourcesList(false);
    }
    if (showToolsPanel) {
      setShowToolsPanel(false);
    }
  }, [showKnowledgePanel, showToolsPanel]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(currentPrompt);
    }
  }, [currentPrompt, handleSendMessage]);

  const hasFiles = uploadedFiles.length > 0 || knowledgeFiles.length > 0;

  return (
    <>
      <div
        className={cn(
          "bg-white relative flex h-full w-full flex-col min-w-0 overflow-hidden border-2 shadow-lg",
          hasFiles
            ? "rounded-t-[20px] border-x border-t"
            : "rounded-[20px] border",
          className,
        )}
        style={{
          borderColor: hasFiles ? '#D1D5DB' : '#D1D5DB',
          boxShadow: '0px 4px 6px -1px rgba(0, 0, 0, 0.1), 0px 2px 4px -1px rgba(0, 0, 0, 0.06)'
        }}
        ref={containerRef}
      >
      <div className="w-full">
        <AnimatePresence>
          {feedback && (
            <motion.div
              ref={feedbackRef}
              className="bg-background border-brand absolute top-0 left-0 mt-2 ml-4 flex items-center justify-center gap-1 rounded-2xl border px-2 py-0.5"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
              transition={{ duration: 0.2, ease: "easeInOut" }}
            >
              <div className="text-brand flex h-full w-full items-center justify-center text-sm opacity-90">
                {feedback.option.text}
              </div>
              <X
                className="cursor-pointer opacity-60"
                size={16}
                onClick={onRemoveFeedback}
              />
            </motion.div>
          )}
          {isEnhanceAnimating && (
            <motion.div
              className="pointer-events-none absolute inset-0 z-20"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="relative h-full w-full">
                <motion.div
                  className="absolute inset-0 rounded-[24px] bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-blue-500/10"
                  animate={{
                    background: [
                      "linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1))",
                      "linear-gradient(225deg, rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1))",
                      "linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1))",
                    ],
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                />
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute h-2 w-2 rounded-full bg-blue-400"
                    style={{
                      left: `${20 + i * 12}%`,
                      top: `${30 + (i % 2) * 40}%`,
                    }}
                    animate={{
                      y: [-10, -20, -10],
                      opacity: [0, 1, 0],
                      scale: [0.5, 1, 0.5],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      delay: i * 0.2,
                    }}
                  />
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        <textarea
          ref={inputRef}
          className={cn(
            "h-24 p-[16px] pb-0 w-full min-w-0 resize-none border-none outline-none bg-transparent overflow-hidden",
            isEnhanceAnimating && "transition-all duration-500",
          )}
          placeholder="Type your message..."
          value={currentPrompt}
          onChange={(e) => setCurrentPrompt(e.target.value)}
          onKeyDown={handleKeyDown}
          onClick={handleInputClick}
        />
      </div>
      <div className="flex items-center p-[16px] pt-[8px] min-w-0 overflow-hidden">
        <div className="flex flex-1 gap-2 items-center min-w-0">
          <div className="relative">
            <Button
              ref={addKnowledgeButtonRef}
              variant="ghost"
              size="icon"
              className={cn(
                "w-[32px] h-[32px] rounded-lg bg-[#F7F7FC] hover:bg-[#EAEAF2]",
                showKnowledgePanel && "!border-brand !text-brand"
              )}
              onClick={handleAddKnowledge}
            >
              <Plus className="h-4 w-4 text-[#343A3E]" />
            </Button>
            <AnimatePresence>
              {showKnowledgePanel && (
                <motion.div
                  className="absolute bottom-full mb-2 left-0 z-50"
                  initial={{ opacity: 0, scale: 0.95, y: 10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: 10 }}
                  transition={{ duration: 0.15, ease: "easeOut" }}
                >
                  <div 
                    className="relative flex flex-col p-1 overflow-auto rounded-xl border bg-white shadow-[0px_10px_10px_-5px_rgba(0,0,0,0.04),0px_20px_25px_-5px_rgba(0,0,0,0.04)] min-w-48 max-w-xs max-h-72"
                    style={{ borderColor: '#E5E5EF' }}
                    onMouseLeave={() => setSelectedKnowledgeIndex(-1)}
                  >
                    {!showResourcesList ? (
                      <div className="flex flex-col gap-1">
                        <button
                          className={cn(
                            "flex items-center gap-2 px-2 py-2 rounded-lg text-sm text-left w-full transition-colors",
                            "hover:bg-[#F7F7FC] focus:bg-[#F7F7FC] focus:outline-none",
                            selectedKnowledgeIndex === 0 && "bg-[#F7F7FC]"
                          )}
                          onClick={handleUploadFiles}
                          onMouseEnter={() => setSelectedKnowledgeIndex(0)}
                          style={{ color: '#343A3E' }}
                        >
                          <Paperclip className="h-4 w-4 shrink-0" />
                          <span className="font-normal">Upload files</span>
                        </button>
                        <button
                          className={cn(
                            "flex items-center gap-2 px-2 py-2 rounded-lg text-sm text-left w-full transition-colors",
                            "hover:bg-[#F7F7FC] focus:bg-[#F7F7FC] focus:outline-none",
                            selectedKnowledgeIndex === 1 && "bg-[#F7F7FC]"
                          )}
                          onClick={handleChooseKnowledge}
                          onMouseEnter={() => setSelectedKnowledgeIndex(1)}
                          style={{ color: '#343A3E' }}
                        >
                          <BookOpen className="h-4 w-4 shrink-0" />
                          <span className="font-normal flex-1">Choose knowledge</span>
                          <ChevronRight className="h-4 w-4 shrink-0" />
                        </button>
                      </div>
                    ) : (
                      <div className="flex flex-col gap-1">
                        {loadingResources ? (
                          <div className="flex items-center justify-center p-4 text-gray-500">
                            Loading...
                          </div>
                        ) : knowledgeResources.length > 0 ? (
                          knowledgeResources.map((item, index) => (
                            <button
                              key={index}
                              className={cn(
                                "flex items-center gap-2 px-2 py-2 rounded-lg text-sm text-left w-full transition-colors",
                                "hover:bg-[#F7F7FC] focus:bg-[#F7F7FC] focus:outline-none",
                                selectedKnowledgeIndex === index && "bg-[#F7F7FC]"
                              )}
                              onClick={() => handleSelectKnowledge({
                                id: item.uri,
                                label: item.title
                              })}
                              onMouseEnter={() => setSelectedKnowledgeIndex(index)}
                              style={{ color: '#343A3E' }}
                            >
                              <span className="font-normal truncate">{item.title}</span>
                            </button>
                          ))
                        ) : (
                          <div className="flex items-center justify-center p-4 text-gray-500">
                            No knowledge resources available
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          <div className="relative h-[32px]">
            {selectedTools.length > 0 ? (
              <div className="h-[32px] flex items-center gap-[6px] bg-[#EAEAF2] rounded-lg px-[6px] py-[8px]" onClick={handleAddTools}>
                {selectedTools.slice(0, 3).map((tool) => (
                  <img key={tool} src={`/images/${tool}-icon.svg`} alt="" className="w-[16px] h-[16px]"/>
                ))}
                {selectedTools.length > 3 && <div className="text-xs text-[#9099B1]">+{selectedTools.length - 3}</div>}
              </div>
            ) : (
              <Button
                ref={addToolsButtonRef}
                variant="ghost"
                size="icon"
                onClick={handleAddTools}
                className="w-[32px] h-[32px]"
              >
                <img src="/images/tool-icon.svg" alt="" className="w-[32px] h-[32px]"/>
              </Button>
            )}
            <AnimatePresence>
              {showToolsPanel && (
                <motion.div
                  className="absolute bottom-full mb-2 left-0 z-50"
                  initial={{ opacity: 0, scale: 0.95, y: 10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: 10 }}
                  transition={{ duration: 0.15, ease: "easeOut" }}
                >
                  <div className="bg-white border-[#EAEAF2] relative flex flex-col overflow-auto rounded-xl border py-[8px] px-[4px] shadow-[0px_20px_25px_-5px_rgba(0,0,0,0.04),_0px_10px_10px_-5px_rgba(0,0,0,0.04)] w-full max-w-64">
                    <div className="font-semibold text-sm text-[#343A3E] p-[8px]">Add tools</div>
                    <ul className="flex flex-col">
                      {tools.map((tool) => (
                        <li 
                          key={tool.key} 
                          className="flex items-center gap-[8px] p-[8px] hover:bg-[#F7F7FC] rounded-lg cursor-pointer"
                          onClick={() => {
                            const isSelected = selectedTools.includes(tool.key);
                            if (isSelected) {
                              setSelectedTools(selectedTools.filter((t) => t !== tool.key));
                            } else {
                              setSelectedTools([...selectedTools, tool.key]);
                            }
                          }}
                        >
                          <span className="flex items-center justify-center">{tool.icon}</span>
                          <span className="flex-1 text-sm text-[#343A3E]">{tool.label}</span>
                          <Checkbox
                            checked={selectedTools.includes(tool.key)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedTools([...selectedTools, tool.key]);
                              } else {
                                setSelectedTools(selectedTools.filter((t) => t !== tool.key));
                              }
                            }}
                            className="w-[16px] h-[16px] data-[state=checked]:bg-[#5C62FF] data-[state=checked]:border-[#5C62FF]"
                          />
                        </li>
                      ))}
                    </ul>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
        <div className="flex shrink-0 items-center gap-2 min-w-0">
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "h-[32px]",
              "hover:bg-accent",
              isEnhancing && "animate-pulse",
            )}
            onClick={handleEnhancePrompt}
            disabled={isEnhancing || currentPrompt.trim() === ""}
          >
            {isEnhancing ? (
              <div className="flex h-10 w-10 items-center justify-center">
                <div className="bg-foreground h-3 w-3 animate-bounce rounded-full opacity-70" />
              </div>
            ) : (
              <MagicWandIcon className="text-brand" />
            )}
          </Button>
          <div className="relative">
            <Button
              variant="ghost"
              className="h-[32px] border-none flex items-center justify-between p-[6px] text-[#343A3E] text-sm"
              onClick={() => setModelMenuOpen((v) => !v)}
            >
              {selectedModel === "4o" ? "ChatGPT 4o" : "ChatGPT nano"}
              <svg className="w-4 h-4" viewBox="0 0 20 20" fill="none"><path d="M6 8l4 4 4-4" stroke="#333" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
            </Button>
            <AnimatePresence>
              {modelMenuOpen && (
                <motion.div
                  className="absolute left-0 bottom-full mb-2 w-full max-w-44 rounded-xl border bg-white border-[#EAEAF2] shadow-[0px_20px_25px_-5px_rgba(0,0,0,0.04),_0px_10px_10px_-5px_rgba(0,0,0,0.04)] z-50 p-[4px] text-sm text-[#343A3E]"
                  initial={{ opacity: 0, scale: 0.95, y: 10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: 10 }}
                  transition={{ duration: 0.15, ease: "easeOut" }}
                >
                  {[
                    { key: "4o", label: "ChatGPT 4o" },
                    { key: "nano", label: "ChatGPT nano" }
                  ].map((item) => (
                    <div
                      key={item.key}
                      className={cn(
                        "flex items-center p-[8px] cursor-pointer rounded-lg",
                        hoveredModel === item.key && "bg-[#F7F7FC]"
                      )}
                      onMouseEnter={() => setHoveredModel(item.key as "4o" | "nano")}
                      onMouseLeave={() => setHoveredModel(null)}
                      onClick={() => {
                        setSelectedModel(item.key as "4o" | "nano");
                        setModelMenuOpen(false);
                      }}
                    >
                      <span className="flex-1">{item.label}</span>
                      {selectedModel === item.key && (
                        <svg className="w-5 h-5 text-[#5C62FF]" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" strokeWidth="2" d="M5 13l4 4L19 7"/></svg>
                      )}
                    </div>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="w-[32px] h-[32px] border-none"
            onClick={() => handleSendMessage(currentPrompt)}
          >
            {responding || !currentPrompt.trim() ? (
              <img src="/images/send-icon.svg" alt="" className="w-[32px] h-[32px]"/>
            ) : (
              <img src="/images/send-icon-active.svg" alt="" className="w-[32px] h-[32px]"/>
            )}
          </Button>
        </div>
      </div>
      
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="*/*"
          className="hidden"
          onChange={handleFileChange}
        />
      </div>
      
      {hasFiles && (
        <UploadedFiles
          ref={uploadedFilesRef}
          files={uploadedFiles}
          knowledgeFiles={knowledgeFiles}
          onFilesChange={onUploadedFilesChange || (() => {})}
          onKnowledgeFilesChange={onKnowledgeFilesChange || (() => {})}
        />
      )}
    </>
  );
});

AgentInputBox.displayName = "AgentInputBox";
