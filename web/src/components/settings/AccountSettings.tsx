'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useAuthStore } from '@/lib/stores';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { UpdatePasswordForm } from './UpdatePasswordForm';
import { ResetPasswordModal } from './ResetPasswordModal';
import { DeleteAccountModal } from './DeleteAccountModal';
import { generateInitialsImage } from '@/lib/utils';
import { updateUserProfile, getGravatarUrl, getSessionUser } from '@/lib/api/auth';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface AccountSettingsProps {
  saveSettings: (settings: any) => void;
  onSave?: () => void;
}

export const AccountSettings: React.FC<AccountSettingsProps> = ({
  saveSettings,
  onSave
}) => {
  const { user, token, setUser } = useAuthStore();
  
  const [name, setName] = useState(user?.name || '');
  const [profileImageUrl, setProfileImageUrl] = useState(user?.profile_image_url || '');
  const [isLoading, setIsLoading] = useState(false);
  const [showResetPasswordModal, setShowResetPasswordModal] = useState(false);
  const [showDeleteAccountModal, setShowDeleteAccountModal] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (user) {
      setName(user.name);
      setProfileImageUrl(user.profile_image_url || '');
    }
  }, [user]);


  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const imageUrl = event.target?.result as string;
        setProfileImageUrl(imageUrl);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUseInitials = () => {
    const initialsImage = generateInitialsImage(name);
    setProfileImageUrl(initialsImage);
  };

  const handleUseGravatar = async () => {
    if (token && user?.email) {
      try {
        const gravatarUrl = await getGravatarUrl(token, user.email);
        setProfileImageUrl(gravatarUrl);
      } catch (error) {
        toast.error('Failed to load Gravatar');
      }
    }
  };


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!token) return;

    setIsLoading(true);
    try {
      // Update profile image if name changed and using initials
      let finalProfileImageUrl = profileImageUrl;
      if (name !== user?.name) {
        if (profileImageUrl === generateInitialsImage(user?.name || '') || profileImageUrl === '') {
          finalProfileImageUrl = generateInitialsImage(name);
        }
      }

      // Update user profile
      const updatedUser = await updateUserProfile(token, name, finalProfileImageUrl);
      
      if (updatedUser) {
        try {
          // Get fresh session user data (like the original ifm project)
          const sessionUser = await getSessionUser(token);
          setUser(sessionUser);
        } catch (sessionError) {
          console.warn('Failed to refresh session user data, using updated profile data:', sessionError);
          // Fall back to using the updated user data from the profile update
          setUser(updatedUser);
        }
        toast.success('Profile updated successfully');
        onSave?.();
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to update profile');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="space-y-6">
        {/* User Avatar */}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="flex items-center gap-[16px]">
            {/* Name Field */}
          <div className="flex w-[64px] h-[64px]">
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="relative rounded-full hover:opacity-80 transition-opacity"
            >
              <img
                src={profileImageUrl && profileImageUrl !== '/user.png' ? profileImageUrl : generateInitialsImage(name)}
                alt="profile"
                className="rounded-full size-20 object-cover w-[64px] h-[64px]"
              />
            </button>
            {/* <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
            /> */}
          </div>
          <div className="space-y-2">
            <label className="text-sm text-[#8384A3]">Name</label>
            <Input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              placeholder="Enter your name"
              className="border border-[#E9E9EA] rounded-lg !focus-visible:outline-none"
            />
          </div>
          </div>
          <div className="flex gap-2 text-sm">
            <Button
              type="button"
              onClick={handleUseInitials}
              variant="secondary"
              size="sm"
            >
              Use Initials
            </Button>
            <Button
              type="button"
              onClick={handleUseGravatar}
              variant="secondary"
              size="sm"
            >
              Use Gravatar
            </Button>
          </div>

          {/* Email Display */}
          <div className="space-y-2">
            <label className="text-sm text-[#2E2F41]">Email</label>
            <div className="text-sm text-[#8384A3] mt-[4px]">
              {user?.email || 'No email available'}
            </div>
          </div>

          {/* Change Password */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-[#2E2F41]">Change password</span>
            <Button
              type="button"
              onClick={() => setShowResetPasswordModal(true)}
              variant="outline"
              size="sm"
            >
              Change
            </Button>
          </div>

          {/* Delete Account */}
          {/* <div className="flex items-center justify-between">
            <span className="text-sm text-[#2E2F41]">Delete account</span>
            <button
              type="button"
              onClick={() => setShowDeleteAccountModal(true)}
              className="px-4 py-2 text-sm border border-red-300 !text-red-600 rounded-md hover:bg-red-50 transition-colors"
            >
              Delete
            </button>
          </div> */}

          {/* Profile Image Options */}
          

          {/* Submit Button */}
          <div className="flex justify-end mt-[48px]">
            <Button
              type="submit"
              disabled={isLoading}
              className="!text-[#625DEC] bg-[#E8E9FF] hover:bg-[#DDE0FF]"
            >
              {isLoading ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </form>
      </div>

      {/* Modals */}
      <ResetPasswordModal
        show={showResetPasswordModal}
        onClose={() => setShowResetPasswordModal(false)}
      />
      
      <DeleteAccountModal
        show={showDeleteAccountModal}
        onClose={() => setShowDeleteAccountModal(false)}
      />
    </>
  );
};
