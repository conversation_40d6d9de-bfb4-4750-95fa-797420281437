'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/lib/stores';
import { useUIStore } from '@/lib/stores';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { SettingsModal } from '@/components/settings/SettingsModal';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  User,
  Settings,
  LogOut,
  Archive,
  Shield
} from 'lucide-react';
import { SettingsTab } from '@/components/settings/SettingsModal';

interface UserMenuProps {
  className?: string;
  onOpenSettings?: () => void;
  onOpenArchivedChats?: () => void;
}

// 将 UserAvatar 提取到组件外部，避免重复渲染
const UserAvatar: React.FC<{ user?: any; className?: string }> = React.memo(({ user, className }) => {
  return (
    <div className={cn("w-[32px] h-[32px] rounded-full bg-[#E8E9FF] flex items-center justify-center overflow-hidden", className)}>
      {user?.profile_image_url && user?.profile_image_url !== '/user.png' ? (
        <img 
          src={user.profile_image_url} 
          alt={user.name || 'User'} 
          className={cn("w-full h-full object-cover")}
        />
      ) : (
        <span className="text-sm font-medium text-[#625DEC]">
          {user?.name?.charAt(0)?.toUpperCase() || 'U'}
        </span>
      )}
    </div>
  );
});

UserAvatar.displayName = 'UserAvatar';

export const UserMenu: React.FC<UserMenuProps> = ({
  className,
  onOpenSettings,
  onOpenArchivedChats
}) => {
  const router = useRouter();
  const { user, token, isAuthenticated } = useAuthStore();
  const { sidebarOpen, setSidebarOpen } = useUIStore();
  const { refreshSession } = useAuth();
  const [activeUsers, setActiveUsers] = useState<number>(0); // This would come from a real-time store
  const [showSettings, setShowSettings] = useState(false);
  const [tab, setTab] = useState<SettingsTab>('general');

  // Auto-refresh session if we have token but no user
  useEffect(() => {
    if (token && isAuthenticated && !user) {
      console.log('🔄 Token exists but no user info, attempting to refresh session...');
      refreshSession().catch(error => {
        console.error('❌ Failed to refresh session:', error);
      });
    }
  }, [token, isAuthenticated, user, refreshSession]);


  const isAdmin = user?.role === 'admin';

  const handleSignOut = async () => {
    try {
      // Clear all auth-related data
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      
      // Clear any other stored data if needed
      sessionStorage.clear();
      
      // Force page reload to clear app state
      window.location.href = '/auth';
    } catch (error) {
      console.error('Failed to sign out:', error);
      // Fallback to router navigation
      router.push('/auth');
    }
  };

  const handleOpenSettings = () => {
    if (onOpenSettings) {
      onOpenSettings();
    } else {
      // Show settings modal
      setShowSettings(true);
    }
    
    // Close sidebar on mobile
    if (window.innerWidth < 768) {
      setSidebarOpen(false);
    }
  };

  const handleOpenArchivedChats = () => {
    if (onOpenArchivedChats) {
      onOpenArchivedChats();
    } else {
      // Navigate to archived chats page
      router.push('/archived');
    }
    
    // Close sidebar on mobile
    if (window.innerWidth < 768) {
      setSidebarOpen(false);
    }
  };

  const handleNavigate = (path: string) => {
    router.push(path);
    
    // Close sidebar on mobile
    if (window.innerWidth < 768) {
      setSidebarOpen(false);
    }
  };


  return (
    <>
      <div className={cn("flex items-center justify-between", className)}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <div
              className="flex items-center gap-2 w-full justify-start p-0 cursor-pointer"
            >
              <UserAvatar user={user} />
              
              {/* <div className="flex-1 min-w-0 text-left">
                <div className="text-sm font-medium truncate">
                  {user?.name || user?.email || 'User'}
                </div>
                {user?.role && (
                  <div className="text-xs text-gray-500  truncate">
                    {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                  </div>
                )}
              </div> */}
              
              {/* <ChevronDown className="w-4 h-4 text-gray-500 " /> */}
            </div>
          </DropdownMenuTrigger>
          
          <DropdownMenuContent align="start" className="w-[180px] bg-white text-[#2E2F41] text-sm">
            <DropdownMenuItem 
              onClick={() => {
                setTab('account');
                handleOpenSettings();
              }}
              className="hover:bg-[#F7F8FA] cursor-pointer flex items-center gap-[8px] h-[44px]"
            >
            <UserAvatar user={user} className="w-[28px] h-[28px]"/>
            <span className="font-medium text-[#2E2F41] text-[#8384A3]">
              {user?.name || 'User'}
            </span>            
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => {
                setTab('general');
                handleOpenSettings();
              }}
              className="hover:bg-[#F7F8FA] cursor-pointer h-[37px] flex items-center gap-[8px]"
            >
              <Settings className="w-[16px] h-[16px]"/>
              Settings
            </DropdownMenuItem>
            
            {/* <DropdownMenuItem 
              onClick={handleOpenArchivedChats}
              className="hover:bg-gray-100 cursor-pointer"
            >
              <Archive className="w-4 h-4 mr-2" />
              Archived Chats
            </DropdownMenuItem> */}
            
            {isAdmin && (
              <DropdownMenuItem
                onClick={() => handleNavigate('/admin')}
                className="hover:bg-[#F7F8FA] cursor-pointer h-[37px] flex items-center gap-[8px]"
              >
                <Shield className="w-[16px] h-[16px]"/>
                Admin Panel
              </DropdownMenuItem>
            )}
            
            {/* <DropdownMenuSeparator /> */}
            
            <DropdownMenuItem 
              onClick={handleSignOut} 
              className="hover:bg-[#F7F8FA] cursor-pointer h-[37px] flex items-center gap-[8px]"
            >
              <LogOut className="w-[16px] h-[16px]"/>
              Log Out
            </DropdownMenuItem>
            
            {activeUsers > 0 && (
              <>
                {/* <DropdownMenuSeparator /> */}
                
                <div className="flex items-center gap-2 px-2 py-1.5 text-xs text-[#8384A3]">
                  <div className="relative flex h-2 w-2">
                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75" />
                    <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500" />
                  </div>
                  <div>
                    <span>Active Users: </span>
                    <span className="font-semibold">{activeUsers}</span>
                  </div>
                </div>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      {/* Settings Modal */}
      <SettingsModal 
        show={showSettings}
        selectedTab={tab}
        onClose={() => setShowSettings(false)} 
      />
    </>
  );
};
