import { WEBUI_API_BASE_URL } from '@/lib/constants';
import type { Chat, Message, PaginatedResponse } from '@/lib/types';

// Create new chat
export const createNewChat = async (token: string, chat: object): Promise<Chat> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/new`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify({ chat })
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to create new chat:', error);
    throw error;
  }
};

// Create new chat and refresh chat list
export const createNewChatAndRefresh = async (token: string, chat: object): Promise<{ newChat: Chat; chatList: PaginatedResponse<Chat> }> => {
  try {
    // First, create the new chat
    const newChat = await createNewChat(token, chat);
    
    // Then, immediately fetch the updated chat list
    const chatList = await getChatList(token, 1);
    
    return { newChat, chatList };
  } catch (error) {
    console.error('Failed to create new chat and refresh list:', error);
    throw error;
  }
};

// Create new chat, refresh list, and update chat content (complete flow like src project)
export const createNewChatWithFullFlow = async (
  token: string, 
  chatData: object,
  updateData?: object
): Promise<{ newChat: Chat; chatList: PaginatedResponse<Chat>; updatedChat?: Chat }> => {
  try {
    // Step 1: Create the new chat
    const newChat = await createNewChat(token, chatData);
    
    // Step 2: Immediately fetch the updated chat list  
    const chatList = await getChatList(token, 1);
    
    // Step 3: If we have update data, update the chat content
    let updatedChat = newChat;
    if (updateData && newChat?.id) {
      updatedChat = await updateChatById(token, newChat.id, updateData);
    }
    
    return { newChat, chatList, updatedChat };
  } catch (error) {
    console.error('Failed to create new chat with full flow:', error);
    throw error;
  }
};

// Import chat
export const importChat = async (
  token: string,
  chat: object,
  meta: object | null,
  pinned?: boolean,
  folderId?: string | null
): Promise<Chat> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/import`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify({
        chat,
        meta: meta ?? {},
        pinned,
        folder_id: folderId
      })
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to import chat:', error);
    throw error;
  }
};

// Get chat list
export const getChatList = async (
  token: string = '',
  page: number | null = null
): Promise<PaginatedResponse<Chat>> => {
  try {
    const searchParams = new URLSearchParams();
    if (page !== null) {
      searchParams.append('page', `${page}`);
    }

    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/?${searchParams.toString()}`, {
      method: 'GET',
      credentials: 'include', // Include cookies for authentication
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        ...(token && { authorization: `Bearer ${token}` })
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    const chats = await response.json();

    // Backend returns array directly, wrap it in expected format
    return {
      data: chats,
      total: chats.length,
      page: page || 1,
      limit: 60,
      has_more: false // Since we don't have pagination info from backend
    };
  } catch (error) {
    console.error('Failed to get chat list:', error);
    throw error;
  }
};

// Helper function to validate chat ID format
const isValidChatId = (id: string): boolean => {
  if (!id || id === 'new' || id === 'local') return false;
  // Accept both UUID format (8-4-4-4-12) and other valid ID formats
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  const otherIdRegex = /^[a-zA-Z0-9_-]+$/; // Accept alphanumeric with dash and underscore
  return uuidRegex.test(id) || (otherIdRegex.test(id) && id.length > 5);
};

// Get chat by ID
export const getChatById = async (token: string, chatId: string): Promise<Chat> => {
  if (!chatId || chatId.trim() === '') {
    throw new Error('Chat ID is required');
  }

  if (!isValidChatId(chatId)) {
    throw new Error(`Invalid chat ID format: ${chatId}`);
  }

  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}`, {
      method: 'GET',
      credentials: 'include', // Include cookies for authentication
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    const chatData = await response.json();
    return chatData;
  } catch (error) {
    console.error('Failed to get chat by ID:', chatId, error);
    throw error;
  }
};

// Update chat by ID
export const updateChatById = async (
  token: string,
  chatId: string,
  chat: Partial<Chat>
): Promise<Chat> => {
  if (!isValidChatId(chatId)) {
    throw new Error(`Invalid chat ID format: ${chatId}`);
  }

  try {

    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify({ chat })
    });

    if (!response.ok) {
      throw await response.json();
    }

    const updatedChat = await response.json();
    return updatedChat;
  } catch (error) {
    console.error('Failed to update chat:', error);
    throw error;
  }
};

// Delete chat by ID
export const deleteChatById = async (token: string, chatId: string): Promise<void> => {
  if (!isValidChatId(chatId)) {
    throw new Error(`Invalid chat ID format: ${chatId}`);
  }

  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}`, {
      method: 'DELETE',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to delete chat:', error);
    throw error;
  }
};

// Get chat by share ID
export const getChatByShareId = async (shareId: string): Promise<Chat> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/share/${shareId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get chat by share ID:', error);
    throw error;
  }
};

// Clone shared chat by ID
export const cloneSharedChatById = async (
  token: string,
  shareId: string
): Promise<Chat> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/share/${shareId}/clone`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to clone shared chat:', error);
    throw error;
  }
};

// Get all tags
export const getAllTags = async (token: string): Promise<string[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/all/tags`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    const result = await response.json();
    return result.tags || [];
  } catch (error) {
    console.error('Failed to get all tags:', error);
    throw error;
  }
};

// Get tags by chat ID
export const getTagsById = async (token: string, chatId: string): Promise<string[]> => {
  if (!isValidChatId(chatId)) {
    throw new Error(`Invalid chat ID format: ${chatId}`);
  }

  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}/tags`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    const result = await response.json();
    return result.tags || [];
  } catch (error) {
    console.error('Failed to get tags by ID:', error);
    throw error;
  }
};

// Add tag by ID
export const addTagById = async (
  token: string,
  chatId: string,
  tagName: string
): Promise<void> => {
  if (!isValidChatId(chatId)) {
    throw new Error(`Invalid chat ID format: ${chatId}`);
  }

  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}/tags`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify({ tag_name: tagName })
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to add tag:', error);
    throw error;
  }
};

// Delete tag by ID
export const deleteTagById = async (
  token: string,
  chatId: string,
  tagName: string
): Promise<void> => {
  if (!isValidChatId(chatId)) {
    throw new Error(`Invalid chat ID format: ${chatId}`);
  }

  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}/tags`, {
      method: 'DELETE',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify({ tag_name: tagName })
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to delete tag:', error);
    throw error;
  }
};

// Delete tags by ID
export const deleteTagsById = async (
  token: string,
  chatId: string
): Promise<void> => {
  if (!isValidChatId(chatId)) {
    throw new Error(`Invalid chat ID format: ${chatId}`);
  }

  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}/tags/all`, {
      method: 'DELETE',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to delete all tags:', error);
    throw error;
  }
};

// Get chat list by user ID
export const getChatListByUserId = async (token: string = '', userId: string): Promise<Chat[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/list/user/${userId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        ...(token && { authorization: `Bearer ${token}` })
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get chat list by user ID:', error);
    throw error;
  }
};

// Get archived chat list
export const getArchivedChatList = async (token: string = ''): Promise<Chat[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/archived`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        ...(token && { authorization: `Bearer ${token}` })
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get archived chat list:', error);
    throw error;
  }
};

// Get all chats
export const getAllChats = async (token: string): Promise<Chat[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/all`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get all chats:', error);
    throw error;
  }
};

// Helper function to get time range for chat - matching ifm-chat-feature-k2 behavior
const getTimeRange = (timestamp: number): string => {
  const date = new Date(timestamp * 1000);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) return 'Today';
  if (diffDays === 2) return 'Yesterday';
  if (diffDays <= 7) return 'This week';
  if (diffDays <= 30) return 'This month';
  return 'Older';
};

// Get chat list by search text - enhanced to match ifm-chat-feature-k2 implementation
export const getChatListBySearchText = async (
  token: string,
  text: string,
  page: number = 1
): Promise<Chat[]> => {
  try {
    const searchParams = new URLSearchParams();
    searchParams.append('text', text);
    searchParams.append('page', `${page}`);

    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/search?${searchParams.toString()}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        ...(token && { authorization: `Bearer ${token}` })
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    const chats = await response.json();
    
    // Add time_range to each chat for better categorization - matching ifm behavior
    return chats.map((chat: Chat) => ({
      ...chat,
      time_range: getTimeRange(chat.updated_at)
    }));
  } catch (error) {
    console.error('Failed to search chats:', error);
    throw error;
  }
};

// Get chats by folder ID
export const getChatsByFolderId = async (token: string, folderId: string): Promise<Chat[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/folder/${folderId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get chats by folder ID:', error);
    throw error;
  }
};

// Get all archived chats
export const getAllArchivedChats = async (token: string): Promise<Chat[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/all/archived`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get all archived chats:', error);
    throw error;
  }
};

// Get all user chats
export const getAllUserChats = async (token: string): Promise<Chat[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/all/db`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get all user chats:', error);
    throw error;
  }
};

// Get pinned chat list
export const getPinnedChatList = async (token: string = ''): Promise<Chat[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/pinned`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        ...(token && { authorization: `Bearer ${token}` })
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get pinned chat list:', error);
    throw error;
  }
};

// Get chat list by tag name
export const getChatListByTagName = async (token: string = '', tagName: string): Promise<Chat[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/tags`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        ...(token && { authorization: `Bearer ${token}` })
      },
      body: JSON.stringify({ name: tagName })
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get chat list by tag name:', error);
    throw error;
  }
};

// Get chat pinned status by ID
export const getChatPinnedStatusById = async (token: string, chatId: string): Promise<boolean> => {
  if (!isValidChatId(chatId)) {
    throw new Error(`Invalid chat ID format: ${chatId}`);
  }

  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}/pinned`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    const result = await response.json();
    return result.pinned || false;
  } catch (error) {
    console.error('Failed to get chat pinned status:', error);
    throw error;
  }
};

// Toggle chat pinned status by ID
export const toggleChatPinnedStatusById = async (token: string, chatId: string): Promise<any> => {
  if (!isValidChatId(chatId)) {
    throw new Error(`Invalid chat ID format: ${chatId}`);
  }

  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}/pin`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to toggle chat pinned status:', error);
    throw error;
  }
};

// Clone chat by ID
export const cloneChatById = async (
  token: string,
  chatId: string,
  title?: string
): Promise<Chat> => {
  if (!isValidChatId(chatId)) {
    throw new Error(`Invalid chat ID format: ${chatId}`);
  }

  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}/clone`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify({
        ...(title && { title })
      })
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to clone chat:', error);
    throw error;
  }
};

// Share chat by ID
export const shareChatById = async (token: string, chatId: string): Promise<any> => {
  if (!isValidChatId(chatId)) {
    throw new Error(`Invalid chat ID format: ${chatId}`);
  }

  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}/share`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to share chat:', error);
    throw error;
  }
};

// Delete shared chat by ID
export const deleteSharedChatById = async (token: string, chatId: string): Promise<any> => {
  if (!isValidChatId(chatId)) {
    throw new Error(`Invalid chat ID format: ${chatId}`);
  }

  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}/share`, {
      method: 'DELETE',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to delete shared chat:', error);
    throw error;
  }
};

// Update chat folder ID by ID
export const updateChatFolderIdById = async (
  token: string,
  chatId: string,
  folderId?: string
): Promise<Chat> => {
  if (!isValidChatId(chatId)) {
    throw new Error(`Invalid chat ID format: ${chatId}`);
  }

  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}/folder`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify({ folder_id: folderId })
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update chat folder ID:', error);
    throw error;
  }
};

// Archive chat by ID
export const archiveChatById = async (token: string, chatId: string): Promise<any> => {
  if (!isValidChatId(chatId)) {
    throw new Error(`Invalid chat ID format: ${chatId}`);
  }

  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}/archive`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to archive chat:', error);
    throw error;
  }
};

// Delete all chats
export const deleteAllChats = async (token: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/`, {
      method: 'DELETE',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to delete all chats:', error);
    throw error;
  }
};

// Archive all chats
export const archiveAllChats = async (token: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/archive/all`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to archive all chats:', error);
    throw error;
  }
};
