import { WEBUI_API_BASE_URL } from '@/lib/constants';
import type { SessionUser, User } from '@/lib/types';
import { safeJsonParse, getMockDataForUrl } from '@/lib/utils/api-error-handler';

// Admin APIs
export const getAdminDetails = async (token: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/admin/details`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      credentials: 'include'
    });

    const mockData = getMockDataForUrl(response.url);
    return await safeJsonParse(response, mockData);
  } catch (error) {
    console.error('Failed to get admin details:', error);
    throw error;
  }
};

export const getAdminConfig = async (token: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/admin/config`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      credentials: 'include'
    });

    const mockData = getMockDataForUrl(response.url);
    return await safeJsonParse(response, mockData);
  } catch (error) {
    console.error('Failed to get admin config:', error);
    throw error;
  }
};

export const updateAdminConfig = async (token: string, body: object): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/admin/config`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      credentials: 'include',
      body: JSON.stringify(body)
    });

    const mockData = getMockDataForUrl(response.url);
    return await safeJsonParse(response, mockData);
  } catch (error) {
    console.error('Failed to update admin config:', error);
    throw error;
  }
};

// Session APIs
export const getSessionUser = async (token: string): Promise<SessionUser> => {
  try {
    console.log('Calling getSessionUser with URL:', `${WEBUI_API_BASE_URL}/auths/`);
    console.log('Using token:', token ? `${token.substring(0, 10)}...` : 'null');
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
    
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      credentials: 'include'
    });
    
    clearTimeout(timeoutId);
    console.log('Response received:', response.status, response.statusText);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const contentType = response.headers.get('content-type');
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      
      try {
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
          errorMessage = errorData.detail || errorData.message || errorMessage;
        } else {
          const text = await response.text();
          if (text.includes('<!doctype') || text.includes('<html')) {
            errorMessage = `Session API returned HTML instead of JSON. Status: ${response.status}. The API endpoint may not exist.`;
          } else {
            errorMessage = `Session API returned non-JSON response: ${text.substring(0, 200)}...`;
          }
        }
      } catch (parseError) {
        errorMessage = `Failed to parse session error response. Status: ${response.status}`;
      }
      
      console.error('Session API error:', errorMessage);
      throw new Error(errorMessage);
    }

    const contentType = response.headers.get('content-type');
    const responseText = await response.text();
    
    console.log('Response content-type:', contentType);
    console.log('Response text length:', responseText.length);
    console.log('Response text preview:', responseText.substring(0, 500));
    
    // Check if response is empty
    if (!responseText || responseText.trim() === '') {
      console.warn('Session API returned empty response, treating as API not available');
      throw new Error('Session API returned empty response');
    }
    
    // Check if response is JSON
    if (!contentType || !contentType.includes('application/json')) {
      console.error('Non-JSON response received:', {
        contentType,
        responsePreview: responseText.substring(0, 200)
      });
      throw new Error(`Session API returned non-JSON response: ${responseText.substring(0, 200)}...`);
    }

    try {
      const parsed = JSON.parse(responseText);
      console.log('Successfully parsed session user:', parsed);
      return parsed;
    } catch (parseError) {
      console.error('Failed to parse session response as JSON:', responseText.substring(0, 200));
      console.error('Parse error:', parseError);
      throw new Error(`Session API returned invalid JSON: ${responseText.substring(0, 200)}...`);
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error('getSessionUser request timed out');
      throw new Error('Request timed out');
    } else if (error instanceof TypeError && error.message.includes('fetch')) {
      console.error('Network error calling getSessionUser:', error);
      throw new Error('Network error: Failed to fetch');
    } else {
      console.error('Failed to get session user:', error);
      throw error;
    }
  }
};

// User sign in
export const userSignIn = async (email: string, password: string): Promise<{ token: string; user: SessionUser }> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/signin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include', // 重要：让服务器设置Cookie
      body: JSON.stringify({ email, password })
    });

    const data = await safeJsonParse(response);
    
    // 后端返回的是单一对象包含所有信息，需要重构为前端期望的格式
    // 从响应中提取token，其余字段作为user对象
    const { token, token_type, ...userData } = data;
    
    return {
      token,
      user: userData as SessionUser
    };
  } catch (error) {
    console.error('Failed to sign in:', error);
    throw error;
  }
};

// User sign up
export const userSignUp = async (
  name: string,
  email: string,
  password: string,
  profile_image_url?: string
): Promise<{ token: string; user: SessionUser }> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include', // 重要：让服务器设置Cookie
      body: JSON.stringify({
        name,
        email,
        password,
        profile_image_url
      })
    });

    const data = await safeJsonParse(response);
    
    // 后端返回的是单一对象包含所有信息，需要重构为前端期望的格式
    const { token, token_type, ...userData } = data;
    
    return {
      token,
      user: userData as SessionUser
    };
  } catch (error) {
    console.error('Failed to sign up:', error);
    throw error;
  }
};

// User sign out
export const userSignOut = async (): Promise<void> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/signout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to sign out:', error);
    throw error;
  }
};

// LDAP sign in
export const ldapUserSignIn = async (email: string, password: string): Promise<{ token: string; user: SessionUser }> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/ldap/signin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({ email, password })
    });

    const data = await safeJsonParse(response);
    
    // 后端返回的是单一对象包含所有信息，需要重构为前端期望的格式
    const { token, token_type, ...userData } = data;
    
    return {
      token,
      user: userData as SessionUser
    };
  } catch (error) {
    console.error('Failed to LDAP sign in:', error);
    throw error;
  }
};

// Update user profile
export const updateUserProfile = async (
  token: string,
  name: string,
  profile_image_url?: string
): Promise<User> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/update/profile`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      credentials: 'include',
      body: JSON.stringify({
        name,
        profile_image_url
      })
    });

    const mockData = getMockDataForUrl(response.url);
    return await safeJsonParse(response, mockData);
  } catch (error) {
    console.error('Failed to update user profile:', error);
    throw error;
  }
};

// Update user password
export const updateUserPassword = async (
  token: string,
  currentPassword: string,
  newPassword: string
): Promise<void> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/update/password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      credentials: 'include',
      body: JSON.stringify({
        password: currentPassword,
        new_password: newPassword
      })
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to update user password:', error);
    throw error;
  }
};

// Get API keys
export const getAPIKeys = async (token: string): Promise<any[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/api_keys`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      credentials: 'include'
    });

    const mockData = getMockDataForUrl(response.url);
    return await safeJsonParse(response, mockData);
  } catch (error) {
    console.error('Failed to get API keys:', error);
    throw error;
  }
};

// Create API key
export const createAPIKey = async (token: string, comment?: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/api_keys`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      credentials: 'include',
      body: JSON.stringify({ comment })
    });

    const mockData = getMockDataForUrl(response.url);
    return await safeJsonParse(response, mockData);
  } catch (error) {
    console.error('Failed to create API key:', error);
    throw error;
  }
};

// Delete API key
export const deleteAPIKey = async (token: string, keyId: string): Promise<void> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/api_keys/${keyId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      credentials: 'include'
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to delete API key:', error);
    throw error;
  }
};

// Get API key (get the first available key)
export const getAPIKey = async (token: string): Promise<string | null> => {
  try {
    const keys = await getAPIKeys(token);
    return keys.length > 0 ? keys[0].api_key : null;
  } catch (error) {
    console.error('Failed to get API key:', error);
    return null;
  }
};

// Get Gravatar URL
export const getGravatarUrl = async (token: string, email: string): Promise<string> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/utils/gravatar?email=${encodeURIComponent(email)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      credentials: 'include'
    });

    if (!response.ok) {
      throw await response.json();
    }

    const result = await response.json();
    return result.url || `https://www.gravatar.com/avatar/${btoa(email.toLowerCase())}?d=identicon`;
  } catch (error) {
    console.error('Failed to get Gravatar URL:', error);
    // Fallback to default Gravatar URL
    return `https://www.gravatar.com/avatar/${btoa(email.toLowerCase())}?d=identicon`;
  }
};
