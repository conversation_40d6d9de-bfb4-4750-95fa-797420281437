import { apiClient } from './index';

export interface EvaluationConfig {
  ENABLE_EVALUATION_ARENA_MODELS: boolean;
  EVALUATION_ARENA_MODELS: Array<{
    id: string;
    name: string;
  }>;
}

export interface FeedbackUser {
  id: string;
  name: string;
  email: string;
  role: string;
  last_active_at: number;
  updated_at: number;
  created_at: number;
}

export interface FeedbackData {
  rating?: string | number;
  model_id?: string;
  sibling_model_ids?: string[] | null;
  reason?: string;
  comment?: string;
  tags?: string[];
}

export interface FeedbackMeta {
  arena?: boolean;
  chat_id?: string;
  message_id?: string;
  tags?: string[];
}

export interface Feedback {
  id: string;
  user_id: string;
  version?: number;
  type: string;
  data: FeedbackData;
  meta?: FeedbackMeta;
  snapshot?: any;
  user?: FeedbackUser;
  updated_at: number;
  created_at: number;
}

export interface FeedbackForm {
  type: string;
  data?: FeedbackData;
  meta?: FeedbackMeta;
  snapshot?: any;
}

// Get evaluation configuration
export const getEvaluationConfig = async (token: string): Promise<EvaluationConfig> => {
  try {
    const response = await apiClient.get('/evaluations/config', token);
    return response;
  } catch (error) {
    console.error('Failed to get evaluation config:', error);
    throw error;
  }
};

// Update evaluation configuration
export const updateEvaluationConfig = async (token: string, config: Partial<EvaluationConfig>): Promise<EvaluationConfig> => {
  try {
    const response = await apiClient.post('/evaluations/config', config, token);
    return response;
  } catch (error) {
    console.error('Failed to update evaluation config:', error);
    throw error;
  }
};

// Get all feedbacks (admin only)
export const getAllFeedbacks = async (token: string): Promise<Feedback[]> => {
  try {
    const response = await apiClient.get('/evaluations/feedbacks/all', token);
    return Array.isArray(response) ? response : [];
  } catch (error) {
    console.error('Failed to get all feedbacks:', error);
    throw error;
  }
};

// Export all feedbacks (admin only)
export const exportAllFeedbacks = async (token: string): Promise<Feedback[]> => {
  try {
    const response = await apiClient.get('/evaluations/feedbacks/all/export', token);
    return Array.isArray(response) ? response : [];
  } catch (error) {
    console.error('Failed to export all feedbacks:', error);
    throw error;
  }
};

// Delete all feedbacks (admin only)
export const deleteAllFeedbacks = async (token: string): Promise<boolean> => {
  try {
    const response = await apiClient.delete('/evaluations/feedbacks/all', token);
    return response === true || response?.success === true;
  } catch (error) {
    console.error('Failed to delete all feedbacks:', error);
    throw error;
  }
};

// Get user feedbacks
export const getUserFeedbacks = async (token: string): Promise<Feedback[]> => {
  try {
    const response = await apiClient.get('/evaluations/feedbacks/user', token);
    return Array.isArray(response) ? response : [];
  } catch (error) {
    console.error('Failed to get user feedbacks:', error);
    throw error;
  }
};

// Delete user feedbacks
export const deleteUserFeedbacks = async (token: string): Promise<boolean> => {
  try {
    const response = await apiClient.delete('/evaluations/feedbacks', token);
    return response === true || response?.success === true;
  } catch (error) {
    console.error('Failed to delete user feedbacks:', error);
    throw error;
  }
};

// Create new feedback
export const createFeedback = async (token: string, feedback: FeedbackForm): Promise<Feedback> => {
  try {
    const response = await apiClient.post('/evaluations/feedback', feedback, token);
    return response;
  } catch (error) {
    console.error('Failed to create feedback:', error);
    throw error;
  }
};

// Get feedback by ID
export const getFeedbackById = async (token: string, feedbackId: string): Promise<Feedback> => {
  try {
    const response = await apiClient.get(`/evaluations/feedback/${feedbackId}`, token);
    return response;
  } catch (error) {
    console.error('Failed to get feedback by ID:', error);
    throw error;
  }
};

// Update feedback by ID
export const updateFeedbackById = async (token: string, feedbackId: string, feedback: FeedbackForm): Promise<Feedback> => {
  try {
    const response = await apiClient.post(`/evaluations/feedback/${feedbackId}`, feedback, token);
    return response;
  } catch (error) {
    console.error('Failed to update feedback:', error);
    throw error;
  }
};

// Delete feedback by ID
export const deleteFeedbackById = async (token: string, feedbackId: string): Promise<boolean> => {
  try {
    const response = await apiClient.delete(`/evaluations/feedback/${feedbackId}`, token);
    return response === true || response?.success === true;
  } catch (error) {
    console.error('Failed to delete feedback:', error);
    throw error;
  }
};
