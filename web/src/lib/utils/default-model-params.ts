import type { ModelParams } from '@/lib/types';

/**
 * Default parameters for different models
 * This serves as a fallback when models don't have parameters configured in the database
 */
export const DEFAULT_MODEL_PARAMS: Record<string, ModelParams> = {
  // OpenAI GPT models
  'gpt-4o': {
    temperature: 0.8,
    max_tokens: 2048,
    top_p: 0.95,
    frequency_penalty: 0.1,
    presence_penalty: 0.1,
    system: 'You are a creative and helpful AI assistant.'
  },
  
  'gpt-4o-mini': {
    temperature: 0.7,
    max_tokens: 1500,
    top_p: 0.9,
    frequency_penalty: 0.0,
    presence_penalty: 0.0
  },

  // O4 models (new naming convention)
  'o4-mini': {
    temperature: 0.8,
    max_tokens: 2048,
    top_p: 0.95,
    frequency_penalty: 0.1,
    presence_penalty: 0.1,
    system: 'You are a creative and helpful AI assistant.'
  },
  
  'o4': {
    temperature: 0.8,
    max_tokens: 3000,
    top_p: 0.95,
    frequency_penalty: 0.1,
    presence_penalty: 0.1,
    system: 'You are a creative and helpful AI assistant.'
  },
  
  'gpt-4': {
    temperature: 0.8,
    max_tokens: 2048,
    top_p: 0.95,
    frequency_penalty: 0.1,
    presence_penalty: 0.1
  },
  
  'gpt-4-turbo': {
    temperature: 0.8,
    max_tokens: 3000,
    top_p: 0.95,
    frequency_penalty: 0.1,
    presence_penalty: 0.1
  },
  
  'gpt-3.5-turbo': {
    temperature: 0.9,
    max_tokens: 1500,
    top_p: 0.9,
    frequency_penalty: 0.2,
    presence_penalty: 0.2
  },
  
  // O1 reasoning models
  'o1-preview': {
    max_tokens: 4000,
    reasoning_effort: 'medium',
    system: 'Think step by step and show your reasoning process.'
    // Note: O1 models don't support temperature, top_p, frequency_penalty, presence_penalty
  },
  
  'o1-mini': {
    max_tokens: 2000,
    reasoning_effort: 'low',
    system: 'Provide clear and concise reasoning.'
  },
  
  // Anthropic Claude models
  'claude-3-opus': {
    temperature: 0.7,
    max_tokens: 3000,
    top_p: 0.9,
    system: 'You are Claude, a helpful AI assistant created by Anthropic.'
  },
  
  'claude-3-sonnet': {
    temperature: 0.7,
    max_tokens: 2500,
    top_p: 0.9
  },
  
  'claude-3-haiku': {
    temperature: 0.7,
    max_tokens: 2000,
    top_p: 0.9
  },
  
  // Llama models (Ollama)
  'llama3:8b': {
    temperature: 0.6,
    max_tokens: 2000,
    top_p: 0.9,
    top_k: 40,
    repeat_penalty: 1.1,
    repeat_last_n: 64,
    num_ctx: 4096,
    system: 'You are a helpful AI assistant based on Llama 3.'
  },
  
  'llama3:70b': {
    temperature: 0.7,
    max_tokens: 3000,
    top_p: 0.9,
    top_k: 40,
    repeat_penalty: 1.05,
    repeat_last_n: 64,
    num_ctx: 8192
  },
  
  'llama2:13b': {
    temperature: 0.6,
    max_tokens: 2000,
    top_p: 0.9,
    top_k: 40,
    repeat_penalty: 1.1,
    repeat_last_n: 64,
    num_ctx: 4096
  },
  
  // Mistral models
  'mistral:7b': {
    temperature: 0.7,
    max_tokens: 2000,
    top_p: 0.9,
    top_k: 50,
    repeat_penalty: 1.1,
    num_ctx: 4096
  },
  
  // Default fallback for unknown models
  'default': {
    temperature: 0.7,
    max_tokens: 2000,
    top_p: 1.0,
    frequency_penalty: 0,
    presence_penalty: 0
  }
};

/**
 * Get default parameters for a model
 * Uses fuzzy matching to find the best match for model names
 */
export function getDefaultModelParams(modelId: string): ModelParams {
  // Direct match
  if (DEFAULT_MODEL_PARAMS[modelId]) {
    return DEFAULT_MODEL_PARAMS[modelId];
  }
  
  const lowercaseId = modelId.toLowerCase();
  
  // Fuzzy matching for common model patterns
  const fuzzyMatches = [
    { pattern: /o4-mini/, config: DEFAULT_MODEL_PARAMS['o4-mini'] },
    { pattern: /o4(?!-mini)/, config: DEFAULT_MODEL_PARAMS['o4'] },
    { pattern: /gpt-4o-mini/, config: DEFAULT_MODEL_PARAMS['gpt-4o-mini'] },
    { pattern: /gpt-4o/, config: DEFAULT_MODEL_PARAMS['gpt-4o'] },
    { pattern: /gpt-4-turbo/, config: DEFAULT_MODEL_PARAMS['gpt-4-turbo'] },
    { pattern: /gpt-4/, config: DEFAULT_MODEL_PARAMS['gpt-4'] },
    { pattern: /gpt-3\.5/, config: DEFAULT_MODEL_PARAMS['gpt-3.5-turbo'] },
    { pattern: /o1-preview/, config: DEFAULT_MODEL_PARAMS['o1-preview'] },
    { pattern: /o1-mini/, config: DEFAULT_MODEL_PARAMS['o1-mini'] },
    { pattern: /claude-3-opus/, config: DEFAULT_MODEL_PARAMS['claude-3-opus'] },
    { pattern: /claude-3-sonnet/, config: DEFAULT_MODEL_PARAMS['claude-3-sonnet'] },
    { pattern: /claude-3-haiku/, config: DEFAULT_MODEL_PARAMS['claude-3-haiku'] },
    { pattern: /llama3.*70b/, config: DEFAULT_MODEL_PARAMS['llama3:70b'] },
    { pattern: /llama3.*8b/, config: DEFAULT_MODEL_PARAMS['llama3:8b'] },
    { pattern: /llama2.*13b/, config: DEFAULT_MODEL_PARAMS['llama2:13b'] },
    { pattern: /mistral.*7b/, config: DEFAULT_MODEL_PARAMS['mistral:7b'] },
  ];
  
  // Find fuzzy match
  for (const { pattern, config } of fuzzyMatches) {
    if (pattern.test(lowercaseId)) {
      return config;
    }
  }
  
  // Return default fallback
  return DEFAULT_MODEL_PARAMS['default'];
}