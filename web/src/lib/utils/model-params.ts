import type { Model, ModelParams, UserSettings } from '@/lib/types';
import { getDefaultModelParams } from './default-model-params';

/**
 * Get merged parameters for a specific model following ifm-chat-feature-k2 approach:
 * 1. Start with global settings parameters
 * 2. Apply model-specific parameters (if any)
 * 3. Apply local/chat parameters as final override
 * 
 * @param modelId - The ID of the model to get parameters for
 * @param models - Available models array
 * @param settings - User settings containing global parameters
 * @param localParams - Local parameters that override everything else
 * @returns Merged parameters for the model
 */
export function getModelSpecificParams(
  modelId: string,
  models: Model[],
  settings?: UserSettings,
  localParams: Record<string, any> = {}
): Record<string, any> {
  // Find the specific model
  const model = models.find(m => m.id === modelId);
  
  // Build parameters in priority order (lowest to highest priority)
  const mergedParams = {
    // 1. Global settings parameters (lowest priority)
    ...(settings?.params || {}),
    
    // 2. Default model parameters (fallback if no model-specific params)
    ...(model?.info?.params ? {} : getDefaultModelParams(modelId)),
    
    // 3. Model-specific parameters (medium-high priority)
    ...(model?.info?.params || {}),
    
    // 4. Local parameters (highest priority - overrides everything)
    ...localParams,
  };

  // Handle top-level settings
  const finalParams = {
    ...mergedParams,
    // Apply top-level settings with model-specific overrides
    format: settings?.requestFormat ?? undefined,
    keep_alive: settings?.keepAlive ?? undefined,
    
    // Handle stop tokens with proper parsing
    stop: getStopTokens(
      model?.info?.params?.stop,
      settings?.params?.stop,
      localParams.stop
    ),
  };

  return finalParams;
}

/**
 * Process stop tokens with priority handling and proper parsing
 */
function getStopTokens(
  modelStop?: string | string[],
  settingsStop?: string | string[],
  localStop?: string | string[]
): string[] | undefined {
  // Priority: local -> model -> settings
  const stopSource = localStop ?? modelStop ?? settingsStop;
  
  if (!stopSource) {
    return undefined;
  }

  let stopTokens: string[];
  
  // Handle array or string input
  if (Array.isArray(stopSource)) {
    stopTokens = stopSource;
  } else {
    stopTokens = stopSource.split(',').map((token: string) => token.trim());
  }

  // Decode each token (following ifm logic)
  return stopTokens.map((str: string) => {
    try {
      return decodeURIComponent(JSON.parse('"' + str.replace(/"/g, '\\"') + '"'));
    } catch {
      return str; // Return original if parsing fails
    }
  });
}

/**
 * Get system prompt with proper priority handling
 */
export function getSystemPrompt(
  modelId: string,
  models: Model[],
  settings?: UserSettings,
  localSystem?: string
): string {
  const model = models.find(m => m.id === modelId);
  const defaultParams = getDefaultModelParams(modelId);
  
  // Priority: local -> model -> default model params -> settings -> empty
  return localSystem || 
         model?.info?.params?.system || 
         defaultParams.system ||
         settings?.system || 
         '';
}

/**
 * Check if a model supports specific parameters
 * Based on model type, capabilities, and API compatibility
 */
export function getModelCapabilities(model: Model) {
  const modelId = model.id.toLowerCase();
  
  // Determine model type for parameter compatibility
  const isOpenAI = modelId.includes('gpt') || modelId.includes('o1') || model.owned_by === 'openai';
  const isOllama = model.owned_by === 'ollama' || modelId.includes('llama');
  const isAnthropic = modelId.includes('claude') || model.owned_by === 'anthropic';
  
  // O-series models have special parameter handling
  const isReasoningModel = modelId.includes('o1') || modelId.includes('reasoning');
  
  return {
    isOpenAI,
    isOllama,
    isAnthropic,
    isReasoningModel,
    
    // Parameter support based on model type
    supportsTemperature: !isReasoningModel, // O1 models don't support temperature
    supportsMaxTokens: true,
    supportsTopP: !isReasoningModel,
    supportsFrequencyPenalty: isOpenAI,
    supportsPresencePenalty: isOpenAI,
    supportsReasoningEffort: isReasoningModel,
    supportsStop: true,
    supportsSeed: isOpenAI || isOllama,
    
    // Ollama-specific parameters
    supportsRepeatPenalty: isOllama,
    supportsTopK: isOllama,
    supportsMirostat: isOllama,
    supportsNumCtx: isOllama,
    
    // Get max tokens field name (OpenAI has different field names for different models)
    maxTokensField: isReasoningModel ? 'max_completion_tokens' : 'max_tokens',
  };
}

/**
 * Filter parameters based on model capabilities
 * Removes unsupported parameters to avoid API errors
 */
export function filterParamsForModel(
  params: Record<string, any>,
  model: Model
): Record<string, any> {
  const capabilities = getModelCapabilities(model);
  const filteredParams: Record<string, any> = {};

  // Copy supported parameters
  Object.entries(params).forEach(([key, value]) => {
    if (value === undefined || value === null) {
      return; // Skip undefined/null values
    }

    let shouldInclude = true;

    switch (key) {
      case 'temperature':
        shouldInclude = capabilities.supportsTemperature;
        break;
      case 'max_tokens':
        // Handle max_tokens vs max_completion_tokens for reasoning models
        if (capabilities.isReasoningModel) {
          filteredParams['max_completion_tokens'] = value;
          shouldInclude = false; // Don't include original max_tokens
        } else {
          shouldInclude = capabilities.supportsMaxTokens;
        }
        break;
      case 'top_p':
        shouldInclude = capabilities.supportsTopP;
        break;
      case 'frequency_penalty':
        shouldInclude = capabilities.supportsFrequencyPenalty;
        break;
      case 'presence_penalty':
        shouldInclude = capabilities.supportsPresencePenalty;
        break;
      case 'reasoning_effort':
        shouldInclude = capabilities.supportsReasoningEffort;
        break;
      case 'seed':
        shouldInclude = capabilities.supportsSeed;
        break;
      case 'repeat_penalty':
      case 'repeat_last_n':
        shouldInclude = capabilities.supportsRepeatPenalty;
        break;
      case 'top_k':
        shouldInclude = capabilities.supportsTopK;
        break;
      case 'mirostat':
      case 'mirostat_eta':
      case 'mirostat_tau':
        shouldInclude = capabilities.supportsMirostat;
        break;
      case 'num_ctx':
      case 'num_batch':
      case 'num_keep':
        shouldInclude = capabilities.supportsNumCtx;
        break;
      default:
        // Include other parameters by default
        shouldInclude = true;
    }

    if (shouldInclude) {
      filteredParams[key] = value;
    }
  });

  return filteredParams;
}